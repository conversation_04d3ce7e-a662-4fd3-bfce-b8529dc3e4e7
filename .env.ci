APP_NAME=Vorgaenge
APP_ENV=ci
APP_KEY=base64:FsyPjz56fae3K27ic4o/udEdT1gk05u5BQw64oGNmzE=
APP_DEBUG=true
APP_URL=http://vorgaenge.testing

APP_TIMEZONE=Europe/Berlin
APP_LOCALE=de
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=de_DE
APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database
BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_LEVEL=debug

# Use separate DB for testing
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=33033
DB_DATABASE=cypress
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_CONNECTION=log
CACHE_STORE=array
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

AWS_ACCESS_KEY_ID=minio
AWS_SECRET_ACCESS_KEY=minio123
AWS_DEFAULT_REGION=us-east-1
AWS_URL=http://127.0.0.1:9000
AWS_BUCKET=demv-ci-vorgaenge

REDIS_HOST=cache
REDIS_PASSWORD=null
REDIS_PORT=6379

PW_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA1EkFwMMOv1bvWGoisOE4
0HpOG8xzr4bu5dQlqLnqs7Ucx4I/ljXBm/bhNykDXeCj0Wg8a/3Q3YPVew/jVcGK
ltOhYrYB8+qxaZG4Uhfmz6yBOGNH99ks2bWrSh0/A7v1C0BD8rueU0LjJZfU0snW
KLAmBsuDtJUeNN+Qg8xBVBHdWEtpQzMLly8cSFnpkIsltrnieKERXkFdtDxJOWqB
Xs6+/Md9CCznewoo1oLCxDdiXPr4Iq2DAhp+SBLBiD1K9quCRfiUjbtQi0isVMpf
QjRuFyPMTxGPy5r3D47iU1qUVIc4049Mq7oOYf0qgEk++Z4jzJrx4Rl4MkE7z7Hl
j97gPBQNKmLziVd5uYUPBaNYa7gg3gduZbhz2dp0dWjBLyKdGuCPgyr9gPAKHNCh
qBhjcrShjjVLialyQQ9E818rm5anEapLkX+1rPaMQ6QFsb0BIW9eZ0xG7NG8J2IG
HMWJEYwBBPdkYRG6K2LxERiumcJOVMEti6u782jT2e3Owi39+LpZDq8qcAu+Q1li
PJz5c2vAJ6xbaKPlSI23btVUYVOFlBmwGE19tg2fgWt+RZLnnl1oxiMFawwlh4cB
HgctBKNiVWdco6Ld7p3YNmZP1cja1yX+e9reqTZ/k6T8bBsPS1/WlUlN1JKyipNK
YbaoA14IPWk0MJTUACEDwfECAwEAAQ==
-----END PUBLIC KEY-----"

INTERCOM_SECRET_KEY=
