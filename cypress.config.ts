// eslint-disable-next-line import/no-extraneous-dependencies
import { defineConfig } from 'cypress';
import cloudPlugin from "cypress-cloud/plugin";

export default defineConfig({
  chromeWebSecurity: false,
  viewportWidth: 1920,
  viewportHeight: 1080,
  responseTimeout: 30000,
  defaultCommandTimeout: 20000,
  video: false,
  screenshotOnRunFailure: false,
  e2e: {
    baseUrl: 'http://vorgaenge.testing',
    env: {
      command_prefix: 'docker compose exec -T php-fpm',
      command_suffix: '--env=testing',
    },
    setupNodeEvents(on, config) {
      on('before:browser:launch', (browser, launchOptions) => {
        if (browser.name === 'chrome' || browser.name === 'edge') {
          // bypass 401 unauthorised access on chromium-based browsers
          launchOptions.args.push('--disable-features=SameSiteByDefaultCookies');

          return launchOptions;
        }
      });

      return cloudPlugin(on, config);
    },
  },
});
