root = true

[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
indent_style = space
indent_size = 4
trim_trailing_whitespace = true

[*.php]
ij_php_space_after_type_cast = true
ij_php_blank_lines_before_return_statement = 1
ij_php_space_before_short_closure_left_parenthesis = true
ij_php_keep_rparen_and_lbrace_on_one_line = true

[*.md]
trim_trailing_whitespace = false

[*.{js,jsx,ts,tsx,vue,json,yml,yaml}]
indent_size = 2
max_line_length = 100
