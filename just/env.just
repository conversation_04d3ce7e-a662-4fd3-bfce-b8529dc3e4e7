# Install dependencies
[group('Env')]
env-encrypt-development:
    @just artisan env:encrypt --force -q --env=development --key=${LARAVEL_ENV_ENCRYPTION_KEY}

env-encrypt-production:
    @just artisan env:encrypt --force -q --env=production --key=${LARAVEL_ENV_ENCRYPTION_KEY}

env-encrypt-staging:
    @just artisan env:encrypt --force -q --env=staging --key=${LARAVEL_ENV_ENCRYPTION_KEY}

env-decrypt-development:
    @just artisan env:decrypt --force -q --env=development --key=${LARAVEL_ENV_ENCRYPTION_KEY}

env-decrypt-production:
    @just artisan env:decrypt --force -q --env=production --key=${LARAVEL_ENV_ENCRYPTION_KEY}

env-decrypt-staging:
    @just artisan env:decrypt --force -q --env=staging --key=${LARAVEL_ENV_ENCRYPTION_KEY}

env-decrypt-all:
    @just env-decrypt-development
    @just env-decrypt-production
    @just env-decrypt-staging

env-encrypt-all:
    @just env-encrypt-development
    @just env-encrypt-production
    @just env-encrypt-staging
