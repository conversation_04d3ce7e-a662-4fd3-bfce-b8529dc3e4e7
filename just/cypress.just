# Install cypress and set up test database
[group('Cypress')]
cypress-install:
    yarn cypress install
    @just exec -T {{mysql_service}} mysql -u"root" -p"${DB_PASSWORD}" -e "drop database if exists cypress"
    @just exec -T {{mysql_service}} mysql -u"root" -p"${DB_PASSWORD}" -e "create database cypress"
    @just exec -T {{mysql_service}} mysql -u"root" -p"${DB_PASSWORD}" -e "GRANT ALL PRIVILEGES ON cypress.* TO '${DB_USERNAME}'@'%';"

# Run cypress tests (full setup + run)
[group('Cypress')]
cypress-run:
    @just cypress-install
    @just yarn-production
    @just cypress-run-fast

# Run cypress tests (fast, assumes setup is done)
[group('Cypress')]
cypress-run-fast:
    yarn cypress run

# Run specific cypress test spec (full setup + run)
[group('Cypress')]
cypress-run-spec SPEC:
    @just cypress-install
    @just yarn-production
    @just cypress-run-spec-fast "{{SPEC}}"

# Run specific cypress test spec (fast, assumes setup is done)
[group('Cypress')]
cypress-run-spec-fast SPEC:
    yarn cypress run --spec "cypress/e2e/{{SPEC}}.cy.ts"

# Open cypress UI (full setup + open)
[group('Cypress')]
cypress-open:
    @just cypress-install
    @just yarn-production
    @just cypress-open-fast

# Open cypress UI (fast, assumes setup is done)
[group('Cypress')]
cypress-open-fast:
    yarn cypress open --e2e

# Delete cypress snapshot from database
[group('Cypress')]
cypress-delete-snapshot:
    @just artisan snapshot:delete cypress-dump
