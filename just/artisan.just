# Run migrations
[group('Database')]
migrate:
    @just artisan migrate

# Create a new migration file
[group('Database')]
make-migration *ARGS:
    @just artisan make:migration "$@"

# Run fresh migrations with seeding
[group('Database')]
migrate-fresh:
    @just artisan migrate:fresh --seed
    @just artisan vorlagen:import-standard

# Rollback the last migration
[group('Database')]
rollback-one:
    @just artisan migrate:rollback --step=1

# Show route list (excluding telescope and vapor routes)
[group('Laravel')]
route-list:
    @just artisan route:list --except-path=telescope,vapor

# Clear application cache
[group('Laravel')]
cache-clear:
    @just artisan cache:clear

# Dispatch a job
[group('Laravel')]
dispatch-job *ARGS:
    @just artisan job:dispatch "$@"
