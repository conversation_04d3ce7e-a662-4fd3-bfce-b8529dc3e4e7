# We need our own composer.just file to avoid problems with xdebug

# Run composer inside the php service container
[group('Composer')]
composer *ARGS:
    @just userexec -e COMPOSER_MEMORY_LIMIT=-1 -e XDEBUG_MODE=off {{compose_php_service}} composer "$@"

# Update the composer lock file without updating any packages
[group('Composer')]
composer-update-lock: (composer 'update' 'php')

# Install PHP dependencies
[group('Composer')]
php-install:
    @just userexec -e COMPOSER_MEMORY_LIMIT=-1 -e XDEBUG_MODE=off {{compose_php_service}} composer install

