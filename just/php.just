# PHP commands

alias setup := php-setup

# Setup PHP environment
[group('PHP')]
php-setup:
    test -e .env || cp .env.example .env
    docker compose build php-fpm # First build PHP-FPM as it creates a image used by other images
    docker compose up -d --build --force-recreate
    just php-install
    @just artisan key:generate
    just migrate-fresh

# Run PHP linting
[group('Linting')]
php-lint:
    @just userexec -e XDEBUG_MODE=off {{compose_php_service}} ./vendor/bin/phpcstd
    @just userexec -e XDEBUG_MODE=off {{compose_php_service}} ./vendor/bin/deptrac analyse --config-file=deptrac_support.yaml

# Fix PHP linting issues
[group('Linting')]
php-lint-fix:
    @just userexec -e XDEBUG_MODE=off {{compose_php_service}} ./vendor/bin/phpcstd --fix

# Run all PHP tests. Optional filtering for class (without path) or tests
[group('Testing')]
php-test filter=".\\*":
    @just userexec -e XDEBUG_MODE=off {{compose_php_service}} ./vendor/bin/phpunit --filter {{filter}}

# Run all PHP tests with xdebug enabled. Optional filtering for class (without path) or tests
[group('Testing')]
php-test-debug filter=".\\*":
    @just userexec {{compose_php_service}} ./vendor/bin/phpunit --filter {{filter}}

# Run unit tests only
[group('Testing')]
php-test-unit:
    @just userexec -e XDEBUG_MODE=off {{compose_php_service}} ./vendor/bin/phpunit --testsuite=unit

# Run unit tests with xdebug enabled
[group('Testing')]
php-test-unit-debug:
    @just userexec {{compose_php_service}} ./vendor/bin/phpunit --testsuite=unit

# Run feature tests only
[group('Testing')]
php-test-feature:
    @just userexec -e XDEBUG_MODE=off {{compose_php_service}} ./vendor/bin/phpunit --testsuite=feature

# Run feature tests with xdebug enabled
[group('Testing')]
php-test-feature-debug:
    @just userexec {{compose_php_service}} ./vendor/bin/phpunit --testsuite=feature

# Run infection testing
[group('Testing')]
php-infection:
    @just userexec -e XDEBUG_MODE=coverage {{compose_php_service}} ./vendor/bin/infection

# Watch and run tests on file changes
[group('Testing')]
php-test-watch:
    @just userexec -e XDEBUG_MODE=off {{compose_php_service}} ./vendor/bin/phpunit-watcher watch

# Generate dependency graph
[group('Testing')]
php-dependency-graph:
    @just userexec -e XDEBUG_MODE=off {{compose_php_service}} ./vendor/bin/deptrac analyze --formatter=graphviz --graphviz-dump-image=dependencies.png

# Run PHP CS with CI flag
[group('Linting')]
phpcstd:
    @just userexec -e XDEBUG_MODE=off {{compose_php_service}} ./vendor/bin/phpcstd --ci

# Run vapor command with arguments
[group('Vapor')]
vapor *args:
    @just userexec -e XDEBUG_MODE=off {{compose_php_service}} ./vendor/bin/vapor {{args}}
