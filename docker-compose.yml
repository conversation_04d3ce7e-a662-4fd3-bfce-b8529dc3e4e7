services:
  nginx:
    build: ./docker/nginx
    depends_on:
      - php-fpm
    volumes:
      - ./public:/var/www/html/public:delegated
      - ./docker/nginx/site.conf:/etc/nginx/conf.d/default.conf:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.vorgaenge-nginx.rule=Host(`vorgaenge.demv.internal`) || Host(`vorgaenge.testing`)"
      - "traefik.http.routers.vorgaenge-nginx.entrypoints=web,websecure"
      - "traefik.docker.network=local-docker-network_default"
    networks:
      - traefik
      - default
    ports:
      - "8091:80"

  php-fpm:
    container_name: vorgaenge
    build:
      context: .
      dockerfile: ./docker/php/Dockerfile
      args:
        GITHUB_TOKEN: $GITHUB_TOKEN
        GROUP_ID: $GID
        USER_ID: $UID
    environment:
      PHP_IDE_CONFIG: 'serverName=vorgaenge.demv.internal'
      XDEBUG_CONFIG: client_host=${MY_IP}
    image: demvsystems/vorgaenge-php-fpm
    volumes:
      - .:/var/www/html:delegated
      - ./docker/php/php.ini:/usr/local/etc/php/conf.d/php.ini
      - ./docker/php/docker-php-ext-xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
      - ./docker/php/fpm_additional.conf:/usr/local/etc/php-fpm.d/zzzz-fpm_additional.conf # zzzz- to be loaded last: overwrites all before
      - ../professionalworks-sdk:/var/www/professionalworks-sdk
    depends_on:
      - mysql
      - node
      - cache
    networks:
      - traefik
      - default
    extra_hosts:
      - "host.docker.internal:host-gateway"

  mysql:
    image: mysql:8.0.33
    environment:
      MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
      MYSQL_DATABASE: '${DB_DATABASE}'
      MYSQL_USER: '${DB_USERNAME}'
      MYSQL_PASSWORD: '${DB_PASSWORD}'
      MYSQL_ALLOW_EMPTY_PASSWORD: 'yes'
    volumes:
      - mysql:/var/lib/mysql
      - ./docker/provision/mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "33300:3306"
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping" ]
    command: mysqld --sql-mode=NO_ENGINE_SUBSTITUTION

  php-queue:
    build:
      context: ./docker/php-queue
      dockerfile: Dockerfile
      args:
        GITHUB_TOKEN: $GITHUB_TOKEN
        GROUP_ID: $GID
        USER_ID: $UID
    volumes:
      - .:/var/www/html:delegated
      - ./docker/php/php.ini:/usr/local/etc/php/conf.d/php.ini
    depends_on:
      - mysql
      - php-fpm
      - cache
    networks:
      - traefik
      - default

  node:
    build:
      context: ./docker/node
      dockerfile: Dockerfile
      args:
        GROUP_ID: $GID
        USER_ID: $UID
    working_dir: /home/<USER>/app
    volumes:
      - .:/home/<USER>/app
    networks:
      - default
    ports:
      - ${VITE_SERVER_PORT}:${VITE_SERVER_PORT}
    tty: true
    command: sh -c "yarn install --frozen-lockfile --non-interactive && yarn run dev"

  cache:
    image: redis:6-alpine
    ports:
      - 63790:6379
    volumes:
      - redis:/data

  openapi:
    image: redocly/redoc
    environment:
      - SPEC_URL=openapi/openapi.yml
      - PAGE_TITLE="OpenApi - Vorgänge"
    volumes:
      - ./openapi:/usr/share/nginx/html/openapi:delegated
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.vorgaenge-openapi.rule=Host(`openapi.vorgaenge.demv.internal`)"
      - "traefik.http.routers.vorgaenge-openapi.entrypoints=web,websecure"
      - "traefik.docker.network=local-docker-network_default"
    networks:
      - traefik

  # Local s3 instance using minio
  # !!! name MUST NOT BE s3 because it would then interfer with the pw s3 minio container
  vorgaenge_s3:
    image: minio/minio:RELEASE.2021-08-25T00-41-18Z
    ports:
      - ${S3_PORT}:${S3_PORT}
    volumes:
      - minio:/data
    environment:
      MINIO_ROOT_USER: $AWS_ACCESS_KEY_ID
      MINIO_ROOT_PASSWORD: $AWS_SECRET_ACCESS_KEY
    command: server /data --console-address=":9001"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.vorgaenge-s3.rule=Host(`s3.vorgaenge.demv.internal`)"
      - "traefik.http.routers.vorgaenge-s3.entrypoints=web,websecure"
      - "traefik.docker.network=local-docker-network_default"
    networks:
      - default
      - traefik

  # Run once on startup to create the necessary buckets
  vorgaenge_s3_install:
    image: minio/mc:RELEASE.2021-07-27T06-46-19Z
    depends_on:
      - vorgaenge_s3
    entrypoint: >
      /bin/sh -c "
      sleep 1;
      /usr/bin/mc alias set minio http://vorgaenge_s3:9000 $AWS_ACCESS_KEY_ID $AWS_SECRET_ACCESS_KEY;
      /usr/bin/mc mb --ignore-existing minio/demv-dev-vorgaenge;
      /usr/bin/mc rm --recursive minio/demv-testing-vorgaenge;
      /usr/bin/mc mb --ignore-existing minio/demv-testing-vorgaenge;
      exit 0;
      "
networks:
  traefik:
    external: true
    name: local-docker-network_default

volumes:
  mysql:
  redis:
  minio:
