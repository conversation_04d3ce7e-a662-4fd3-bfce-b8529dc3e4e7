# MSG-Datei <PERSON>vertierung - Debugging Guide

## Problem
Be<PERSON> von MSG-Dateien an Vorgänge tritt ein 422-<PERSON><PERSON> mit der Nachricht "Could not process email" auf.

## Implementierte Lösungen

### 1. Erweiterte Fehlerbehandlung
- **Controller**: Detaillierte Fehlerbehandlung mit spezifischen Fehlermeldungen für verschiedene Exception-Typen
- **Service**: Bessere Validierung von Dateistreams und ConvertApi-Antworten
- **Logging**: Umfassendes Logging aller Schritte der MSG-Konvertierung

### 2. ConvertApi-Konfiguration Validierung
- Überprüfung der `CONVERT_API_SECRET` Umgebungsvariable
- Validierung der ConvertApi-Antworten
- Bessere Fehlerbehandlung bei API-Fehlern

### 3. Debug-Tools

#### Artisan-Kommando
```bash
php artisan convert-api:test
```
Testet die ConvertApi-Konfiguration und Konnektivität.

#### Debug-Endpoint
```
GET /api/debug/convert-api-status
```
Überprüft die ConvertApi-Konfiguration über HTTP (authentifiziert).

## Debugging-Schritte

### 1. Umgebungsvariable prüfen
```bash
# In .env Datei
CONVERT_API_SECRET=your_secret_here

# Über Artisan
php artisan tinker
>>> config('convert_api.secret')
```

### 2. ConvertApi-Konfiguration testen
```bash
php artisan convert-api:test -v
```

### 3. Logs überwachen
```bash
# Alle MSG-bezogenen Logs
tail -f storage/logs/laravel.log | grep -i "msg\|convert"

# Nur Fehler
tail -f storage/logs/laravel.log | grep -i "error.*msg\|error.*convert"
```

### 4. Debug-Endpoint verwenden
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://your-domain/api/debug/convert-api-status
```

## Häufige Probleme und Lösungen

### Problem: "CONVERT_API_SECRET is not configured"
**Lösung**: 
1. ConvertApi-Account erstellen auf https://www.convertapi.com/
2. API-Secret aus dem Dashboard kopieren
3. In `.env` Datei eintragen: `CONVERT_API_SECRET=your_secret_here`
4. Cache leeren: `php artisan config:clear`

### Problem: "ConvertApi conversion failed"
**Mögliche Ursachen**:
- Ungültiger API-Key
- Keine Credits mehr im ConvertApi-Account
- Netzwerkprobleme
- Beschädigte MSG-Datei

**Debugging**:
1. ConvertApi-Dashboard prüfen (Credits, API-Calls)
2. Logs analysieren für detaillierte Fehlermeldungen
3. Mit kleiner Test-MSG-Datei versuchen

### Problem: "Invalid file stream"
**Mögliche Ursachen**:
- Datei nicht korrekt hochgeladen
- Speicher-/Dateisystem-Probleme
- Falsche Dateiberechtigungen

**Debugging**:
1. Datei-Upload-Prozess überprüfen
2. Dateisystem-Berechtigungen prüfen
3. Speicherplatz überprüfen

## Monitoring

### Log-Einträge überwachen
Die folgenden Log-Einträge zeigen den Konvertierungsprozess:

```
INFO: Processing MSG file
INFO: Starting MSG to MIME conversion via ConvertApi
INFO: ConvertApi metadata conversion successful
INFO: MSG conversion successful
```

### Fehler-Einträge
```
ERROR: MSG conversion failed
ERROR: ConvertApi secret is not configured
ERROR: Invalid file stream for MSG file
ERROR: ConvertApi returned null message
```

## Produktions-Hinweise

1. **Debug-Route entfernen**: Die Route `/api/debug/convert-api-status` sollte in der Produktion entfernt werden
2. **Log-Level anpassen**: In Produktion Log-Level auf WARNING oder ERROR setzen
3. **Monitoring**: ConvertApi-Credits und API-Calls überwachen
4. **Fallback**: Alternative MSG-Konvertierung implementieren für kritische Anwendungen

## Alternative Lösungsansätze

Falls ConvertApi weiterhin Probleme bereitet:

1. **PHP-MSG-Parser**: `hfig/mapi` Package verwenden
2. **Python-Integration**: `extract-msg` Python-Tool integrieren
3. **Lokale Konvertierung**: MSG-zu-EML-Konverter implementieren
4. **Fallback-Mechanismus**: Bei MSG-Fehlern alternative Verarbeitung

## Support

Bei anhaltenden Problemen:
1. Logs sammeln und analysieren
2. ConvertApi-Support kontaktieren
3. Alternative Konvertierungsmethoden evaluieren
