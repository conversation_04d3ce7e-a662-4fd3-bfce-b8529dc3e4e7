{"openapi": "3.0.0", "info": {"title": "Vorgaenge API", "description": "API for managing Vorgaenge", "contact": {"name": "Team Magma", "email": "<EMAIL>"}, "version": "1.0"}, "servers": [{"url": "vorgaenge.professional.works"}], "paths": {"/api/v1/vorgaenge/aufgabe": {"post": {"tags": ["Vorgaenge"], "summary": "<PERSON><PERSON><PERSON> einen neuen Vorgang mit der Vorgangsart aufgabe", "description": "Erst<PERSON>t einen neuen Vorgang mit der Vorgangsart aufgabe und den angegebenen Daten", "operationId": "storeAufgabeVorgang", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AufgabeVorgangStoreData"}}}}, "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"id": {"type": "integer", "example": 1}, "vorgangsnummer": {"type": "string", "example": "V-12345"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge/brief/blueprint": {"post": {"tags": ["Vorgaenge"], "description": "Erstellt einen vollständig vorausgefüllten Blueprint für einen Brief-Vorgang (ohne Persistierung)", "operationId": "blueprintBriefVorgang", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VorgangBlueprintData"}}}}, "responses": {"200": {"description": "Blueprint für Brief-Vorgang", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueprintBriefVorgang"}}}}}}}, "/api/v1/vorgaenge/brief": {"post": {"tags": ["Vorgaenge"], "description": "Erst<PERSON>t einen neuen Vorgang mit der Vorgangsart korrespondenz_brief", "operationId": "storeBriefVorgang", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BriefVorgangStoreData"}}}}, "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"id": {"type": "integer", "example": 1}, "vorgangsnummer": {"type": "string", "example": "V-12345"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge/dunkelverarbeitet": {"post": {"tags": ["Vorgaenge"], "summary": "<PERSON><PERSON><PERSON> einen neuen Vorgang mit der Vorgangsart dunkelverarbeitet", "description": "Erst<PERSON>t einen neuen Vorgang mit der Vorgangsart dunkelverarbeitet und den angegebenen Daten", "operationId": "storeDunkelverarbeitetVorgang", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DunkelverarbeitetVorgangStoreData"}}}}, "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"id": {"type": "integer", "example": 1}, "vorgangsnummer": {"type": "string", "example": "V-12345"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge/email/blueprint": {"post": {"tags": ["Vorgaenge"], "description": "Erstellt einen vollständig vorausgefüllten Blueprint für einen E-Mail-Vorgang (ohne Persistierung)", "operationId": "blueprintEmailVorgang", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VorgangBlueprintData"}}}}, "responses": {"200": {"description": "Blueprint für E-Mail-Vorgang", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueprintEmailVorgang"}}}}}}}, "/api/v1/vorgaenge/email": {"post": {"tags": ["Vorgaenge"], "description": "Erst<PERSON>t einen neuen Vorgang mit der Vorgangsart korrespondenz_email", "operationId": "storeEmailVorgang", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailVorgangStoreData"}}}}, "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"id": {"type": "integer", "example": 1}, "vorgangsnummer": {"type": "string", "example": "V-12345"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge/{vorgangid}/{elementtype}/{elementid}/content": {"get": {"tags": ["Vorgaenge"], "description": "Liefert den Gesamten Content eines Timeline Elements", "operationId": "showTimelineElementContent", "parameters": [{"name": "vorgangid", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "elementtype", "in": "path", "description": "Zulässige typen sind: korrespondenzen, erinnerungen, mahnungen", "required": true, "schema": {"type": "string"}}, {"name": "elementid", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"302": {"description": "Redirect to content"}, "400": {"description": "Invalid element type."}, "404": {"description": "Not Found - Timeline element not found"}}}}, "/api/v1/vorgaenge/{vorgang}/nachrichten": {"post": {"tags": ["Vorgaenge"], "description": "<PERSON>rstellt eine neue Nachricht (E-Mail) in einem bestehenden Vorgang", "operationId": "storeEmailElement", "parameters": [{"name": "vorgang", "in": "path", "description": "ID des Vorgangs, zu dem die Nachricht hinzugefügt werden soll", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailKorrespondenzElementStoreData"}}}}, "responses": {"201": {"description": "Successful response"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Forbidden"}}, "type": "object"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Not Found"}}, "type": "object"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge/{vorgang}/erinnerungen": {"post": {"tags": ["Vorgaenge"], "description": "Erstellt eine neue Erinnerung in einem bestehenden Vorgang", "operationId": "storeErinnerungElement", "parameters": [{"name": "vorgang", "in": "path", "description": "ID des Vorgangs, zu dem die Erinnerung hinzugefügt werden soll", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErinnerungElementStoreData"}}}}, "responses": {"201": {"description": "Successful response"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Forbidden"}}, "type": "object"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Not Found"}}, "type": "object"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge/{vorgang}/gespraechsnotizen": {"post": {"tags": ["Vorgaenge"], "description": "Erstellt eine neue Gesprächsnotiz in einem bestehenden Vorgang", "operationId": "storeGespraechsnotizElement", "parameters": [{"name": "vorgang", "in": "path", "description": "ID des Vorgangs, zu dem die Gesprächsnotiz hinzugefügt werden soll", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GespraechsnotizElementStoreData"}}}}, "responses": {"201": {"description": "Successful response"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Forbidden"}}, "type": "object"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Not Found"}}, "type": "object"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge/{vorgang}/kommentare": {"post": {"tags": ["Vorgaenge"], "description": "<PERSON><PERSON><PERSON> einen neuen Kommentar in einem Vorgang", "operationId": "storeKommentarElement", "parameters": [{"name": "vorgang", "in": "path", "description": "ID des Vorgangs, zu dem der Kommentar hinzugefügt werden soll", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KommentarElementStoreData"}}}}, "responses": {"201": {"description": "Successful response"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Forbidden"}}, "type": "object"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Not Found"}}, "type": "object"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge/{vorgang}/mahnungen": {"post": {"tags": ["Vorgaenge"], "description": "Erstellt eine neue Mahnung in einem bestehenden Vorgang", "operationId": "storeMahnungElement", "parameters": [{"name": "vorgang", "in": "path", "description": "ID des Vorgangs, zu dem die Mahnung hinzugefügt werden soll", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MahnungElementStoreData"}}}}, "responses": {"201": {"description": "Successful response"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Forbidden"}}, "type": "object"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Not Found"}}, "type": "object"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge/{vorgang}/systemkommentare": {"post": {"tags": ["Vorgaenge"], "description": "<PERSON><PERSON><PERSON> einen neuen Systemkommentar in einem Vorgang", "operationId": "storeSystemkommentarElement", "parameters": [{"name": "vorgang", "in": "path", "description": "ID des Vorgangs, zu dem der Kommentar hinzugefügt werden soll", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemkommentarElementStoreData"}}}}, "responses": {"201": {"description": "Successful response"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Forbidden"}}, "type": "object"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Not Found"}}, "type": "object"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge": {"get": {"tags": ["Vorgaenge"], "description": "Listet alle Vorgaenge mit Filtern und Paginierung", "operationId": "listVorgaenge", "parameters": [{"$ref": "#/components/parameters/kunde_id"}, {"$ref": "#/components/parameters/vertrag_ids"}, {"$ref": "#/components/parameters/gesellschaft_id"}, {"$ref": "#/components/parameters/sparte_id"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/vorgangsart"}, {"$ref": "#/components/parameters/vorgang_typ_id"}, {"$ref": "#/components/parameters/bearbeiter_id"}, {"$ref": "#/components/parameters/beobachter_id"}, {"$ref": "#/components/parameters/participant_id"}, {"$ref": "#/components/parameters/per_page"}, {"$ref": "#/components/parameters/cursor"}, {"$ref": "#/components/parameters/faelligkeit_von"}, {"$ref": "#/components/parameters/faelligkeit_bis"}], "responses": {"200": {"description": "Erfolgreiche Antwort", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Vorgang"}}, "meta": {"properties": {"next_cursor": {"type": "string", "nullable": true}, "prev_cursor": {"type": "string", "nullable": true}, "per_page": {"type": "integer"}, "total": {"type": "integer"}}, "type": "object"}}, "type": "object"}}}}, "403": {"description": "Verboten - <PERSON>cht autorisiert, diese Aktion auszuführen", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "This action is unauthorized."}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge/{id}": {"get": {"tags": ["Vorgaenge"], "summary": "<PERSON><PERSON><PERSON> einen Vorgang", "description": "Gibt die Details eines einzelnen Vorgangs zurück, einsch<PERSON>ß<PERSON> aller zugehörigen Timeline-Elemente und Verknüpfungen.", "operationId": "showVorgang", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Vorgang"}}}}, "403": {"description": "Forbidden - Unauthorized to perform this action", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "This action is unauthorized."}}, "type": "object"}}}}, "404": {"description": "Not Found - Target entity was not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Record not found."}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge/vorgangsnummer/{vorgangsnummer}": {"get": {"tags": ["Vorgaenge"], "summary": "Redirect von Vorgangsnummer zu <PERSON>", "description": "Findet einen Vorgang anhand der Vorgangsnummer und leitet zur ID-basierten Route weiter.", "operationId": "showVorgangByVorgangsnummer", "parameters": [{"name": "vorgangsnummer", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"302": {"description": "Redirect to the ID-based endpoint"}, "404": {"description": "Not Found - <PERSON><PERSON><PERSON><PERSON> not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Record not found."}}, "type": "object"}}}}}}}, "/api/v1/vorgaenge/vorgangsgruppe": {"post": {"tags": ["Vorgaenge"], "summary": "<PERSON><PERSON><PERSON> einen neuen Vorgang mit der Vorgangsart vorgangsgruppe", "description": "Erst<PERSON>t einen neuen Vorgang mit der Vorgangsart vorgangsgruppe und den angegebenen Daten", "operationId": "storeVorgangsgruppeVorgang", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VorgangsgruppeVorgangStoreData"}}}}, "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"id": {"type": "integer", "example": 1}, "vorgangsnummer": {"type": "string", "example": "V-12345"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}}}}, "components": {"schemas": {"EmailKorrespondenzElementStoreData": {"description": "Request zum Erstellen einer neuen E-Mail-Korrespondenz in einem Vorgang", "required": ["content", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "properties": {"content": {"type": "string", "maxLength": 65500, "example": "Inhalt der E-Mail"}, "betreff": {"type": "string", "maxLength": 255, "example": "Betreff der E-Mail"}, "empfaenger": {"type": "array", "items": {"$ref": "#/components/schemas/EmailEmpfaengerData"}}, "cc": {"type": "array", "items": {"$ref": "#/components/schemas/EmailEmpfaengerData"}, "nullable": true}, "bcc": {"type": "array", "items": {"$ref": "#/components/schemas/EmailEmpfaengerData"}, "nullable": true}, "include_full_history": {"description": "<PERSON>n true, wird der komplette Korrespondenzverlauf an die E-Mail angehängt", "type": "boolean", "example": false, "nullable": true}}, "type": "object"}, "ErinnerungElementStoreData": {"description": "Request zum Erstellen einer neuen Erinnerung in einem Vorgang", "required": ["content", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "properties": {"content": {"type": "string", "maxLength": 65500, "example": "Inhalt der Erinnerung"}, "betreff": {"type": "string", "maxLength": 255, "example": "Betreff der Erinnerung"}, "empfaenger": {"type": "array", "items": {"$ref": "#/components/schemas/EmailEmpfaengerData"}}, "cc": {"type": "array", "items": {"$ref": "#/components/schemas/EmailEmpfaengerData"}, "nullable": true}, "bcc": {"type": "array", "items": {"$ref": "#/components/schemas/EmailEmpfaengerData"}, "nullable": true}, "include_full_history": {"description": "<PERSON>n true, wird der komplette Korrespondenzverlauf an die E-Mail angehängt", "type": "boolean", "example": false, "nullable": true}, "faellig_at": {"description": "Fälligkeitsdatum für den Vorgang. Wenn nicht angegeben, wird automatisch 1 Woche ab Erstellungsdatum gesetzt.", "type": "string", "format": "date", "example": "2025-10-15", "nullable": true}}, "type": "object"}, "GespraechsnotizElementStoreData": {"required": ["content", "type", "date_from", "date_to"], "properties": {"content": {"type": "string", "maxLength": 65500, "example": "Inhalt der Notiz"}, "type": {"type": "string", "enum": ["phone", "on-site", "other"], "example": "phone"}, "date_from": {"type": "string", "format": "date-time", "example": "2023-10-01T12:00:00Z"}, "date_to": {"type": "string", "format": "date-time", "example": "2023-10-01T13:00:00Z"}}, "type": "object"}, "KommentarElementStoreData": {"required": ["content"], "properties": {"content": {"type": "string", "maxLength": 65500, "example": "Inhalt des Kommentars"}}, "type": "object"}, "MahnungElementStoreData": {"description": "Request zum Erstellen einer neuen Mahnung in einem Vorgang", "required": ["content", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "properties": {"content": {"type": "string", "maxLength": 65500, "example": "Inhalt der Mahnung"}, "betreff": {"type": "string", "maxLength": 255, "example": "Betreff der Mahnung"}, "empfaenger": {"type": "array", "items": {"$ref": "#/components/schemas/EmailEmpfaengerData"}}, "cc": {"type": "array", "items": {"$ref": "#/components/schemas/EmailEmpfaengerData"}, "nullable": true}, "bcc": {"type": "array", "items": {"$ref": "#/components/schemas/EmailEmpfaengerData"}, "nullable": true}, "include_full_history": {"description": "<PERSON>n true, wird der komplette Korrespondenzverlauf an die E-Mail angehängt", "type": "boolean", "example": false, "nullable": true}, "faellig_at": {"description": "Fälligkeitsdatum für den Vorgang. Wenn nicht angegeben, wird automatisch 1 Woche ab Erstellungsdatum gesetzt.", "type": "string", "format": "date", "example": "2025-10-15", "nullable": true}}, "type": "object"}, "SystemkommentarElementStoreData": {"required": ["content"], "properties": {"content": {"type": "string", "example": "Inhalt des Systemkommentars"}, "notify_participants": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> ist dies false. Wenn true, wird der Vorgang für alle Bearbeiter/Beobachter als ungelesen markiert.", "type": "boolean", "example": true, "nullable": true}}, "type": "object"}, "AufgabeVorgangStoreData": {"description": "Request zum Erstellen eines neuen Vorgangs mit Typ aufgabe", "required": ["titel", "content"], "properties": {"content": {"type": "string"}, "titel": {"type": "string", "maxLength": 255, "example": "Vorgangstitel"}, "faellig_at": {"type": "string", "format": "date", "example": "2021-01-01", "nullable": true}, "kunde_id": {"type": "integer", "nullable": true}, "gesellschaft_id": {"type": "integer", "nullable": true}, "sparte_id": {"type": "integer", "nullable": true}, "vertrag_ids": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3], "nullable": true}}, "type": "object"}, "BriefEmpfaengerData": {"required": ["name", "land", "plz", "stadt", "adresszeile1", "salutation_type"], "properties": {"name": {"type": "string", "example": "<PERSON>"}, "adresszeile1": {"type": "string", "example": "Musterstraße 1"}, "plz": {"type": "string", "example": "12345"}, "stadt": {"type": "string", "example": "Musterstadt"}, "land": {"type": "string", "example": "Deutschland"}, "salutation_type": {"type": "string", "enum": ["<PERSON>", "<PERSON><PERSON>", "Firma", ""], "example": "<PERSON>", "nullable": true}, "titel": {"type": "string", "example": "Dr.", "nullable": true}, "fax": {"type": "string", "example": "0123456789", "nullable": true}, "adresszeile2": {"type": "string", "nullable": true}}, "type": "object"}, "BriefVorgangStoreData": {"description": "Request zum Erstellen eines neuen Vorgangs mit Typ korrespondenz_brief", "required": ["vorgang_typ_id", "titel", "<PERSON><PERSON><PERSON><PERSON>", "brief_absender_typ", "content"], "properties": {"vorgang_typ_id": {"type": "integer", "example": 1}, "content": {"type": "string"}, "titel": {"type": "string", "maxLength": 255, "example": "Vorgangstitel"}, "empfaenger": {"$ref": "#/components/schemas/BriefEmpfaengerData"}, "brief_absender_typ": {"type": "string", "enum": ["makler", "kunde"], "example": "kunde"}, "brief_datum": {"type": "string", "nullable": true}, "faellig_at": {"type": "string", "format": "date", "example": "2021-01-01", "nullable": true}, "kunde_id": {"type": "integer", "nullable": true}, "gesellschaft_id": {"type": "integer", "nullable": true}, "sparte_id": {"type": "integer", "nullable": true}, "vertrag_ids": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3], "nullable": true}}, "type": "object"}, "DunkelverarbeitetVorgangStoreData": {"description": "Request zum Erstellen eines neuen Vorgangs mit Typ dunkelverarbeitet", "required": ["titel", "content"], "properties": {"content": {"type": "string"}, "titel": {"type": "string", "maxLength": 255, "example": "Vorgangstitel"}, "faellig_at": {"type": "string", "format": "date", "example": "2021-01-01", "nullable": true}, "kunde_id": {"type": "integer", "nullable": true}, "gesellschaft_id": {"type": "integer", "nullable": true}, "sparte_id": {"type": "integer", "nullable": true}, "vertrag_ids": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3], "nullable": true}}, "type": "object"}, "EmailEmpfaengerData": {"required": ["email"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "name": {"type": "string", "example": "<PERSON>", "nullable": true}}, "type": "object"}, "EmailVorgangStoreData": {"description": "Request zum Erstellen eines neuen Vorgangs mit Typ korrespondenz_email", "required": ["vorgang_typ_id", "titel", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "content"], "properties": {"vorgang_typ_id": {"type": "integer", "example": 1}, "titel": {"type": "string", "maxLength": 255, "example": "Vorgangstitel"}, "content": {"type": "string"}, "betreff": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "empfaenger": {"type": "array", "items": {"$ref": "#/components/schemas/EmailEmpfaengerData"}}, "faellig_at": {"type": "string", "format": "date", "example": "2021-01-01", "nullable": true}, "kunde_id": {"type": "integer", "nullable": true}, "vertrag_ids": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3], "nullable": true}, "gesellschaft_id": {"type": "integer", "nullable": true}, "sparte_id": {"type": "integer", "nullable": true}, "kunde_document_ids": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3], "nullable": true}, "cc": {"type": "array", "items": {"$ref": "#/components/schemas/EmailEmpfaengerData"}, "nullable": true}, "bcc": {"type": "array", "items": {"$ref": "#/components/schemas/EmailEmpfaengerData"}, "nullable": true}}, "type": "object"}, "VorgangBlueprintData": {"description": "Gemeinsames Blueprint-Input für E-Mail- und Brief-Vorgänge", "properties": {"vorgangstyp_id": {"type": "integer"}, "kunde_id": {"type": "integer", "nullable": true}, "vertrag_ids": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3], "nullable": true}, "gesellschaft_id": {"type": "integer", "nullable": true}, "sparte_id": {"type": "integer", "nullable": true}}, "type": "object"}, "VorgangsgruppeVorgangStoreData": {"description": "Request zum Erstellen eines neuen Vorgangs mit Typ vorgangsgruppe", "required": ["titel"], "properties": {"titel": {"type": "string", "maxLength": 255, "example": "Vorgangstitel"}, "faellig_at": {"type": "string", "format": "date", "example": "2021-01-01", "nullable": true}, "kunde_id": {"type": "integer", "nullable": true}, "gesellschaft_id": {"type": "integer", "nullable": true}, "sparte_id": {"type": "integer", "nullable": true}, "vertrag_ids": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3], "nullable": true}}, "type": "object"}, "BlueprintBriefVorgang": {"description": "Wrapper für einen Brief-Vorgangs-Blueprint", "properties": {"data": {"$ref": "#/components/schemas/BriefVorgangStoreData"}}, "type": "object"}, "BlueprintEmailVorgang": {"description": "Wrapper für einen E-Mail-Vorgangs-Blueprint", "properties": {"data": {"$ref": "#/components/schemas/EmailVorgangStoreData"}}, "type": "object"}, "BriefAdresse": {"description": "Enthält die Angaben zu einer Briefadresse.\n Dieses Objekt wird verwendet, um Absenderdaten in Briefen strukturiert darzustellen.", "properties": {"salutation_type": {"type": "string", "enum": ["<PERSON>", "<PERSON><PERSON>", "Firma", ""], "example": "<PERSON>"}, "name": {"type": "string", "example": "<PERSON>"}, "adresszeile1": {"type": "string", "example": "Musterstraße 1"}, "plz": {"type": "string", "example": "12345"}, "stadt": {"type": "string", "example": "Musterstadt"}, "land": {"type": "string", "example": "Deutschland"}, "titel": {"type": "string", "example": "Dr.", "nullable": true}, "fax": {"type": "string", "example": "0123456789", "nullable": true}, "adresszeile2": {"type": "string", "nullable": true}}, "type": "object"}, "EmailAdresse": {"description": "Enthält die Angaben zu einer E-Mail-Adresse inklusive des zugehörigen Namens.\n Dieses Objekt wird verwendet, um sowohl Absender- als auch Empfängerdaten\n in E-Mail-Kommunikation strukturiert darzustellen – z.B. in Korrespondenzelementen oder beim Versand von Mahnungen.", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "name": {"type": "string", "example": "<PERSON>"}}, "type": "object"}, "Gesellschaft": {"description": "Repräsentiert eine Versicherungsgesellschaft,\nwie sie in Professional works gepflegt ist. Die Gesellschaft besitzt eine eindeutige ID,\neinen vollen Namen sowie optional eine gebräuchliche Abkürzung (z.B. „BVA“ für „Beispiel Versicherung AG“).\n Gesellschaften werden u.a. in Verträgen und Vorgängen referenziert.", "properties": {"id": {"type": "integer", "example": 154}, "name": {"type": "string", "example": "Beispiel Versicherung AG"}, "abkuerzung": {"type": "string", "example": "BVA"}}, "type": "object"}, "Kunde": {"description": "Repräsent<PERSON><PERSON> einen <PERSON>,\nwie er in Professional works gepflegt ist.", "properties": {"id": {"type": "integer", "example": 154}, "name": {"type": "string", "example": "<PERSON>"}}, "type": "object"}, "Sparte": {"description": "Repräsentiert eine Versicherungssparte,\n wie sie in Professional works definiert ist.\n Jede Sparte besitzt eine eindeutige ID, einen Namen sowie optional eine gebräuchliche Abkürzung.\n Zusätzlich existiert ein Anzeigename, der ggf. vom technischen Namen abweichen kann,\n um eine geläufigere Bezeichnung darzustellen.\n Sparten sind hierarchisch aufgebaut und können bis zu drei Tiefenstufen umfassen\n (z.B. „Krankenversicherung“ → „Private Krankenversicherung“ → „Zusatzversicherung ambulant“).", "properties": {"id": {"type": "integer", "example": 132}, "name": {"type": "string", "example": "Krankenversicherung"}, "abkuerzung": {"type": "string", "example": "KV", "nullable": true}, "anzeigename": {"type": "string", "example": "Krankenversicherung (KV)"}}, "type": "object"}, "TimelineElement": {"description": "Ein Timeline-Element repräsentiert einen Eintrag in der chronologischen Vorgangs-Zeitleiste.\n    Es dient als Container für verschiedene Typen von Aktionen oder Informationen,\n    die während der Bearbeitung eines Vorgangs entstehen – z<PERSON><PERSON><PERSON>, Gesprächsnotizen, Erinnerungen oder Statusänderungen.\n    Der konkrete Inhalt wird über den Typ (elementType) bestimmt und verweist auf ein jeweils spezifisches Unterobjekt,\n    das im Detail beschreibt, was genau passiert ist.", "properties": {"id": {"type": "integer", "example": 1}, "elementType": {"type": "string", "enum": ["vertragAenderungen", "App\\Models\\UserAenderungElement", "folgevorgang", "verknuepfungen", "korrespondenzen", "loesch_kommentare", "systemkommentare", "kommentare", "statusAenderungen", "faelligkeitAenderungen", "gespraechsnotizen", "er<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "externeKorrespondenzen"], "example": "korrespondenzen"}, "element": {"type": "object", "oneOf": [{"$ref": "#/components/schemas/KorrespondenzElement"}, {"$ref": "#/components/schemas/VertragAenderungenElement"}, {"$ref": "#/components/schemas/UserAenderungElement"}, {"$ref": "#/components/schemas/FolgeVorgangTimelineElement"}, {"$ref": "#/components/schemas/VerknuepfungTimelineElement"}, {"$ref": "#/components/schemas/LoeschKommentarElement"}, {"$ref": "#/components/schemas/SystemkommentarElement"}, {"$ref": "#/components/schemas/KommentarElement"}, {"$ref": "#/components/schemas/StatusAenderungenElement"}, {"$ref": "#/components/schemas/FaelligkeitAenderungenElement"}, {"$ref": "#/components/schemas/GespraechsnotizElement"}, {"$ref": "#/components/schemas/ErinnerungElement"}, {"$ref": "#/components/schemas/MahnungElement"}, {"$ref": "#/components/schemas/ExterneKorrespondenzElement"}]}, "created_at": {"type": "string", "example": "2025-03-20"}}, "type": "object", "discriminator": {"propertyName": "type", "mapping": {"korrespondenzen": "#/components/schemas/KorrespondenzElement", "vertragAenderungen": "#/components/schemas/VertragAenderungenElement", "App\\Models\\UserAenderungElement": "#/components/schemas/UserAenderungElement", "folgevorgang": "#/components/schemas/FolgeVorgangTimelineElement", "verknuepfungen": "#/components/schemas/VerknuepfungTimelineElement", "loesch_kommentare": "#/components/schemas/LoeschKommentarElement", "systemkommentare": "#/components/schemas/SystemkommentarElement", "kommentare": "#/components/schemas/KommentarElement", "statusAenderungen": "#/components/schemas/StatusAenderungenElement", "faelligkeitAenderungen": "#/components/schemas/FaelligkeitAenderungenElement", "gespraechsnotizen": "#/components/schemas/GespraechsnotizElement", "erinnerungen": "#/components/schemas/ErinnerungElement", "mahnungen": "#/components/schemas/MahnungElement", "externeKorrespondenzen": "#/components/schemas/ExterneKorrespondenzElement"}}}, "ErinnerungElement": {"description": "Dieses Element dokumentiert eine Erinnerung, die im Rahmen eines Vorgangs versendet wurde –\n    typischerweise dann, wenn vom Kunden oder einer Gesellschaft keine Rückmeldung erfolgt ist.\n    Erinnerungen können manuell durch einen Nutzer ausgelöst werden und erscheinen in der Zeitleiste des Vorgangs.", "allOf": [{"$ref": "#/components/schemas/KorrespondenzElement"}]}, "ExterneKorrespondenzElement": {"description": "Dieses Element dokumentiert, dass eine externe Korrespondenz (E-Mail als .eml oder .msg) hochgeladen und in den Vorgang importiert wurde.\n    Die eigentliche Korrespondenz wird nicht in diesem Element dargestellt, sondern in einem separaten Korrespondenzelement.", "properties": {"id": {"type": "integer", "example": 12345}, "uses_attachments": {"description": "G<PERSON>t an, ob Anhänge aus der E-Mail mit in den Vorgang importiert wurden", "type": "boolean", "example": true}}, "type": "object"}, "FaelligkeitAenderungenElement": {"description": "Dieses Element zeigt an, dass die Fälligkeit eines Vorgangs geändert wurde.\n    Jeder Vorgang besitzt ein Fälligkeitsdatum, das z.B. zur internen Priorisierung dient.\n    Das Element enthält den vorherigen sowie den neuen Fälligkeitstermin.\n    Die Änderung kann durch Nutzer oder zukünftig durch Agenten vorgenommen werden.", "properties": {"id": {"type": "integer", "example": 12345}, "vorheriger_wert": {"type": "string", "format": "date-time", "example": "2023-06-15T00:00:00+02:00", "nullable": true}, "neuer_wert": {"type": "string", "format": "date-time", "example": "2023-06-30T00:00:00+02:00", "nullable": true}}, "type": "object"}, "FolgeVorgangTimelineElement": {"description": "Dieses Element verknüpft zwei Vorgänge logisch miteinander:\n    einen ursprünglichen Vorgang („Vorgänger“) und einen neu erstellten „Folgevorgang“.\n    <PERSON>s wird genutzt, wenn sich aus einem bestehenden Vorgang ein neuer Kontext ergibt,\n    der getrennt weiterverfolgt werden soll – z.B.\n    bei der Beantragung eines zusätzlichen Produkts oder einer neuen Sparte.", "properties": {"id": {"type": "integer"}, "vorgaenger": {"description": "ID des vorgaenger Vorgang", "type": "integer"}, "nachfolger": {"description": "ID des nachfolgenden Vorgang", "type": "integer"}}, "type": "object"}, "GespraechsnotizElement": {"description": "Erfasst eine interne Gesprächsnotiz innerhalb eines Vorgangs.\n    Die Notiz enthält Informationen über Gespräche mit Kunden,\n    etwa bei Vor-Ort-Terminen, Telefonaten oder anderen Kontakten.\n    Zusätzlich zum Inhalt können Beginn und Ende des Gesprächs sowie der Typ (z.B. telefonisch, vor Ort) dokumentiert werden.\n    Die Notizen sind rein intern und für den Kunden nicht sichtbar.", "properties": {"id": {"type": "integer", "example": 12345}, "type": {"type": "string", "enum": ["phone", "on-site", "other"], "example": "phone"}, "date_from": {"type": "string", "format": "date", "example": "2023-05-15"}, "date_to": {"type": "string", "format": "date", "example": "2023-05-15"}}, "type": "object"}, "KommentarElement": {"description": "Ein einfaches Textelement zur internen Dokumentation innerhalb eines Vorgangs.\n    Anders al<PERSON> bei Gesprächsnotizen werden hier keine Metadaten wie Gesprächszeitraum oder Typ gespeichert –\n    es handelt sich lediglich um einen freien Kommentartext.", "properties": {"id": {"type": "integer", "example": 12345}}, "type": "object"}, "KorrespondenzElement": {"description": "Beschreibt eine vollständige ausgehende Korrespondenz im Rahmen eines Vorgangs,\n    entweder per E-Mail oder Brief. Enthalten sind u.a. <PERSON>ff, Versandart, Versandzeitpunkt,\n    Empfänger- und Absenderinformationen (inkl. CC und BCC),\n    sowie der Versandstatus und potenzielle Fehlermeldungen.\n    Auch bei Briefen werden Metadaten wie das Versanddatum oder der Typ des Absenders mitgeführt.\n    Das KorrespondenzElement ist eines der zentralen Objekte für die externe Kommunikation mit Kunden oder Gesellschaften.", "properties": {"id": {"type": "integer", "example": 12345}, "versandart": {"type": "string", "enum": ["email", "brief"], "example": "email"}, "betreff": {"type": "string", "example": "Ihre Anfrage vom 15.05.2023"}, "inhalt_text": {"type": "string", "example": "<PERSON><PERSON> gee<PERSON>te Damen und Herren, ... Mit freundlichen Grüßen, ..."}, "versendet_at": {"type": "string", "format": "date-time", "example": "2023-05-20T14:30:00+02:00", "nullable": true}, "empfaenger": {"type": "array", "items": {"$ref": "#/components/schemas/EmailAdresse"}}, "absender": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/EmailAdresse"}, {"$ref": "#/components/schemas/BriefAdresse"}]}, "cc": {"type": "array", "items": {"type": "string"}, "example": ["<EMAIL>"]}, "bcc": {"type": "array", "items": {"type": "string"}, "example": ["<EMAIL>"]}, "status": {"type": "string", "enum": ["entwurf", "in_bearbeitung", "versandbereit", "in_versand", "abgeschlossen", "fehler"], "example": "abgeschlossen"}, "status_text": {"type": "string", "example": "Erfolgreich versendet", "nullable": true}, "cc_actual": {"type": "array", "items": {"type": "string"}, "example": ["<EMAIL>"]}, "bcc_actual": {"type": "array", "items": {"type": "string"}, "example": ["<EMAIL>"]}, "empfaenger_actual": {"type": "array", "items": {"$ref": "#/components/schemas/EmailAdresse"}}, "absender_actual": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/EmailAdresse"}, {"$ref": "#/components/schemas/BriefAdresse"}]}, "brief_datum": {"type": "string", "format": "date", "example": "2023-05-22", "nullable": true}, "email_type": {"type": "string", "example": "outgoing", "nullable": true}, "brief_sender_type": {"type": "string", "example": "gesellschaft", "nullable": true}, "contentUrl": {"type": "string", "example": "https://example.com/api/v1/vorgaenge/1/korrespondenzen/123/content", "nullable": true}}, "type": "object"}, "LoeschKommentarElement": {"description": "Dieses Element dokumentiert die Löschung eines anderen Timeline-Eintrags.\n    Es enthält einen verpflichtenden Kommentar, der den Grund für die Löschung beschreibt, so<PERSON><PERSON> den <PERSON>utzer, der die Löschung durchgeführt hat.", "properties": {"id": {"type": "integer"}, "user": {"$ref": "#/components/schemas/User"}}, "type": "object"}, "MahnungElement": {"description": "Eine Mahnung stellt eine Eskalationsstufe gegenüber einer einfachen Erinnerung dar.\n    Sie kann z.B. bei längerer Inaktivität auf einen offenen Vorgang hinweisen und wird explizit als Mahnung deklariert.\n    Das Objekt enthält ähnliche Informationen wie das Erinnerungselement, ist jedoch stärker formalisiert.", "allOf": [{"$ref": "#/components/schemas/KorrespondenzElement"}]}, "StatusAenderungenElement": {"description": "Ein Timeline-Eintrag zur Dokumentation von Statusänderungen innerhalb eines Vorgangs – z<PERSON><PERSON><PERSON> von „offen“ zu „geschlossen“.\n    Jeder Vorgang hat einen Status, der den aktuellen Bearbeitungsstand beschreibt.\n    Dieses Element speichert sowohl den alten als auch den neuen Statuswert.", "properties": {"id": {"type": "integer"}, "vorheriger_wert": {"type": "string", "enum": ["offen", "entwurf", "erinnerung_1", "erinnerung_2", "mahnung_1", "mahnung_2", "<PERSON><PERSON><PERSON><PERSON>"], "example": "offen"}, "neuer_wert": {"type": "string", "enum": ["offen", "entwurf", "erinnerung_1", "erinnerung_2", "mahnung_1", "mahnung_2", "<PERSON><PERSON><PERSON><PERSON>"], "example": "<PERSON><PERSON><PERSON><PERSON>"}}, "type": "object"}, "SystemkommentarElement": {"description": "Ein automatischer Kommentar, der vom System hinzugefügt wurde – z.B.\n    im Zuge einer bestimmten Aktion oder Regel. Diese Kommentare dienen der Nachvollziehbarkeit technischer Prozesse und sind Teil der Vorgangszeitleiste.", "properties": {"id": {"type": "integer"}}, "type": "object"}, "UserAenderungElement": {"description": "Dokumentiert Änderungen bei den zugewiesenen Nutzern eines Vorgangs.\n    Unterschieden wird zwischen Bearbeitern und Beobachtern – beide Rollen können hinzugefügt oder entfernt werden.\n    Das Element speichert zudem den Autor der Änderung sowie den betroffenen Nutzer.", "properties": {"id": {"type": "integer"}, "aktion": {"type": "string", "enum": ["hinzugefuegt", "entfernt"], "example": "hinzugefuegt"}, "task": {"type": "string", "enum": ["bear<PERSON>ter", "<PERSON><PERSON><PERSON><PERSON>"], "example": "bear<PERSON>ter"}, "author": {"$ref": "#/components/schemas/User"}, "assignedUser": {"$ref": "#/components/schemas/User"}}, "type": "object"}, "VerknuepfungTimelineElement": {"properties": {"id": {"type": "integer"}, "aktion": {"type": "string", "enum": ["erstellt", "entfernt"]}, "vorgang_id": {"type": "integer"}}, "type": "object"}, "VertragAenderungenElement": {"description": "Dieses Element zeigt an, dass der einem Vorgang zugeordnete Vertrag verändert wurde – z.B.\n    durch Austausch oder Ergänzung. Die Vertragsverknüpfung ist optional, wird aber häufig verwendet,\n    um Vorgänge mit spezifischen Verträgen aus dem Bestand zu verbinden.", "properties": {"id": {"type": "integer", "example": 132}, "author": {"$ref": "#/components/schemas/User"}}, "type": "object"}, "User": {"description": "Stellt einen Nutzer innerhalb von Professional works dar – typischerweise einen Makler oder Sachbearbeiter.\n    Das Objekt enthält eine eindeutige Nutzer-ID sowie den Namen der Person und wird z.B.\n    bei der Bearbeitung von Vorgängen oder als Autor von Aktionen referenziert.", "properties": {"id": {"type": "integer", "example": 132}, "name": {"type": "string", "example": "<PERSON>"}}, "type": "object"}, "Vertrag": {"description": "Abbildung eines konkreten Versicherungsvertrags innerhalb von Professional works.\n    Er enthält u.a. die Vertragsnummer, die zugehörige Kunden-ID, die verantwortliche Nutzer-ID,\n    sowie Informationen zur Versicherungsgesellschaft und zur Sparte.\n    Zusätzlich wird die sogenannte BaFin-Gesellschaft angegeben – in der Regel identisch mit der normalen Gesellschaft,\n    es sei denn, es handelt sich um einen nicht regulierten Anbieter mit übergeordneter regulierter Gesellschaft (z.B. Adcuri → Barmenia).\n    Diese Struktur erlaubt eine eindeutige fachliche und regulatorische Zuordnung des Vertrags.", "properties": {"id": {"type": "integer", "example": 1}, "vertragsnummer": {"type": "string", "example": "V123456"}, "user_id": {"type": "integer", "example": 42, "nullable": true}, "kunde_id": {"type": "integer", "example": 123}, "gesellschaft": {"oneOf": [{"$ref": "#/components/schemas/Gesellschaft"}], "nullable": true}, "bafin_gesellschaft": {"oneOf": [{"$ref": "#/components/schemas/Gesellschaft"}], "nullable": true}, "sparte": {"oneOf": [{"$ref": "#/components/schemas/Sparte"}], "nullable": true}}, "type": "object"}, "Vorgang": {"description": "Ein Vorgang bildet eine abgeschlossene Arbeitseinheit in Professional works ab – z.B. eine Korrespondenz,\n    ein <PERSON>, eine Schadenmeldung oder eine Aufgabe.\n    Er enthält einen Titel zur inhaltlichen Beschreibung, e<PERSON> Vorgangstyp (z.B. E-Mail, Brief, Aufgabe),\n    optionale Verknüpfungen zu Verträgen, einer Gesellschaft, einem Vertriebsweg (z.B. Fondsfinanz bei Pooling),\n    sowie einer Sparte. Zudem umfasst ein Vorgang die komplette chronologische Timeline (Korrespondenzen, Gesprächsnotizen etc.)\n    sowie das Erstellungs- und Fälligkeitsdatum.", "properties": {"id": {"type": "integer", "example": 1}, "titel": {"type": "string", "example": "Vorgang 1"}, "vorgang_typ": {"oneOf": [{"$ref": "#/components/schemas/VorgangTyp"}], "nullable": true}, "vertraege": {"type": "array", "items": {"$ref": "#/components/schemas/Vertrag"}}, "kunde": {"oneOf": [{"$ref": "#/components/schemas/Kunde"}], "nullable": true}, "gesellschaft": {"oneOf": [{"$ref": "#/components/schemas/Gesellschaft"}], "nullable": true}, "vertriebsweg": {"oneOf": [{"$ref": "#/components/schemas/Gesellschaft"}], "nullable": true}, "sparte": {"oneOf": [{"$ref": "#/components/schemas/Sparte"}], "nullable": true}, "timeline_elemente": {"type": "array", "items": {"$ref": "#/components/schemas/TimelineElement"}}, "created_at": {"type": "string"}, "faellig_at": {"type": "string"}, "status": {"type": "string", "enum": ["offen", "erinnerung_1", "erinnerung_2", "mahnung_1", "mahnung_2", "<PERSON><PERSON><PERSON><PERSON>"], "example": "offen"}}, "type": "object"}, "VorgangTyp": {"description": "Definiert die Art eines Vorgangs – z.B. E-Mail, Brief oder interne Aufgabe.\n    Vorgangstypen können vorkonfiguriert sein, um z.B. automatisch Ansprechpartner der Gesellschaften, Betreffzeilen oder Textbausteine zu setzen.\n    Diese Typisierung erleichtert die Standardisierung und Automatisierung von Bearbeitungsprozessen.", "properties": {"id": {"type": "integer", "example": 1}, "titel": {"type": "string", "example": "Änderung"}}, "type": "object"}, "VorgangsStatus": {"description": "Status eines Vorgangs", "type": "string", "enum": ["offen", "erinnerung_1", "erinnerung_2", "mahnung_1", "mahnung_2", "<PERSON><PERSON><PERSON><PERSON>"]}}, "parameters": {"kunde_id": {"name": "kunde_id", "in": "query", "description": "<PERSON><PERSON>-ID filtern", "required": false, "schema": {"type": "integer"}}, "vertrag_ids": {"name": "vertrag_ids", "in": "query", "description": "<PERSON><PERSON> Vertrags-IDs filtern", "required": false, "schema": {"type": "array", "items": {"type": "integer"}}}, "gesellschaft_id": {"name": "gesellschaft_id", "in": "query", "description": "Nach Versicherungsgesellschafts-ID filtern", "required": false, "schema": {"type": "integer"}}, "sparte_id": {"name": "sparte_id", "in": "query", "description": "<PERSON><PERSON>-ID filtern", "required": false, "schema": {"type": "integer"}}, "status": {"name": "status", "in": "query", "description": "Nach Status filtern", "required": false, "schema": {"$ref": "#/components/schemas/VorgangsStatus"}}, "vorgangsart": {"name": "vorgan<PERSON><PERSON>", "in": "query", "description": "<PERSON><PERSON> filtern", "required": false, "schema": {"type": "string", "enum": ["undefined", "vorgangsgruppe", "aufgabe", "korrespondenz_email", "korrespondenz_brief", "dunkelverarbeitet"]}}, "vorgang_typ_id": {"name": "vorgang_typ_id", "in": "query", "description": "Nach Vorgangstyp-ID filtern", "required": false, "schema": {"type": "integer"}}, "bearbeiter_id": {"name": "bearbeiter_id", "in": "query", "description": "<PERSON><PERSON>-User-ID filtern", "required": false, "schema": {"type": "integer"}}, "beobachter_id": {"name": "beobachter_id", "in": "query", "description": "<PERSON><PERSON>-User-ID filtern", "required": false, "schema": {"type": "integer"}}, "participant_id": {"name": "participant_id", "in": "query", "description": "<PERSON><PERSON>-User-ID (Bearbeiter oder Beobachter) filtern", "required": false, "schema": {"type": "integer"}}, "per_page": {"name": "per_page", "in": "query", "description": "Anzahl der Elemente pro Seite", "required": false, "schema": {"type": "integer", "default": 25, "maximum": 100, "minimum": 1}}, "cursor": {"name": "cursor", "in": "query", "description": "Cursor für Paginierung", "required": false, "schema": {"type": "string"}}, "faelligkeit_von": {"name": "faelligkeit_von", "in": "query", "description": "<PERSON><PERSON>t Vorgänge mit Fälligkeit ab (inklusive) Datum Y-m-d", "required": false, "schema": {"type": "string", "format": "date"}}, "faelligkeit_bis": {"name": "faelligkeit_bis", "in": "query", "description": "<PERSON>ltert Vorgänge mit Fälligkeit bis (inklusive) Datum Y-m-d", "required": false, "schema": {"type": "string", "format": "date"}}}}, "tags": [{"name": "Vorgaenge"}]}