APP_NAME=Vorgaenge
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://vorgaenge.demv.internal

APP_TIMEZONE=Europe/Berlin
APP_LOCALE=de
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=de_DE
APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database
BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=laravel
DB_PASSWORD=demv

BROADCAST_CONNECTION=log
CACHE_STORE=redis
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=cache
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=minio
AWS_SECRET_ACCESS_KEY=minio123
AWS_DEFAULT_REGION=us-east-1
AWS_URL=http://s3.vorgaenge.demv.internal
AWS_BUCKET=demv-dev-vorgaenge

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

FILESYSTEM_DISK=s3

#Relaxed
RELAXED_URL='https://relaxed.api.demv-systems.de/generate'

# PW
PW_BASE_URL=http://professionalworks.demv.internal
VITE_PW_BASE_URL="${PW_BASE_URL}" # load to vite
PW_API_TOKEN=

# Docker
UID=1000
GID=1000

VITE_SERVER_PORT=5173
S3_PORT=9005

GITHUB_TOKEN="${GITHUB_TOKEN}"

# XDEBUG
MY_IP=host.docker.internal

SENTRY_LARAVEL_DSN=
SENTRY_TRACES_SAMPLE_RATE=1

VITE_SENTRY_DSN=
VITE_SENTRY_TRACES_SAMPLE_RATE=1

VITE_ENABLE_BUG_REPORTING=false

VITE_APP_INTERCOM_APP_ID=
VITE_APP_GTM_ID=
INTERCOM_SECRET_KEY=

MAILER_BASE_URL=http://mailer.demv.internal

DB_HOST_PRODUCTION=
DB_USER_PRODUCTION=readonly
DB_PASSWORD_PRODUCTION=

DB_HOST_STAGING=
DB_USER_STAGING=
DB_PASSWORD_STAGING=

DB_HOST_DEV=
DB_USER_DEV=
DB_PASSWORD_DEV=

VAPOR_JUMPBOX_USER=ec2-user
VAPOR_JUMPBOX_HOST=
VAPOR_JUMPBOX_PRIVATE_KEY=path/to/private/key

CONVERT_API_SECRET=

AI_GATEWAY_URL=http://ai-gateway.demv.internal
