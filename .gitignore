# Setup
docker-compose.override.yml
supervisord.pid
ray.php

# Env
.env
.env.development
.env.production
.env.staging

# Frontend
/.pnpm-store
/node_modules
/public/hot
/public/build
npm-debug.log
yarn-error.log
pnpm-debug.log
*.local
.yalc
yalc.lock

#Frontend-Tests
/cypress/screenshots
/cypress/videos

#Snapshots
/database/snapshots/*.sql

# Backend
/public/storage
/storage/*.key
/vendor
.phpunit.result.cache
.deptrac.cache

# IDE
.idea/
.vscode/

# OS
.DS_Store

# vapor
.vapor/

#phpunit
/coverage
/.phpunit.cache/
