includes:
    - phpstan-baseline.neon
    - vendor/nunomaduro/larastan/extension.neon
    - vendor/phpstan/phpstan-deprecation-rules/rules.neon
    - vendor/phpstan/phpstan-strict-rules/rules.neon
    - vendor/phpstan/phpstan-mockery/extension.neon
    - vendor/phpstan/phpstan-phpunit/extension.neon
    - vendor/phpstan/phpstan-phpunit/rules.neon

rules:
    - <PERSON><PERSON>\Ray\PHPStan\RemainingRayCallRule

parameters:
    level: 8
    paths:
        - app
        - tests
    excludePaths:
        - tests/Unit/Support/JsonApi/TestModel.php
    checkModelProperties: true
    reportUnmatchedIgnoredErrors: true
    databaseMigrationsPath:
        - database/migrations

    ignoreErrors:
        - '#Dynamic call to static method PHPUnit\\Framework\\.*#'
        - '#Parameter \#1 \$options of method [a-zA-Z0-9\\_]+::save\(\) expects array\<model property of [a-zA-Z0-9\\_]+, mixed\>, array\<string, false\> given.#'
        -
            identifier: missingType.generics
        -
            identifier: missingType.iterableValue
