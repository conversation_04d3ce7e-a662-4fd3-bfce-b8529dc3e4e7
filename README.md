# Vorgangsmanager

Die neuen Vorgänge.

[![Powered by - Coffee](https://img.shields.io/badge/Powered_by-Coffee-2ea44f)](https://www.youtube.com/watch?v=dQw4w9WgXcQ)

## Erstes Setup

### 1. Traefik

Falls noch nicht geschehen, [local-docker-network](https://github.com/demvsystems/local-docker-network)
einrichten (siehe [Traefik Setup](https://github.com/demvsystems/local-docker-network#setup)).

In der Hosts-Datei die Einträge:

```
127.0.0.1 vorgaenge.demv.internal
127.0.0.1 vorgaenge.testing
127.0.0.1 openapi.vorgaenge.demv.internal
127.0.0.1 s3.vorgaenge.demv.internal
```

hinzufügen.

### 2. Environment vorbereiten

```
cp .env.example .env
```
In der `.env` die Environment Variablen definieren.

#### User und Group Ids

```
id -u
id -g
```

<PERSON><PERSON>tel<PERSON>, dass `UID` (`id -u`) und `GID` (`id -g`) in der `.env` korrekt sind.

#### Token

Für die Kommunikation zwischen Vorgänge und PW muss ein Token erzeugt werden unter

http://professionalworks.demv.internal/auth/token/admin

Nutze dabei folgende Werte: Name `Vorgaenge`, Service Id `vorgaenge`, User `DemvSystem`.

In der relevanten `.env` den Token unter `PW_API_TOKEN` speichern.

#### Professional works Public Key

Damit die Authentifizierung funktioniert, muss der Public-Key von Professional works übernommen werden. Dieser ist in AUTH_PUBLIC_KEY in
der `.env.auth` in der lokalen PW-instanz zu finden.

Dieser PublicKey muss in der `.env` unter `PW_PUBLIC_KEY` eingetragen werden.

#### GITHUB_TOKEN

In `.env` muss unter GITHUB_TOKEN ein Github Access Token hinterlegt werden.

### 3. Container bauen
```
just setup
```

Die Vorgänge sollten jetzt lokal erreichbar sein unter:

http://vorgaenge.demv.internal

Falls ein File-Permission Fehler kommt muss das System File Handle Limit erhöht werden:
```
echo fs.inotify.max_user_watches=256000 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 4. Xdebug

Siehe: [Xdebug Einrichten](./docs/xdebug.md)

### 5. phpcs (Code Sniffer)

Siehe: [phpcs Einrichten](./docs/phpcs.md)

## Deployment

Wir nutzen [Laravel Vapor](https://vapor.laravel.com/). Es existieren drei verschiedene Environments.

1. [`development`](https://dev-vorgaenge.professional.works)
    Dieses Environment kann zum Testen von lokalen Ständen benutzt werden.
    Um auf `development` zu deployen, kann auf den `dev`-Branch gepusht werden.
2. [`staging`](https://staging-vorgaenge.professional.works)
    Dieses Environment spiegelt den Stand des `main`-Branches wider.
    Jeder PR auf diesem Branch triggert ein Deployment.
3. [`production`](https://vorgaenge.professional.works)
    Dieses Environment spiegelt den Stand des `production`-Branches wider.
    Um ein Deployment auf Production zu starten, kann entweder
    1. `just deploy` ausgeführt werden, oder
    2. manuell ein PR `main` -> `production` geöffnet werden.

### Environment Variablen

#### Lokale Entwicklung

Für die lokale Entwicklung wird eine `.env`-Datei im Projektroot verwendet:
- `.env` - Hauptkonfiguration für die lokale Entwicklung
- `.env.testing` - Spezielle Konfiguration für Tests (wird automatisch geladen bei `vorgaenge.testing`)

#### Deployment-Environments

Für die verschiedenen Deployment-Umgebungen werden [verschlüsselte Environment-Dateien](https://laravel.com/docs/configuration#encrypting-environment-files) verwendet:

- `.env.development.encrypted` - Verschlüsselte Konfiguration für Development-Environment
- `.env.staging.encrypted` - Verschlüsselte Konfiguration für Staging-Environment
- `.env.production.encrypted` - Verschlüsselte Konfiguration für Production-Environment

Diese werden über Laravel Vapor automatisch entschlüsselt und geladen.

Lokal existieren just-Recipes, um Environment-Dateien zu ver- und entschlüsseln. Dafür muss in `.env` die Variable `LARAVEL_ENV_ENCRYPTION_KEY` gesetzt sein. Diese findet sich in der Vapor-UI des jeweiligen Environments.

```bash
# Alle Environment-Dateien entschlüsseln
just env-decrypt-all

# Alle Environment-Dateien verschlüsseln
just env-encrypt-all

# Einzelne Environments bearbeiten
just env-decrypt-development
just env-encrypt-development
```

## Port für Frontend anpassen

Der Standardport ist 5173. Dieser kann in der `.env` unter `VITE_SERVER_PORT` angepasst werden.
Danach müssen die Container neugebaut werden.

## Testing

### Vitest (Unit)

Siehe: [Unit-Test Readme](./resources/js/tests/readme.md)

### Cypress (End to End)

#### Setup

1. `vorgaenge.testing` in `/etc/hosts` eintragen.

Die End to End Tests laufen auf dem Host-System und erfordern, dass yarn installiert ist.
- `just cypress-run` => Führt die Tests im Hintergrund aus
- `just cypress-open` => Öffnet die Testoberfläche

#### Datenbank Seeding erzwingen

Wenn eine neue Migration dazugekommen ist, sollte die Datei `/database/snapshots/cypress-dump.sql` gelöscht werden.
Hierzu kann der Befehl `just cypress-delete-db-dump` genutzt werden.
Beim nächsten Testdurchlauf wird diese automatisch neu erstellt und beinhaltet das aktualisierte Datenbankschema.

#### Mehr

Siehe: [E2E Readme](./cypress/readme.md)

### Telescope (Logs)
Access Logs, Error Logs, SQL-Logs, ...

[vorgaenge.demv.internal/telescope/](http://vorgaenge.demv.internal/telescope/)
