<?php

declare(strict_types=1);

namespace App\Services;

use App\Domain\Korrespondenz\Enums\Status;
use App\Domain\Korrespondenz\Enums\Versandart;
use App\Domain\Korrespondenz\Models\ExterneKorrespondenzElement;
use App\Domain\Korrespondenz\Models\KorrespondenzElement;
use App\Domain\Korrespondenz\Services\MsgToMIMEConverterService;
use App\Enums\EmailType;
use App\Exceptions\CannotConvertMsgException;
use App\Exceptions\CannotGetFileContentException;
use App\Exceptions\CannotGetMailContentException;
use App\Exceptions\MissingVorgangException;
use App\Models\File;
use App\Models\TimelineElementBase;
use App\Support\Files\Services\FileService;
use Carbon\Carbon;
use Closure;
use DateTime;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use LogicException;
use ZBateson\MailMimeParser\Header\AddressHeader;
use ZBateson\MailMimeParser\Header\DateHeader;
use ZBateson\MailMimeParser\Header\HeaderConsts;
use ZBateson\MailMimeParser\Header\Part\AddressPart;
use ZBateson\MailMimeParser\IMessage;
use ZBateson\MailMimeParser\MailMimeParser;
use ZBateson\MailMimeParser\Message\MimePart;

class MailConverterService
{
    protected const MSG_FILE_MIME_TYPE = 'application/vnd.ms-outlook';

    public function __construct(
        private readonly FileService $fileService,
        private readonly MailMimeParser $mimeParser,
        private readonly TimelineService $timelineService,
        private readonly MsgToMIMEConverterService $converterService,
    ) {
    }

    /**
     * @throws CannotGetFileContentException|GuzzleException
     */
    private function getMailFileContent(File $uploadedMail): ?string
    {
        if ($uploadedMail->mimetype === self::MSG_FILE_MIME_TYPE) {
            Log::info('Processing MSG file', [
                'file_id' => $uploadedMail->id,
                'filename' => $uploadedMail->filename,
                'size' => $uploadedMail->size,
            ]);

            $fileStream = $this->fileService->getFileStream($uploadedMail);

            if ($fileStream === null) {
                Log::error('Could not get file stream for MSG file', [
                    'file_id' => $uploadedMail->id,
                    'filename' => $uploadedMail->filename,
                ]);
                throw new CannotGetFileContentException('Could not get file stream for MSG file');
            }

            // Validate file stream before conversion
            if (!is_resource($fileStream)) {
                Log::error('Invalid file stream for MSG file', [
                    'file_id' => $uploadedMail->id,
                    'filename' => $uploadedMail->filename,
                    'stream_type' => gettype($fileStream),
                ]);
                throw new CannotGetFileContentException('Invalid file stream for MSG file');
            }

            try {
                return $this->converterService->convertMsgToMIMEString($fileStream);
            } catch (Exception $e) {
                Log::error('MSG to MIME conversion failed in MailConverterService', [
                    'file_id' => $uploadedMail->id,
                    'filename' => $uploadedMail->filename,
                    'error' => $e->getMessage(),
                ]);
                throw new CannotConvertMsgException('MSG conversion failed: ' . $e->getMessage());
            }
        }

        return $this->fileService->getFileContents($uploadedMail);
    }

    /**
     * @throws CannotGetMailContentException
     */
    public function getKorrespondenzFromMail(string $mail, EmailType $emailType): KorrespondenzElement
    {
        $message = $this->mimeParser->parse($mail, true);

        /** @var AddressHeader|null $fromHeader */
        $fromHeader = $message->getHeader(HeaderConsts::FROM);
        /** @var AddressHeader|null $toHeader */
        $toHeader = $message->getHeader(HeaderConsts::TO);
        /** @var AddressHeader|null $ccHeader */
        $ccHeader = $message->getHeader(HeaderConsts::CC);
        /** @var AddressHeader|null $bccHeader */
        $bccHeader = $message->getHeader(HeaderConsts::BCC);

        $from = array_map($this->getNameAndMailFromAddress(), $fromHeader?->getAddresses() ?? [])[0] ?? null;
        $to = array_map($this->getNameAndMailFromAddress(), $toHeader?->getAddresses() ?? []);
        $cc = array_map($this->getNameAndMailFromAddress(), $ccHeader?->getAddresses() ?? []);
        $bcc = array_map($this->getNameAndMailFromAddress(), $bccHeader?->getAddresses() ?? []);

        /** @var DateHeader|null $versendetAtHeader */
        $versendetAtHeader = $message->getHeader(HeaderConsts::DATE);
        $versendetAt = Carbon::instance($versendetAtHeader?->getDateTime() ?? new DateTime());
        // supported mysql timestamp range
        if ($versendetAt->year >= 2038 || $versendetAt->year < 1970) {
            $versendetAt = Carbon::now();
        }

        $korrespondenz = new KorrespondenzElement();
        $korrespondenz->versandart = Versandart::EMAIL;

        $korrespondenz->betreff = $message->getHeader(HeaderConsts::SUBJECT)?->getValue()
            ?? 'Betreff konnte nicht abgerufen werden';
        $korrespondenz->content = self::getMessageBodyHtml($message);

        $korrespondenz->versendet_at = $versendetAt;
        $korrespondenz->empfaenger = $to;
        $korrespondenz->empfaenger_actual = $to;
        $korrespondenz->absender = $from;
        $korrespondenz->absender_actual = $from;
        $korrespondenz->cc = $cc;
        $korrespondenz->bcc = $bcc;
        $korrespondenz->status = Status::ABGESCHLOSSEN;
        $korrespondenz->email_type = $emailType;
        $korrespondenz->message_id = $message->getHeaderValue(HeaderConsts::MESSAGE_ID);

        return $korrespondenz;
    }

    public function convertEmbeddedFiles(KorrespondenzElement $korrespondenzElement, string $fileContents): string
    {
        $message = $this->mimeParser->parse($fileContents, true);

        $attachments = $message->getAllAttachmentParts();
        $resultContent = $korrespondenzElement->content;
        foreach ($attachments as $attachment) {
            $contentId = $attachment->getContentId();
            if ($contentId !== null && str_contains($resultContent, $contentId)) {
                $file = $this->fileService->createFile(
                    $korrespondenzElement,
                    $attachment->getContent() ?? '',
                    $attachment->getFilename() ?? '',
                    $attachment->getContentType() ?? ''
                );

                $resultContent = self::replaceCidByUrl($resultContent, $contentId, $file->id);
            }
        }
        return $resultContent;
    }

    /**
     * @return File[]
     */
    public function extractAttachmentFromMail(KorrespondenzElement $korrespondenzElement, string $fileContents): array
    {
        $message = $this->mimeParser->parse($fileContents, true);

        $attachments = $message->getAllAttachmentParts();
        $parsedAttachment = [];

        foreach ($attachments as $attachment) {
            /** @var MimePart $attachment */
            $filename = $attachment->getFilename();

            if ($filename === null) {
                continue;
                //We have not a File attachment but rather an attachment in the Headers here
            }

            $content = $attachment->getContent();

            if ($content === null) {
                Log::warning('Attachment is missing Content');
                continue;
            }

            $parsedAttachment[] = $this->fileService->createFile(
                owner: $korrespondenzElement,
                content: $content,
                filename: $filename,
                mimetype: (string) $attachment->getContentType(),
            );
        }

        return $parsedAttachment;
    }

    /**
     * @throws CannotGetFileContentException|CannotGetMailContentException|Exception|GuzzleException
     */
    public function convertUploadedMailToKorrespondenz(
        File $uploadedMail,
        bool $extractAttachments = false,
    ): KorrespondenzElement {
        $owningModel = $uploadedMail->owner;

        if (!$owningModel instanceof TimelineElementBase) {
            throw new LogicException('Owner of Uploaded mail needs to implement TimelineElementBase::');
        }

        if ($owningModel instanceof ExterneKorrespondenzElement) {
            $extractAttachments = $owningModel->uses_attachments;
        }

        $fileContents = $this->getMailFileContent($uploadedMail);

        if ($fileContents === null) {
            throw new CannotGetFileContentException();
        }

        $korrespondenzElement = $this->getKorrespondenzFromMail($fileContents, EmailType::uploaded());
        $korrespondenzElement->save();
        $korrespondenzElement->content = $this->convertEmbeddedFiles($korrespondenzElement, $fileContents);
        $korrespondenzElement->save();

        $vorgang = $owningModel->timelineEintrag?->vorgang;

        if ($vorgang === null) {
            throw new MissingVorgangException();
        }

        $this->timelineService->saveAndAttachElement(
            $vorgang,
            $korrespondenzElement,
            owner: Auth::user(),
        );

        if ($extractAttachments) {
            $this->extractAttachmentFromMail($korrespondenzElement, $fileContents);
        }

        return $korrespondenzElement;
    }

    protected function getNameAndMailFromAddress(): Closure
    {
        return static function (AddressPart $addressPart): array {
            $result = [
                'email' => $addressPart->getValue(),
            ];

            if ($addressPart->getName() !== '') {
                $result['name'] = $addressPart->getName();
            }

            return $result;
        };
    }

    /**
     * This function:
     * will use existing html if mail has html content
     * will use existing html if the mail has mixed content (html and text)
     * will convert a pure text message to html
     *
     * @throws CannotGetMailContentException
     */
    protected static function getMessageBodyHtml(IMessage $message): string
    {
        $html = $message->getHtmlContent();
        if ($html !== null && $html !== '') {
            return $html;
        }

        $text = $message->getTextContent();
        if ($text !== null && $text !== '') {
            return self::convertTextContentToHtml($text);
        }

        throw new CannotGetMailContentException();
    }

    protected static function convertTextContentToHtml(string $text): string
    {
        return sprintf(
            <<<EOT
            <!DOCTYPE html>
            <html>
                <head>
                    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                </head>
                <body>
                    %s
                </body>
            </html>
            EOT,
            preg_replace('/\n\r|\r\n|\r|\n/', '<br>', $text)
        );
    }

    public static function replaceCidByUrl(string $message, string $contentId, int $fileId): string
    {
        $url = "/api/files/{$fileId}";

        return str_replace('cid:' . $contentId, $url, $message);
    }
}
