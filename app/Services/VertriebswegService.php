<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Gesellschaft;
use App\Models\Vertrag;

class VertriebswegService
{
    private const FONDS_FINANZ_EXTERNAL_ID = 701;
    private const TOP_TEN_OCCURING_FF_VERWAHRSTELLEN = [
        'Fonds Finanz Maklerservice GmbH',
        'Fonds Finanz',
        'FondsFinanz',
        'Fondsfinanz-AC',
        'Fondsfinanz Maklerservice GmbH, Zentrale',
        'FondsFinanz Maklerservice GmbH',
        'FondsFinanz AC',
        'Fonds Finanz GmbH',
        'Fondsfinanz Maklerservice',
        'Fonds-Finanz Maklerservice GmbH',
    ];

    public function getVertriebsweg(Vertrag $vertrag): ?Gesellschaft
    {
        $isFondsFinanzVerwahrstelle = in_array(
            $vertrag->verwahrstelle,
            self::TOP_TEN_OCCURING_FF_VERWAHRSTELLEN,
            true,
        );

        if ($isFondsFinanzVerwahrstelle) {
            return Gesellschaft::query()
                ->where('external_id', self::FONDS_FINANZ_EXTERNAL_ID)
                ->firstOrFail();
        }

        return $vertrag->relationLoaded('gesellschaft')
            ? $vertrag->gesellschaft
            : $vertrag->gesellschaft()->firstOrFail();
    }
}
