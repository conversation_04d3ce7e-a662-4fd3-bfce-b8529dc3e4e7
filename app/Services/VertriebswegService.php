<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\VorgangstypEmpfaengertyp;
use App\Models\Gesellschaft;
use App\Models\User;
use App\Models\Vertrag;
use App\Models\VorgangTyp;

class VertriebswegService
{
    private const ANTRAG_EINREICHEN_TYP_ID = 2;
    private const FONDS_FINANZ_EXTERNAL_ID = 701;
    private const TOP_TEN_OCCURING_FF_VERWAHRSTELLEN = [
        'Fonds Finanz Maklerservice GmbH',
        'Fonds Finanz',
        'FondsFinanz',
        'Fondsfinanz-AC',
        'Fondsfinanz Maklerservice GmbH, Zentrale',
        'FondsFinanz Maklerservice GmbH',
        'FondsFinanz AC',
        'Fonds Finanz GmbH',
        'Fondsfinanz Maklerservice',
        'Fonds-Finanz Maklerservice GmbH',
    ];

    public function __construct(
        private readonly MaklerVermittlernummerService $maklerVermittlernummerService
    ) {
    }

    public function getVertriebswegFromVertrag(Vertrag $vertrag): ?Gesellschaft
    {
        $isFondsFinanzVerwahrstelle = in_array(
            $vertrag->verwahrstelle,
            self::TOP_TEN_OCCURING_FF_VERWAHRSTELLEN,
            true,
        );

        if ($isFondsFinanzVerwahrstelle) {
            return $this->getFondsFinanzGesellschaft();
        }

        return $vertrag->relationLoaded('gesellschaft')
            ? $vertrag->gesellschaft
            : $vertrag->gesellschaft()->firstOrFail();
    }

    public function getVertriebsweg(
        VorgangTyp $vorgangTyp,
        ?User $currentUser,
        ?Vertrag $vertrag = null,
        ?Gesellschaft $gesellschaft = null,
    ): ?Gesellschaft {
        if ($vertrag === null && $gesellschaft === null) {
            throw new \InvalidArgumentException("Either vertrag or gesellschaft must be provided.");
        }

        if (in_array($vorgangTyp->empfaenger_typ, [VorgangstypEmpfaengertyp::KUNDE, VorgangstypEmpfaengertyp::DEMV], true)) {
            return null;
        }

        if ($vertrag !== null) {
            return match ($vorgangTyp->empfaenger_typ) {
                VorgangstypEmpfaengertyp::VERTRIEBSWEG => $this->getVertriebswegFromVertrag($vertrag),

                VorgangstypEmpfaengertyp::GESELLSCHAFT => (
                    $vertrag->bafinGesellschaft !== null
                    && $vertrag->gesellschaft_external_id !== $vertrag->bafin_gesellschaft_external_id
                )
                    ? $vertrag->bafinGesellschaft
                    : $vertrag->gesellschaft,
            };
        }

        if ($vorgangTyp->id === self::ANTRAG_EINREICHEN_TYP_ID) {
            if ($currentUser === null) {
                return $gesellschaft;
            }

            return $this->maklerVermittlernummerService
                ->hasVermittlernummer($currentUser, $gesellschaft)
                ? $gesellschaft
                : $this->getFondsFinanzGesellschaft();
        }

        return $gesellschaft;
    }

    private function getFondsFinanzGesellschaft(): Gesellschaft
    {
        return Gesellschaft::query()
            ->where('external_id', self::FONDS_FINANZ_EXTERNAL_ID)
            ->firstOrFail();
    }
}
