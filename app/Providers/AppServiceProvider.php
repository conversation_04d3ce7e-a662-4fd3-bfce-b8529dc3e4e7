<?php

declare(strict_types=1);

namespace App\Providers;

use App\Console\Commands\Debug\DebugCommand;
use App\Exceptions\Handler;
use App\Listeners\LogJobProcess;
use App\Models\DokumentTyp;
use App\Models\File;
use App\Models\Firma;
use App\Models\Gesellschaft;
use App\Models\Kunde;
use App\Models\Sparte;
use App\Models\TimelineEintrag;
use App\Models\User;
use App\Models\UserAenderungElement;
use App\Models\Vertrag;
use App\Models\VertragAenderungenElement;
use App\Models\Vorgang;
use App\Models\VorgangParticipant;
use App\Models\VorgangTyp;
use App\Observers\CascadeDeleteUntervorgaengeObserver;
use App\Observers\ClearPwCacheVorgangObserver;
use App\Observers\VorgangFaelligkeitObserver;
use App\Services\ResourceTypeRegistry;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Contracts\Debug\ExceptionHandler;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Http\Request;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Queue\Events\JobProcessing;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * This is used by Laravel authentication to redirect users after login.
     */
    public const HOME = '/';

    /**
     * Register any application services.
     */
    public function register(): void
    {
        $isLocal = (bool) $this->app->environment('local');
        $isTelescopeEnabledForProduction = (bool) config('telescope.use_on_production');

        $this->app->bind(ExceptionHandler::class, Handler::class);

        if ($isLocal) {
            $this->registerTelescope(TelescopeServiceProvider::class);

            return;
        }

        if ($isTelescopeEnabledForProduction) {
            $this->registerTelescope(TelescopeProductionServiceProvider::class);
        }
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Model::preventLazyLoading();
        DB::prohibitDestructiveCommands(App::isProduction());
        DebugCommand::prohibit(App::isProduction());

        $this->app->singleton(ResourceTypeRegistry::class);
        $this->registerResourceTypes();

        Relation::morphMap([
            'user' => User::class,
            VertragAenderungenElement::MORPH_TYPE => VertragAenderungenElement::class,
        ]);

        Vorgang::observe([
            VorgangFaelligkeitObserver::class,
            ClearPwCacheVorgangObserver::class,
            CascadeDeleteUntervorgaengeObserver::class,
        ]);
        Event::listen([
            JobProcessing::class,
            JobProcessed::class,
            JobFailed::class,
            ], LogJobProcess::class);

        $this->bootRoute();
    }

    /**
     * @param class-string $telescopeServiceProvider
     */
    private function registerTelescope(string $telescopeServiceProvider): void
    {
        $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
        $this->app->register($telescopeServiceProvider);
    }

    private function registerResourceTypes(): void
    {
        /** @var ResourceTypeRegistry $resourceTypeRegistry */
        $resourceTypeRegistry = app(ResourceTypeRegistry::class);

        $resourceTypeRegistry->register(
            DokumentTyp::class,
            File::class,
            Firma::class,
            Gesellschaft::class,
            Kunde::class,
            Sparte::class,
            TimelineEintrag::class,
            User::class,
            UserAenderungElement::class,
            VertragAenderungenElement::class,
            Vertrag::class,
            Vorgang::class,
            VorgangParticipant::class,
            VorgangTyp::class,
        );
    }

    public function bootRoute(): void
    {
        RateLimiter::for('api', static function (Request $request): Limit {
            return Limit::perMinute(640)->by($request->user()?->id ?? $request->ip());
        });


    }
}
