<?php

declare(strict_types=1);

namespace App\Providers;

use App\Gateway\GatewayInterface;
use App\Mailer\MailerGatewayFactory;
use App\Support\Pdf\PdfService;
use App\Testing\MockedApiManagerGateway;
use App\Testing\MockedMailerGatewayFactory;
use App\Testing\MockedProfessionalWorksGateway;
use App\Testing\Services\MockedPdfService;
use Carbon\Carbon;
use Demv\SdkFramework\Gateway\GatewayInterface as SdkFrameworkGatewayInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;

class TestingServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (!(bool) $this->app->environment('testing') && !(bool) $this->app->environment('ci')) {
            return;
        }

        // Allow Cypress tests to define PW-Responses (FiFo)
        Route::post('/testing/pwMockResponse', static function (Request $request): void {
            DB::insert(
                'insert into testing_mock_db (method, url, body, created_at) values (?, ?, ?, ?)',
                [
                    $request->input('method'),
                    $request->input('url'),
                    $request->input('body'),
                    Carbon::now()->toDateString(),
                ],
            );
        });
    }

    public function register(): void
    {
        if (!(bool) $this->app->environment('testing') && !(bool) $this->app->environment('ci')) {
            return;
        }

        $this->app->singleton(SdkFrameworkGatewayInterface::class, MockedProfessionalWorksGateway::class);
        $this->app->singleton(MailerGatewayFactory::class, MockedMailerGatewayFactory::class);
        $this->app->singleton(GatewayInterface::class, MockedApiManagerGateway::class);

        $this->app->singleton(PdfService::class, MockedPdfService::class);
    }
}
