<?php

declare(strict_types=1);

namespace App\Mailer;

use App\Gateway\GatewayInterface;
use App\Gateway\MailerGateway;
use App\Models\User;
use App\Services\CachedAuthTokenService;

class MailerGatewayFactory
{
    public function __construct(private readonly CachedAuthTokenService $cachedAuthTokenService)
    {
    }

    public function createForUser(User $user): GatewayInterface
    {
        $userAuthToken = $this->cachedAuthTokenService->getRawOAuthTokenFor($user);
        return new MailerGateway(config('mailer.base_url'), $userAuthToken, 'vorgaenge');
    }

    public function createForApplication(): GatewayInterface
    {
        $authToken = $this->cachedAuthTokenService->getRawApplicationToken();
        return new MailerGateway(config('mailer.base_url'), $authToken, 'vorgaenge');
    }
}
