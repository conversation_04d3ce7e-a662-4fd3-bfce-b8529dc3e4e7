<?php

declare(strict_types=1);

namespace App\Mailer\Services;

use App\Mailer\Converter\UserMailSettingsResponseConverter;
use App\Mailer\DTO\UserMailSettings;
use App\Mailer\Enums\ConnectionStatus;
use App\Mailer\MailerGatewayFactory;
use App\Models\User;
use Demv\ProfessionalworksSdk\Exceptions\ApplicationTokenException;
use Demv\ProfessionalworksSdk\Exceptions\OAuthException;
use Exception;
use Illuminate\Support\Facades\Cache;

readonly class UserMailSettingsService
{
    private const MAIL_SETTINGS_KEY_FORMAT = 'user-mail-settings-%u';
    private const TTL_SETTINGS_IN_SECONDS = 60;

    public function __construct(
        private MailerGatewayFactory $mailerGatewayFactory,
        private UserMailSettingsResponseConverter $responseConverter,
    ) {
    }

    /**
     * @throws ApplicationTokenException
     * @throws Exception
     *
     * @return UserMailSettings[]
     */
    public function fetchAllMailSettings(): array
    {
        $mailerGateway = $this->mailerGatewayFactory->createForApplication();
        $response = $mailerGateway->get('/v1/users/email-settings');

        if ($response->getStatusCode() !== 200) {
            throw new Exception("Fetching all mail user settings failed. Code: {$response->getStatusCode()}");
        }

        $allSettingsResponse = json_decode($response->getBody()->getContents(), flags: JSON_OBJECT_AS_ARRAY)['response'];

        return array_map(
            fn ($settingsResponse) => $this->responseConverter->toUserMailSettings($settingsResponse),
            $allSettingsResponse,
        );
    }

    /**
     * @throws OAuthException
     * @throws Exception
     */
    public function fetchMailSettingFor(User $user): ?UserMailSettings
    {
        return Cache::remember(
            key: $this->getUserKey($user),
            ttl: self::TTL_SETTINGS_IN_SECONDS,
            callback: function () use ($user): ?UserMailSettings {
                $mailerGateway = $this->mailerGatewayFactory->createForUser($user);
                $response = $mailerGateway->get('/v1/users/me/email-settings');

                if ($response->getStatusCode() === 404) {
                    return null;
                }

                if ($response->getStatusCode() !== 200) {
                    throw new Exception(
                        "Fetching mail settings for user {$user->external_id} failed. Code: {$response->getStatusCode()}"
                    );
                }

                $contents = $response->getBody()->getContents();

                $settingsResponse = json_decode($contents, flags: JSON_OBJECT_AS_ARRAY)['response'];

                return $this->responseConverter->toUserMailSettings($settingsResponse);
            },
        );
    }

    /**
     * @throws ApplicationTokenException
     *
     * @return UserMailSettings[]
     */
    public function getAllMailSettingsWithReceivingStatusActive(): array
    {
        return array_filter($this->fetchAllMailSettings(), function (UserMailSettings $userMailSettings) {
            return $userMailSettings->receivingStatus === ConnectionStatus::ACTIVE;
        });
    }

    private function getUserKey(User $user): string
    {
        return sprintf(self::MAIL_SETTINGS_KEY_FORMAT, $user->external_id);
    }
}
