<?php

namespace App\Domain\Vorlagen\Repositories;

use App\Domain\Vorlagen\Models\Vorlage;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class VorlagenRepository
{
    /**
     * @param ?Builder<Vorlage> $builder
     *
     * @return Builder<Vorlage>
     */
    public function getVorlagenQuery(int $userId, ?Builder $builder = null): Builder
    {
        $user = User::findOrFail($userId);
        $firmaUserIds = $user->firma->users->map(static fn(User $user) => $user->id);

        return ($builder ?? Vorlage::query())
            // fix for https://github.com/laravel/framework/issues/36839
            ->where(static function (Builder $q) use ($userId, $firmaUserIds): void {
                /** @var Builder<Vorlage> $q */
                $q->where('ersteller_id', '=', $userId)
                    ->orWhere('ersteller_id', '=', null)
                    ->orWhere(
                        static function (Builder $q) use ($firmaUserIds): void {
                            /** @var Builder<Vorlage> $q */
                            $q->where('usage_by_owner_only', '=', 0)
                                ->whereIn('ersteller_id', $firmaUserIds);
                        },
                    );
            });
    }
}
