<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\Services;

use App\Domain\ApiGateway\DTO\Request\v1\Vorgang\BriefEmpfaengerDTO;
use App\Domain\ApiGateway\DTO\Request\v1\Vorgang\BriefVorgangDTO;
use App\Domain\ApiGateway\DTO\Request\v1\Vorgang\EmailVorgangDTO;
use App\Domain\ApiGateway\DTO\Response\v1\BlueprintBriefVorgangDTO;
use App\Domain\ApiGateway\DTO\Response\v1\BlueprintEmailVorgangDTO;
use App\Domain\Korrespondenz\Enums\Versandart;
use App\Domain\UserSettings\Enums\VorgangstitelSource;
use App\Domain\UserSettings\Models\UserSetting;
use App\Domain\Vorlagen\Models\BriefVorlageContent;
use App\Domain\Vorlagen\Models\MailVorlageContent;
use App\Domain\Vorlagen\Models\Vorlage;
use App\Domain\Vorlagen\Repositories\VorlagenRepository;
use App\Enums\BriefAbsenderType;
use App\Enums\VorgangEmpfaengerType;
use App\Enums\VorgangstypEmpfaengertyp;
use App\Models\Gesellschaft;
use App\Models\Kunde;
use App\Models\Sparte;
use App\Models\User;
use App\Models\Vertrag;
use App\Models\VorgangTyp;
use App\Services\AddressService;
use App\Services\AnsprechpartnerService;
use App\Services\AttachmentTypeKundeDocumentService;
use App\Services\VertriebswegService;
use Demv\ProfessionalworksSdk\Endpoints\Gesellschaft\Config\AnsprechpartnerConfig;
use Demv\ProfessionalworksSdk\Endpoints\Mail\MailAddressEndpoint;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class BlueprintBuilder
{
    private VorgangTyp $vorgangTyp;
    /** @var Collection<Vertrag> */
    private Collection $vertrage;
    private ?Kunde $kunde = null;
    private ?Gesellschaft $gesellschaft = null;
    private ?Gesellschaft $vertriebsweg = null;
    private ?Sparte $sparte = null;

    public function __construct(
        private readonly VorlagenRepository $vorlagenRepository,
        private readonly VertriebswegService $vertriebswegService,
        private readonly AnsprechpartnerService $ansprechpartnerService,
        private readonly MailAddressEndpoint $mailAddressEndpoint,
        private readonly AddressService $addressService,
        private readonly AttachmentTypeKundeDocumentService $vorlageAttachmentService,
    ) {
    }

    public function withBaseInfo(
        int $vorgangTypId,
        ?int $kundeExternalId,
        array $vertragExternalIds,
        ?int $gesellschaftExternalId,
        ?int $sparteId,
    ): self {
        $this->vorgangTyp = VorgangTyp::query()->findOrFail($vorgangTypId);

        $vertragQuery = Vertrag::query()
            ->whereIn('external_id', $vertragExternalIds);

        if ($kundeExternalId !== null) {
            // remove vertraege that do not belong to the given kunde
            $vertragQuery->whereHas('kunden', static function (Builder $q) use ($kundeExternalId): void {
                /** @var Builder<Kunde> $q */
                $q->where('external_id', $kundeExternalId);
            });
        }

        $this->vertrage = $vertragQuery->get();
        $firstVertrag = $this->vertrage->first();

        $kundeExternalId ??= $firstVertrag?->kunde_external_id;
        $gesellschaftExternalId ??= $firstVertrag?->bafin_gesellschaft_external_id
            ?? $firstVertrag?->gesellschaft_external_id;
        $sparteId ??= $firstVertrag?->sparte_id;

        $this->kunde = $kundeExternalId !== null
            ? Kunde::query()->where('external_id', $kundeExternalId)->first()
            : null;
        $this->gesellschaft = $gesellschaftExternalId !== null
            ? Gesellschaft::query()->where('external_id', $gesellschaftExternalId)->first()
            : null;
        $this->sparte = $sparteId !== null
            ? Sparte::query()->find($sparteId)
            : null;
        $this->vertriebsweg = $firstVertrag instanceof Vertrag
            ? $this->vertriebswegService->getVertriebswegFromVertrag($firstVertrag)
            : null;

        return $this;
    }

    public function buildEmailBlueprint(User $user): BlueprintEmailVorgangDTO
    {
        abort_unless($this->vorgangTyp->is_email, 422, 'Vorgangstyp unterstützt keine E-Mail-Korrespondenz');

        $vorlage = $this->selectVorlageFor($user, $this->vorgangTyp, Versandart::EMAIL);
        abort_if($vorlage === null, 422, 'Keine passende Vorlage gefunden');

        $isInformal = ($this->kunde?->informal ?? false)
            && $this->vorgangTyp->empfaenger_typ === VorgangstypEmpfaengertyp::KUNDE;

        /** @var MailVorlageContent $mailContent */
        $mailContent = $vorlage->vorlage;

        $betreff = $isInformal && $mailContent->informal_subject !== null
            ? $mailContent->informal_subject
            : $mailContent->formal_subject;

        $content = $isInformal && $mailContent->informal_content !== null
            ? $mailContent->informal_content
            : $mailContent->formal_content;

        $empfaenger = $this->resolveEmailEmpfaengerFromTypes(
            $vorlage->vorlage->empfaenger_types ?? [],
            $user,
        );

        $cc = $this->resolveEmailEmpfaengerFromTypes(
            $vorlage->vorlage->cc ?? [],
            $user,
        );
        $bcc = $this->resolveEmailEmpfaengerFromTypes(
            $vorlage->vorlage->bcc ?? [],
            $user,
        );

        $kundeDocumentIds = $this->kunde !== null
            ? $this->vorlageAttachmentService->getKundeDocuments(
                collect($mailContent->attachments),
                $this->kunde,
                $this->gesellschaft,
                $this->vertrage
            )->pluck('id')->all()
            : [];

        return new BlueprintEmailVorgangDTO(EmailVorgangDTO::from([
            'vorgang_typ_id' => $this->vorgangTyp->id,
            'titel' => $this->buildTitelFor($user, $this->vorgangTyp, $vorlage),
            'content' => $content,
            'betreff' => $betreff,
            'empfaenger' => $empfaenger,
            'cc' => $cc,
            'bcc' => $bcc,
            'kunde_id' => $this->kunde?->external_id,
            'gesellschaft_id' => $this->gesellschaft?->external_id,
            'sparte_id' => $this->sparte?->id,
            'vertrag_ids' => $this->vertrage->pluck('external_id')->all(),
            'kunde_document_ids' => $kundeDocumentIds,
        ]));
    }

    public function buildBriefBlueprint(User $user): BlueprintBriefVorgangDTO
    {
        abort_unless($this->vorgangTyp->is_brief, 422, 'Vorgangstyp unterstützt keine Brief-Korrespondenz');

        $vorlage = $this->selectVorlageFor($user, $this->vorgangTyp, Versandart::BRIEF);
        abort_if($vorlage === null, 422, 'Keine passende Vorlage gefunden');

        $kunde = $this->kunde;
        $isInformal = ($kunde?->informal ?? false)
            && $this->vorgangTyp->empfaenger_typ === VorgangstypEmpfaengertyp::KUNDE;

        /** @var BriefVorlageContent $briefContent */
        $briefContent = $vorlage->vorlage;

        $content = $isInformal && $briefContent->informal_content !== null
            ? $briefContent->informal_content
            : $briefContent->formal_content;

        $empfaenger = $this->buildBriefEmpfaenger(
            $this->vorgangTyp->empfaenger_typ,
            $kunde,
            $this->gesellschaft,
            $this->vertriebsweg,
        );

        return new BlueprintBriefVorgangDTO(BriefVorgangDTO::from([
            'vorgang_typ_id' => $this->vorgangTyp->id,
            'titel' => $this->buildTitelFor($user, $this->vorgangTyp, $vorlage),
            'content' => $content,
            'empfaenger' => $empfaenger,
            'brief_absender_typ' => $briefContent->sender_typ ?? BriefAbsenderType::MAKLER->value,
            'brief_datum' => null,
            'kunde_id' => $this->kunde?->external_id,
            'gesellschaft_id' => $this->gesellschaft?->external_id,
            'sparte_id' => $this->sparte?->id,
            'vertrag_ids' => $this->vertrage->pluck('external_id')->all(),
        ]));
    }

    public function selectVorlageFor(User $user, VorgangTyp $vorgangstyp, Versandart $versandart): ?Vorlage
    {
        $morphType = match ($versandart) {
            Versandart::EMAIL => MailVorlageContent::MORPH_TYPE,
            Versandart::BRIEF => BriefVorlageContent::MORPH_TYPE,
        };

        return $this->vorlagenRepository->getVorlagenQuery($user->id)
            ->with('vorlage')
            ->where('vorlage_type', $morphType)
            ->whereHas('vorlage', static function (Builder $q) use ($vorgangstyp): void {
                if (in_array($q->getModel()->getTable(), ['vorlagen_mail', 'vorlagen_brief'], true)) {
                    /** @var Builder<MailVorlageContent|BriefVorlageContent> $q */
                    $q->where('vorgang_typ_id', $vorgangstyp->id);
                }
            })
            ->whereNull('ersteller_id') // only standard vorlagen for now
            ->orderByDesc('updated_at')
            ->first();
    }

    public function buildTitelFor(User $user, VorgangTyp $vorgangstyp, ?Vorlage $vorlage): string
    {
        /** @var UserSetting|null $settings */
        $settings = UserSetting::forUser($user);

        $source = $settings?->vorgangstitel_source ?? VorgangstitelSource::VORGANGSTYP;

        if ($source === VorgangstitelSource::VORLAGE && $vorlage !== null) {
            return $vorlage->name;
        }

        return $vorgangstyp->titel;
    }

    /**
     * @param array<int,string> $types
     * @return array<int,array{name:string,email:string}>
     */
    private function resolveEmailEmpfaengerFromTypes(array $types, User $user): array
    {
        $result = new Collection();

        foreach ($types as $type) {
            $result = $result->merge($this->resolveEmailEmpfaengerFromType($type, $user));
        }

        return $result->unique(fn($item) => $item['email'])->all();
    }

    private function resolveEmailEmpfaengerFromType(string $type, User $user): array
    {
        $isEmpfaengerType = in_array($type, array_column(VorgangEmpfaengerType::cases(), 'value'), true);
        if (!$isEmpfaengerType) {
            $email = trim($type);

            $isValidEmail = filter_var($email, FILTER_VALIDATE_EMAIL) !== false;

            return $isValidEmail ? [['email' => $email]] : [];
        }

        $enum = VorgangEmpfaengerType::from($type);

        switch ($enum) {
            case VorgangEmpfaengerType::DEMV:
                return [['name' => 'DEMV', 'email' => '<EMAIL>']];

            case VorgangEmpfaengerType::GESELLSCHAFT:
                $vertriebsweg = $this->vertriebsweg ?? $this->gesellschaft;
                if ($vertriebsweg === null) {
                    return [];
                }

                $config = new AnsprechpartnerConfig(
                    userId: $user->external_id,
                    insuranceCompanyId: $vertriebsweg->external_id,
                    subjectId: $this->vorgangTyp->external_support_assignment_subject_id,
                    forProductComboId: $this->sparte?->id,
                    multiple: false,
                );

                return $this->ansprechpartnerService->getAnsprechpartner($config)
                    ->map(fn($p) => ['email' => $p->email, 'name' => $p->lastname])
                    ->values()
                    ->all();

            case VorgangEmpfaengerType::KUNDE:
                if ($this->kunde?->email === null || $this->kunde->email === '') {
                    return [];
                }

                return [['name' => $this->kunde->name, 'email' => $this->kunde->email]];

            case VorgangEmpfaengerType::VERSENDER:
                $sender = $this->mailAddressEndpoint->getSenderAddress($user->external_id);
                if ($sender->email === '') {
                    return [];
                }

                return [[
                    'email' => $sender->email,
                    'name' => $sender->name ?? '',
                ]];

            case VorgangEmpfaengerType::ZUSTAENDIGER_VERMITTLER:
                if ($this->kunde?->user_external_id === null) {
                    return [];
                }

                $receiver = $this->mailAddressEndpoint->getReceiverAddress($this->kunde->user_external_id);
                return [[
                    'email' => $receiver->email,
                    'name' => $receiver->name ?? '',
                ]];
        }
    }

    private function buildBriefEmpfaenger(
        VorgangstypEmpfaengertyp $empfaengerTyp,
        ?Kunde $kunde,
        ?Gesellschaft $gesellschaft,
        ?Gesellschaft $vertriebsweg,
    ): BriefEmpfaengerDTO {
        switch ($empfaengerTyp) {
            case VorgangstypEmpfaengertyp::KUNDE:
                abort_if($kunde === null, 422, 'Dieser Vorgangstyp benötigt einen gültigen Kunden');

                return $this->buildBriefEmpfaengerForKunde($kunde);

            case VorgangstypEmpfaengertyp::GESELLSCHAFT:
                abort_if($gesellschaft === null, 422, 'Dieser Vorgangstyp benötigt eine gültige Gesellschaft');

                return $this->buildBriefEmpfaengerForGesellschaft($gesellschaft);

            case VorgangstypEmpfaengertyp::VERTRIEBSWEG:
                abort_if($vertriebsweg === null, 422, 'Dieser Vorgangstyp benötigt eine gültige Vertriebswege');

                return $this->buildBriefEmpfaengerForGesellschaft($vertriebsweg);

            case VorgangstypEmpfaengertyp::DEMV:
                $demvGesellschaft = Gesellschaft::query()
                    ->where('external_id', Gesellschaft::DEMV_GESELLSCHAFT_EXTERNAL_ID)
                    ->firstOrFail();

                return $this->buildBriefEmpfaengerForGesellschaft($demvGesellschaft);
        }
    }

    private function buildBriefEmpfaengerForKunde(Kunde $kunde): BriefEmpfaengerDTO
    {
        try {
            $address = $this->addressService->getMainAddressForKunde($kunde);
        } catch (\Exception) {
            abort(422, 'Keine Adresse für den Kunden gefunden');
        }

        return BriefEmpfaengerDTO::from([
            'salutation_type' => $kunde->salutation_type->value,
            'titel' => $kunde->title,
            'name' => $kunde->name,
            'adresszeile1' => $address->strasse . ' ' . $address->hausnummer,
            'adresszeile2' => $address->adresszusatz,
            'plz' => $address->plz ?? '',
            'stadt' => $address->ort ?? '',
            'land' => 'Deutschland',
        ]);
    }

    private function buildBriefEmpfaengerForGesellschaft(Gesellschaft $gesellschaft): BriefEmpfaengerDTO
    {
        try {
            $address = $this->addressService->getAddressForGesellschaft($gesellschaft);
        } catch (\Exception) {
            Log::error('Could not find address for gesellschaft in blueprint builder', [
                'gesellschaft_id' => $gesellschaft->id,
                'gesellschaft_name' => $gesellschaft->name,
            ]);
            abort(422, 'Keine Adresse für die Gesellschaft gefunden');
        }

        return BriefEmpfaengerDTO::from([
            'name' => $gesellschaft->name,
            'adresszeile1' => $address->strasse . ' ' . $address->hausnummer,
            'adresszeile2' => $address->adresszusatz,
            'plz' => $address->plz ?? '',
            'stadt' => $address->ort ?? '',
            'land' => 'Deutschland',
        ]);
    }
}
