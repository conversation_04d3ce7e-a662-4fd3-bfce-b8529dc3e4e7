<?php

declare(strict_types=1);

namespace App\Domain\Korrespondenz;

use App\Domain\Korrespondenz\Commands\Debug\ResendLastMailKorrespondenz;
use App\Domain\Korrespondenz\Commands\Debug\SimulateMailImport;
use App\Domain\Korrespondenz\Commands\ResendMails;
use App\Domain\Korrespondenz\Commands\SetStuckKorrespondenzenToFehler;
use App\Domain\Korrespondenz\Events\KorrespondenzElementSaved;
use App\Domain\Korrespondenz\Http\Controller\ErinnerungElementController;
use App\Domain\Korrespondenz\Http\Controller\KorrespondenzElementController;
use App\Domain\Korrespondenz\Http\Controller\MahnungElementController;
use App\Domain\Korrespondenz\Http\Controller\MailConverterController;
use App\Domain\Korrespondenz\Http\Controller\MailHistoryController;
use App\Domain\Korrespondenz\Http\Controller\PdfController;
use App\Domain\Korrespondenz\Jobs\ClearOldKorrespondenzEntwuerfe;
use App\Domain\Korrespondenz\Listeners\AddAbsender;
use App\Domain\Korrespondenz\Listeners\IncomingEmailNotifier;
use App\Domain\Korrespondenz\Listeners\TriggerCreatePdfJob;
use App\Domain\Korrespondenz\Listeners\UpdateKorrespondenzMailStatus;
use App\Domain\Korrespondenz\Listeners\UpdateKorrespondenzStatusByFileStatus;
use App\Domain\Korrespondenz\Listeners\UpdateKorrespondenzStatusByVorgangStatus;
use App\Domain\Korrespondenz\Models\ErinnerungElement;
use App\Domain\Korrespondenz\Models\ExterneKorrespondenzElement;
use App\Domain\Korrespondenz\Models\KorrespondenzElement;
use App\Domain\Korrespondenz\Models\MahnungElement;
use App\Domain\Korrespondenz\Observers\AddVorgangsnummerToBetreffObserver;
use App\Domain\Korrespondenz\Observers\KorrespondenzElementContentObserver;
use App\Domain\Korrespondenz\Observers\MailStatusErrorNotifyObserver;
use App\Domain\Korrespondenz\Observers\SanitizeBetreffObserver;
use App\Domain\Korrespondenz\Observers\SendMailObserver;
use App\Domain\Korrespondenz\Observers\UpdateVorgangStatusByErinnerungMahnungObserver;
use App\Domain\Korrespondenz\Services\BriefService;
use App\Domain\Korrespondenz\Services\S3\S3ReferenceGenerator;
use App\Events\File\FileUploadFailed;
use App\Events\File\NewFileWasUploaded;
use App\Events\File\NotExistingFileWasRequested;
use App\Events\Mailer\EmailUpdated;
use App\Events\Timeline\TimelineElementAttached;
use App\Events\Vorgaenge\VorgangWasUpdated;
use App\Models\Vorgang;
use App\Observers\ReplaceTagsInContentObserver;
use App\Providers\DomainServiceProvider;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Schedule;

class KorrespondenzServiceProvider extends DomainServiceProvider
{
    public function boot(): void
    {
        parent::boot();

        KorrespondenzElement::observe([
            ReplaceTagsInContentObserver::class,
            KorrespondenzElementContentObserver::class,
            SanitizeBetreffObserver::class,
            AddVorgangsnummerToBetreffObserver::class,
            SendMailObserver::class,
            MailStatusErrorNotifyObserver::class,
        ]);
        MahnungElement::observe([
            ReplaceTagsInContentObserver::class,
            KorrespondenzElementContentObserver::class,
            SendMailObserver::class,
            MailStatusErrorNotifyObserver::class,
            UpdateVorgangStatusByErinnerungMahnungObserver::class,
        ]);
        ErinnerungElement::observe([
            ReplaceTagsInContentObserver::class,
            KorrespondenzElementContentObserver::class,
            SendMailObserver::class,
            MailStatusErrorNotifyObserver::class,
            UpdateVorgangStatusByErinnerungMahnungObserver::class,
        ]);

        Event::listen([
            NewFileWasUploaded::class,
            FileUploadFailed::class,
            KorrespondenzElementSaved::class,
        ], UpdateKorrespondenzStatusByFileStatus::class);
        Event::listen(NotExistingFileWasRequested::class, TriggerCreatePdfJob::class);
        Event::listen([VorgangWasUpdated::class], UpdateKorrespondenzStatusByVorgangStatus::class);
        Event::listen([
            TimelineElementAttached::class,
        ], AddAbsender::class);

        Event::listen([TimelineElementAttached::class], IncomingEmailNotifier::class);
        Event::listen([EmailUpdated::class], UpdateKorrespondenzMailStatus::class);
    }

    public function getCommands(): array
    {
        return [
            ResendMails::class,
            ResendLastMailKorrespondenz::class,
            SimulateMailImport::class,
            SetStuckKorrespondenzenToFehler::class,
        ];
    }

    public function getTimelineElemente(): array
    {
        return [
            KorrespondenzElement::MORPH_TYPE => KorrespondenzElement::class,
            ErinnerungElement::MORPH_TYPE => ErinnerungElement::class,
            MahnungElement::MORPH_TYPE => MahnungElement::class,
            ExterneKorrespondenzElement::MORPH_TYPE => ExterneKorrespondenzElement::class,
        ];
    }

    public function getModels(): array
    {
        return [
            ErinnerungElement::class,
            ExterneKorrespondenzElement::class,
            KorrespondenzElement::class,
            MahnungElement::class,
        ];
    }

    public function getVorgangsArten(): array
    {
        return [KorrespondenzElement::MORPH_TYPE];
    }

    public function registerRoutes(): void
    {
        // for testing
        Route::get('/test-s3-connection', [KorrespondenzElementController::class, 'testS3Connection']);

        Route::prefix('vorgaenge/{vorgang}')
            ->middleware('can:view,vorgang')
            ->group(static function (): void {
                // show entwuerfe
                Route::bind('korrespondenz', static fn ($id) => (
                    KorrespondenzElement::query()->withoutGlobalScope('excludeEntwuerfe')->findOrFail($id)
                ));
                Route::bind('mahnung', static fn ($id) => (
                    MahnungElement::query()->withoutGlobalScope('excludeEntwuerfe')->findOrFail($id)
                ));
                Route::bind('erinnerung', static fn ($id) => (
                    ErinnerungElement::query()->withoutGlobalScope('excludeEntwuerfe')->findOrFail($id)
                ));

                Route::apiResource('/korrespondenzen', KorrespondenzElementController::class)
                    ->parameter('korrespondenzen', 'korrespondenz')
                    ->only('show', 'store', 'update');
                Route::apiResource('/mahnungen', MahnungElementController::class)
                    ->parameter('mahnungen', 'mahnung')
                    ->only('show', 'store', 'update');
                Route::apiResource('/erinnerungen', ErinnerungElementController::class)
                    ->parameter('erinnerungen', 'erinnerung')
                    ->only('show', 'store', 'update');

                Route::apiResource('/attachMail', MailConverterController::class)
                    ->parameter('korrespondenzen', 'korrespondenz')
                    ->only('store');

                Route::prefix('/korrespondenzen/{korrespondenz}')->group(static function (): void {
                    Route::get('/history', [MailHistoryController::class, 'show']);
                    Route::get('/pdf', [PdfController::class, 'show']);
                    Route::post('/delete', [KorrespondenzElementController::class, 'delete']);

                    Route::middleware('debug')->get('/pdf/generate', static function (
                        Vorgang $vorgang,
                        KorrespondenzElement $korrespondenz,
                        BriefService $briefService,
                    ): \Illuminate\Http\Response {
                        abort_if($korrespondenz->timelineEintrag?->vorgang_id !== $vorgang->id, 404);

                        $pdf = $briefService->makeBriefForKorrespondenz($korrespondenz, $vorgang->owner);

                        return Response::make($pdf, 200, [
                            'Content-Type' => 'application/pdf',
                            'Content-Disposition' => 'inline; filename="test.pdf"',
                        ]);
                    });
                });

                Route::prefix('/mahnungen/{mahnung}')->group(static function (): void {
                    Route::get('/history', [MailHistoryController::class, 'show']);
                });

                Route::prefix('/erinnerungen/{erinnerung}')->group(static function (): void {
                    Route::get('/history', [MailHistoryController::class, 'show']);
                });
            });
    }

    public function register(): void
    {
        $this->app->singleton(S3ReferenceGenerator::class);
        parent::register();
    }

    protected function schedule(): void
    {
        Schedule::job(new ClearOldKorrespondenzEntwuerfe(Carbon::now()->subWeek()))->weekly();
    }
}
