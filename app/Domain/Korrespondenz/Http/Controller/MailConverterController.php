<?php

declare(strict_types=1);

namespace App\Domain\Korrespondenz\Http\Controller;

use App\Domain\Korrespondenz\Http\Request\MailConverterStoreRequest;
use App\Domain\Korrespondenz\Http\Resources\KorrespondenzElementResource;
use App\Domain\Korrespondenz\Models\ExterneKorrespondenzElement;
use App\Exceptions\CannotConvertMsgException;
use App\Exceptions\CannotGetFileContentException;
use App\Exceptions\CannotGetMailContentException;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Vorgang;
use App\Services\MailConverterService;
use App\Services\TimelineService;
use App\Support\Files\Factories\FileContentLoaderFactory;
use App\Support\Files\Services\FileService;
use App\Support\Files\ValueObjects\FileSource;
use App\Support\JsonApi\Resources\JsonApiResource;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;

class MailConverterController extends Controller
{
    public function __construct(
        protected MailConverterService $mailConverterService,
        protected TimelineService $timelineService,
        protected FileContentLoaderFactory $fileContentLoaderCreator,
        protected FileService $fileService,
    ) {
    }

    /**
     * @throws GuzzleException
     * @throws Exception
     */
    public function store(
        Vorgang $vorgang,
        MailConverterStoreRequest $request,
    ): JsonApiResource {
        $includeAttachments = Arr::get($request->all(), 'includeAttachments', false);
        $validated = $request->validated();

        if (!is_bool($includeAttachments)) {
            $includeAttachments = false;
        }

        $timelineElement = new ExterneKorrespondenzElement();
        $timelineElement->uses_attachments = $includeAttachments;

        /** @var User $user */
        $user = Auth::user() ?? throw new RuntimeException();

        $this->timelineService->saveAndAttachElement($vorgang, $timelineElement, $user);

        $source = new FileSource(
            tmpPath: $validated['temp_path'] ?? null,
            url: $validated['url'] ?? null,
        );

        $file = $this->fileService->createFileFromSource(
            userId: $user->id,
            owner: $timelineElement,
            filename: $validated['name'],
            source: $source,
            size: $validated['size'] ?? null,
            mimetype: $validated['mimetype'] ?? null,
            saveFileSynchronously: true,
        );

        try {
            $korrespondenz = $this->mailConverterService->convertUploadedMailToKorrespondenz(
                $file,
                $includeAttachments,
            );
        } catch (CannotGetMailContentException|CannotGetFileContentException|CannotConvertMsgException) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'Could not process email');
        }

        return KorrespondenzElementResource::make($korrespondenz);
    }
}
