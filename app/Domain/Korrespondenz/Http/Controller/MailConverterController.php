<?php

declare(strict_types=1);

namespace App\Domain\Korrespondenz\Http\Controller;

use App\Domain\Korrespondenz\Http\Request\MailConverterStoreRequest;
use App\Domain\Korrespondenz\Http\Resources\KorrespondenzElementResource;
use App\Domain\Korrespondenz\Models\ExterneKorrespondenzElement;
use App\Exceptions\CannotConvertMsgException;
use App\Exceptions\CannotGetFileContentException;
use App\Exceptions\CannotGetMailContentException;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Vorgang;
use App\Services\MailConverterService;
use App\Services\TimelineService;
use App\Support\Files\Factories\FileContentLoaderFactory;
use App\Support\Files\Services\FileService;
use App\Support\Files\ValueObjects\FileSource;
use App\Support\JsonApi\Resources\JsonApiResource;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;

class MailConverterController extends Controller
{
    public function __construct(
        protected MailConverterService $mailConverterService,
        protected TimelineService $timelineService,
        protected FileContentLoaderFactory $fileContentLoaderCreator,
        protected FileService $fileService,
    ) {
    }

    /**
     * @throws GuzzleException
     * @throws Exception
     */
    public function store(
        Vorgang $vorgang,
        MailConverterStoreRequest $request,
    ): JsonApiResource {
        Log::info('MailConverterController::store called', [
            'vorgang_id' => $vorgang->id,
            'request_data' => $request->all(),
        ]);

        $includeAttachments = Arr::get($request->all(), 'includeAttachments', false);
        $validated = $request->validated();

        if (!is_bool($includeAttachments)) {
            $includeAttachments = false;
        }

        $timelineElement = new ExterneKorrespondenzElement();
        $timelineElement->uses_attachments = $includeAttachments;

        /** @var User $user */
        $user = Auth::user() ?? throw new RuntimeException();

        $this->timelineService->saveAndAttachElement($vorgang, $timelineElement, $user);

        $source = new FileSource(
            tmpPath: $validated['temp_path'] ?? null,
            url: $validated['url'] ?? null,
        );

        $file = $this->fileService->createFileFromSource(
            userId: $user->id,
            owner: $timelineElement,
            filename: $validated['name'],
            source: $source,
            size: $validated['size'] ?? null,
            mimetype: $validated['mimetype'] ?? null,
            saveFileSynchronously: true,
        );

        Log::info('Starting MSG conversion', [
            'file_id' => $file->id,
            'filename' => $file->filename,
            'mimetype' => $file->mimetype,
            'include_attachments' => $includeAttachments,
        ]);

        try {
            $korrespondenz = $this->mailConverterService->convertUploadedMailToKorrespondenz(
                $file,
                $includeAttachments,
            );

            Log::info('MSG conversion completed successfully', [
                'file_id' => $file->id,
                'korrespondenz_id' => $korrespondenz->id,
            ]);
        } catch (CannotConvertMsgException $e) {
            Log::error('MSG conversion failed', [
                'file_id' => $file->id,
                'filename' => $file->filename,
                'mimetype' => $file->mimetype,
                'size' => $file->size,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $user->id,
            ]);
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'MSG-Datei konnte nicht konvertiert werden: ' . $e->getMessage());
        } catch (CannotGetMailContentException $e) {
            Log::error('Mail content extraction failed', [
                'file_id' => $file->id,
                'filename' => $file->filename,
                'mimetype' => $file->mimetype,
                'error' => $e->getMessage(),
                'user_id' => $user->id,
            ]);
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'E-Mail-Inhalt konnte nicht extrahiert werden: ' . $e->getMessage());
        } catch (CannotGetFileContentException $e) {
            Log::error('File content retrieval failed', [
                'file_id' => $file->id,
                'filename' => $file->filename,
                'mimetype' => $file->mimetype,
                'error' => $e->getMessage(),
                'user_id' => $user->id,
            ]);
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'Datei-Inhalt konnte nicht gelesen werden: ' . $e->getMessage());
        }

        return KorrespondenzElementResource::make($korrespondenz);
    }
}
