<?php

declare(strict_types=1);

namespace App\Domain\Korrespondenz\Services;

use App\Exceptions\CannotConvertMsgException;
use Carbon\Carbon;
use ConvertApi\ConvertApi;
use ConvertApi\FileUpload;
use ConvertApi\ResultFile;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use RuntimeException;
use stdClass;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Part\DataPart;


readonly class MsgToMIMEConverterService
{
    public function __construct()
    {
        ConvertApi::setApiCredentials(config('convert_api.secret'));
    }

    /**
     * @required resource $fileStream
     * @throws GuzzleException
     */
    public function convertMsgToMIMEString(mixed $fileStream): string
    {
        $msgUpload = new FileUpload($fileStream, 'mail.msg');

        try {
            $convertedMeta = ConvertApi::convert(
                'metadata',
                ['File' => $msgUpload],
                'email',
            );
            $convertedMessage = json_decode(Http::get($convertedMeta->getFile()->getUrl())->body());

            if ($convertedMessage === null) {
                throw new Exception('E-Mail is null');
            }
        } catch (Exception $exception) {
            throw new CannotConvertMsgException($exception->getMessage());
        }

        try {
            $attachments = $this->extractAttachments($msgUpload, $convertedMessage->Attachments);
        } catch (Exception $exception) {
            Log::error('Could not extract attachments from email', [
                'exception' => $exception,
            ]);
        }

        return $this->buildEmail($convertedMessage, $attachments ?? [])->toString();
    }

    /**
     * @param stdClass[] $convertedMessageAttachments the list of attachments that the converted message holds
     * @return DataPart[]
     */
    private function extractAttachments(FileUpload $msgUpload, array $convertedMessageAttachments): array
    {
        if (count($convertedMessageAttachments) === 0) {
            return [];
        }

        $extractedAttachments = ConvertApi::convert(
            'extract',
            ['File' => $msgUpload],
            'email',
        );

        $attachments = Arr::map(
            $extractedAttachments->getFiles(),
            function ($extractedAttachment) use ($convertedMessageAttachments) {
                try {
                    $fileBody = Http::get($extractedAttachment->getUrl())->body();
                } catch (Exception $exception) {
                    Log::warning('Could not download attachment', [
                        'attachment' => $extractedAttachment->getFileName(),
                        'exception' => $exception,
                    ]);
                    return null;
                }

                return $this->buildDataPart($fileBody, $extractedAttachment, $convertedMessageAttachments);
        });

        return array_filter($attachments);
    }

    /**
     * @param stdClass[] $convertedMessageAttachments
     */
    private function buildDataPart(
        string $fileBody,
        ResultFile $extractedAttachment,
        array $convertedMessageAttachments
    ): DataPart {
        $file = new DataPart(
            body: $fileBody,
            filename: $extractedAttachment->getFileName(),
        );

        $convertedMessageAttachment = Arr::first(
            $convertedMessageAttachments,
            fn($attachment) => $attachment->FileName === $extractedAttachment->getFileName()
                && $attachment->Size === $extractedAttachment->getFileSize(),
        );

        $cidExistsAndIsNotEmpty = $convertedMessageAttachment !== null
            && property_exists($convertedMessageAttachment, 'Id')
            && ($convertedMessageAttachment->Id ?? '') !== '';

        if ($cidExistsAndIsNotEmpty) {
            try {
                $file->setContentId($convertedMessageAttachment->Id);
            } catch (InvalidArgumentException $exception) {
                Log::notice('Could not set ContentId for attachment', [
                    'attachment' => $extractedAttachment->getFileName(),
                    'exception' => $exception,
                ]);
            }
        }

        return $file;
    }
    /**
     * @param stdClass $convertedMessage {
     *      PlainTextBody?: string,
     *      HtmlBody: string,
     *      Sender: string | stdClass {
     *          Name: string,
     *          Email: string
     *      },
     *      Recipients: stdClass {
     *          To: string[],
     *          Cc: string[],
     *          Bcc: string[]
     *      },
     *      Subject: string,
     *      SentTime: string,
     *      Attachments: stdClass[],
     *      CreationTime: string,
     *      ReceivedTime: string,
     *      LastModificationTime: string,
     * }
     * @param DataPart[] $attachments
     */
    private function buildEmail(stdClass $convertedMessage, array $attachments): Email
    {
        $email = (new Email())
            ->from($this->buildAddress($convertedMessage->Sender))
            ->to(...Arr::map(
                $convertedMessage->Recipients->To,
                fn($recipient) => $this->buildAddress($recipient))
            )
            ->cc(...Arr::map(
                $convertedMessage->Recipients->Cc,
                fn($recipient) => $this->buildAddress($recipient))
            )
            ->bcc(...Arr::map(
                $convertedMessage->Recipients->Bcc,
                fn($recipient) => $this->buildAddress($recipient))
            )
            ->subject($convertedMessage->Subject)
            ->text($convertedMessage->PlainTextBody)
            ->html($convertedMessage->HtmlBody)
            ->date($this->extractDate($convertedMessage));

        foreach ($attachments as $attachment) {
            $email->addPart($attachment);
        }

        return $email;
    }

    private function extractDate(stdClass $convertedMessage): Carbon
    {
        try {
            if ($convertedMessage->SentTime !== '') {
                return Carbon::createFromTimeString($convertedMessage->SentTime);
            }

            if ($convertedMessage->CreationTime !== '') {
                return Carbon::createFromTimeString($convertedMessage->CreationTime);
            }

            if ($convertedMessage->ReceivedTime !== '') {
                return Carbon::createFromTimeString($convertedMessage->ReceivedTime);
            }

            if ($convertedMessage->LastModificationTime !== '') {
                return Carbon::createFromTimeString($convertedMessage->LastModificationTime);
            }

            throw new RuntimeException('No date found in converted email');
        } catch (Exception $exception) {
            Log::notice('Could not extract date from email. Using fallback date', [
                'exception' => $exception,
            ]);
        }

        return Carbon::createFromTimestamp(0);
    }

    /**
     * @param string|stdClass $address { Name: string, Email: string }
     */
    private function buildAddress(stdClass | string $address): Address
    {
        try {
            if (is_string($address)) {
                return new Address($address);
            }

            return new Address(
                $address->Email,
                $address->Name,
            );
        } catch (Exception $exception) {
            Log::notice('Could not build Address from given data. Trying to extract email.', [
                'address' => $address,
                'exception' => $exception,
            ]);
        }

        return new Address($this->extractEmail($address));
    }

    /**
     * @param string|stdClass $address { Name: string, Email: string }
     */
    function extractEmail(stdClass | string $address): string {
        try {
            $addressString = is_string($address) ? $address : $address->Email;

            if (preg_match('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', $addressString, $matches) === 1) {
                return $matches[0];
            }

            throw new RuntimeException('No mail address found in converted email');
        } catch (Exception $exception) {
            Log::notice('Could not extract email from given data. Using fallback address.', [
                'address' => $address,
                'exception' => $exception,
            ]);
        }

        return '<EMAIL>';
    }
}
