<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Firma;
use App\Synchronization\Services\SyncService;
use Demv\ProfessionalworksSdk\Endpoints\Agency\AgencyEndpoint;
use Illuminate\Console\Command;

class SyncFirmaCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vorgaenge:sync:firma {pw-agency-id}';

    /**
     * The console command description.
     *
     * @var string|null
     */
    protected $description = 'Syncs a given Firma by PW ID';

    /**
     * Create a new command instance.
     */
    public function __construct(
        private AgencyEndpoint $agencyEndpoint,
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $firmaExternalId = $this->argument('pw-agency-id');

        if (!is_numeric($firmaExternalId)) {
            throw new \InvalidArgumentException('PW Id is not numeric');
        }

        $internalFirma = Firma::query()->where('external_id', $firmaExternalId)->first();

        if ($internalFirma === null) {
            $externalFirma = $this->agencyEndpoint->getAgencyById((int) $firmaExternalId) ?? throw new \RuntimeException('Could not get matching Agency from PW');
            /** @var Firma $internalFirma */
            $internalFirma = Firma::query()->create(
                [
                    'external_id' => $externalFirma->id,
                    'name' => $externalFirma->name ?? '',
                ],
            );
        }

        $this->line('Dispatching Chained Jobs for Firma Related Data: User, Kunden, Vertraege');

        SyncService::dispatchResyncForFirma($internalFirma);

        return 0;
    }
}
