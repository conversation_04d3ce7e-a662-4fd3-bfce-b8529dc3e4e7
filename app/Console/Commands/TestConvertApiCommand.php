<?php

declare(strict_types=1);

namespace App\Console\Commands;

use ConvertApi\ConvertApi;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestConvertApiCommand extends Command
{
    protected $signature = 'convert-api:test';
    protected $description = 'Test ConvertApi configuration and connectivity';

    public function handle(): int
    {
        $this->info('Testing ConvertApi configuration...');

        // Check if secret is configured
        $secret = config('convert_api.secret');
        if (empty($secret)) {
            $this->error('❌ CONVERT_API_SECRET is not configured');
            $this->line('Please set the CONVERT_API_SECRET environment variable in your .env file');
            return self::FAILURE;
        }

        $this->info('✅ CONVERT_API_SECRET is configured');
        $this->line('Secret length: ' . strlen($secret) . ' characters');
        $this->line('Secret preview: ' . substr($secret, 0, 4) . '...' . substr($secret, -4));

        // Test ConvertApi initialization
        try {
            ConvertApi::setApiCredentials($secret);
            $this->info('✅ ConvertApi credentials set successfully');
        } catch (Exception $e) {
            $this->error('❌ Failed to set ConvertApi credentials: ' . $e->getMessage());
            return self::FAILURE;
        }

        // Test basic ConvertApi connectivity (optional - requires actual API call)
        if ($this->option('verbose')) {
            $this->info('Testing ConvertApi connectivity...');
            try {
                // This would require a test file, so we'll skip for now
                $this->line('⚠️  Connectivity test skipped (requires test file)');
                $this->line('To test full functionality, try uploading an MSG file through the application');
            } catch (Exception $e) {
                $this->error('❌ ConvertApi connectivity test failed: ' . $e->getMessage());
                return self::FAILURE;
            }
        }

        $this->info('🎉 ConvertApi configuration test completed successfully');
        $this->line('');
        $this->line('Next steps:');
        $this->line('1. Try uploading an MSG file to test the full conversion process');
        $this->line('2. Check logs with: tail -f storage/logs/laravel.log | grep -i "msg\\|convert"');
        $this->line('3. Use the debug endpoint: GET /api/debug/convert-api-status');

        return self::SUCCESS;
    }
}
