<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\VorgangstypEmpfaengertyp;
use App\Models\Vertrag;
use App\Models\VorgangTyp;
use App\Services\VertriebswegService;
use Illuminate\Http\JsonResponse;

class VertriebswegController extends Controller
{
    public function __construct(private readonly VertriebswegService $vertriebswegService)
    {
    }

    public function show(Vertrag $vertrag, VorgangTyp $vorgangTyp): JsonResponse
    {
        $vertriebswegId = match($vorgangTyp->empfaenger_typ) {
            VorgangstypEmpfaengertyp::VERTRIEBSWEG => $this->vertriebswegService->getVertriebsweg($vertrag)?->id,
            VorgangstypEmpfaengertyp::GESELLSCHAFT => $vertrag->bafinGesellschaft !== null
                && $vertrag->gesellschaft_external_id !== $vertrag->bafin_gesellschaft_external_id
                    ? $vertrag->bafinGesellschaft->id
                    : $vertrag->gesellschaft?->id,
            default => null,
        };

        return response()->json(['id' => $vertriebswegId === null ? null : (string) $vertriebswegId]);
    }
}
