<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Gesellschaft;
use App\Models\Vertrag;
use App\Models\VorgangTyp;
use App\Services\VertriebswegService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class VertriebswegController extends Controller
{
    public function __construct(
        private readonly VertriebswegService $vertriebswegService,
    ) {
    }

    public function show(VorgangTyp $vorgangTyp, ?Vertrag $vertrag = null, ?Gesellschaft $gesellschaft = null): JsonResponse
    {
        abort_if(
            $vertrag === null && $gesellschaft === null,
            400,
            "Vertrag or Gesellschaft is required"
        );

        $currentUser = Auth::user();
        $vertriebsweg = $this->vertriebswegService->getVertriebsweg($vorgangTyp, $currentUser, $vertrag, $gesellschaft);

        return response()->json(['id' => $vertriebsweg === null ? null : (string) $vertriebsweg->id]);
    }
}
