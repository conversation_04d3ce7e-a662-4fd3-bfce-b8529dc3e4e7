<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Domain\External\Services\ExternalIdMappingService;
use App\Http\Requests\EmailSuggestionsGetRequest;
use App\Models\Gesellschaft;
use App\Models\Kunde;
use App\Models\Vorgang;
use App\Models\VorgangTyp;
use App\Services\EmailSuggestionsService;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class EmailSuggestionController
{
    public function suggestions(
        EmailSuggestionsGetRequest $request,
        ExternalIdMappingService $externalIdMappingService,
        EmailSuggestionsService $emailSuggestionsService,
    ): JsonResponse {

        $user = $request->user();
        abort_if($user === null, Response::HTTP_UNAUTHORIZED);

        $validated = $request->validated();

        $externalKundeId = isset($validated['kundeId'])
            ? $externalIdMappingService->getExternalId((int) $validated['kundeId'], Kunde::class)
            : null;
        $externalGesellschaftId = isset($validated['gesellschaftId'])
            ? $externalIdMappingService->getExternalId((int) $validated['gesellschaftId'], Gesellschaft::class)
            : null;
        $sparteId = isset($validated['sparteId'])
            ? (int) $validated['sparteId']
            : null;
        $subjectId = isset($validated['vorgangstypId'])
            ? VorgangTyp::query()->find((int) $validated['vorgangstypId'])?->external_support_assignment_subject_id
            : null;
        $vorgang = isset($validated['vorgangId'])
            ? Vorgang::query()->find((int) $validated['vorgangId'])
            : null;
        $externalVertriebswegId = isset($validated['vertriebswegId'])
            ? $externalIdMappingService->getExternalId((int) $validated['vertriebswegId'], Gesellschaft::class)
            : null;

        if ($vorgang !== null) {
            abort_if($user->cannot('view', $vorgang), Response::HTTP_UNAUTHORIZED);
        }

        $suggestions = $emailSuggestionsService->getMailSuggestions(
            externalUserId: $user->external_id,
            externalKundeId: $externalKundeId,
            externalGesellschaftId: $externalGesellschaftId,
            sparteId: $sparteId,
            subjectId: $subjectId,
            vorgang: $vorgang,
            externalVertriebswegId: $externalVertriebswegId,
        );

        return response()->json($suggestions);
    }
}
