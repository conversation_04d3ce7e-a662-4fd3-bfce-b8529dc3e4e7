<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\KundeResource;
use App\Models\Kunde;
use App\Models\User;
use App\Services\UserHierarchieService;
use App\Support\DataTransferObjectResource;
use App\Support\JsonApi\JsonApiQueryBuilder;
use App\Support\JsonApi\Resources\JsonApiCollection;
use App\Support\ReflectionDataTransferObjectResource;
use App\Synchronization\KundenSynchronizer;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\Adresse;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\KundenDetailsEndpoint;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Pagination\CursorPaginator;
use Illuminate\Support\Facades\Log;

class KundeController extends Controller
{
    public function __construct(
        protected UserHierarchieService $userHierarchieService,
        private KundenSynchronizer $kundenSynchronizer,
    ) {
        $this->authorizeResource(Kunde::class, 'kunde');
    }

    public function index(JsonApiQueryBuilder $queryBuilder, Request $request): JsonApiCollection
    {
        /** @var User $user */
        $user = $request->user();

        $query = $queryBuilder->build(new Kunde())
            ->whereIn('user_external_id', $this->userHierarchieService->getUnderlingsExternalForUser($user));

        /** @var CursorPaginator $paginator */
        $paginator = $query->cursorPaginate(10000);
        $paginator->withQueryString();

        $collection = new JsonApiCollection($paginator);
        $collection->additional([
            'meta' => [
                'total' => $query->count(),
            ],
        ]);

        return $collection;
    }

    public function show(Kunde $kunde, JsonApiQueryBuilder $queryBuilder, Request $request): KundeResource
    {
        $query = $queryBuilder->build(new Kunde());

        if ($request->boolean('refresh')) {
            $this->kundenSynchronizer->refresh($kunde->external_id);
        }

        return KundeResource::make($query->findOrFail($kunde->id));
    }

    public function address(Kunde $kunde, KundenDetailsEndpoint $kundenDetailsEndpoint): DataTransferObjectResource
    {
        $externalKundeId = $kunde->external_id;

        try {
            $kundeDetails = $kundenDetailsEndpoint->getDetails($externalKundeId);
            $mainAddresses = array_filter($kundeDetails->addresses, static function (Adresse $adresse): bool {
                return $adresse->isHauptAdresse === true;
            });
        } catch (\Throwable $exception) {
            Log::warning(
                'Could not resolve Main Address for Kunde - an address should always be available',
                [
                    'exception' => $exception,
                    'kundeId' => $externalKundeId,
                ],
            );
            abort(Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        if (count($mainAddresses) > 1) {
            Log::notice('Kunde has multiple Main-Addresses', [
                'kundeId' => $externalKundeId,
            ]);
        }

        if (count($mainAddresses) === 0) {
            Log::info('Kunde has no Main Address', [
                'kundeId' => $externalKundeId,
            ]);
            abort(Response::HTTP_NOT_FOUND);
        }

        return ReflectionDataTransferObjectResource::fromObject($mainAddresses[0]);
    }
}
