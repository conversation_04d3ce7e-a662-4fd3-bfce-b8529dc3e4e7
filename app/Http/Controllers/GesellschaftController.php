<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\GesellschaftResource;
use App\Models\Gesellschaft;
use App\Services\AddressService;
use App\Support\DataTransferObjectResource;
use App\Support\JsonApi\JsonApiQueryBuilder;
use App\Support\JsonApi\Resources\JsonApiCollection;
use App\Support\ReflectionDataTransferObjectResource;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class GesellschaftController
{
    public function index(JsonApiQueryBuilder $queryBuilder): JsonApiCollection
    {
        $query = $queryBuilder->build(new Gesellschaft());

        return new JsonApiCollection($query->get());
    }

    public function show(int $gesellschaft, JsonApiQueryBuilder $queryBuilder): GesellschaftResource
    {
        $query = $queryBuilder->build(new Gesellschaft());

        $gesellschaftModel = $query->findOrFail($gesellschaft);

        return GesellschaftResource::make($gesellschaftModel);
    }

    public function showDemv(): GesellschaftResource
    {
        $gesellschaft = Gesellschaft::query()
            ->where('external_id', Gesellschaft::DEMV_GESELLSCHAFT_EXTERNAL_ID)
            ->firstOrFail();

        return GesellschaftResource::make($gesellschaft);
    }

    public function address(
        Gesellschaft $gesellschaft,
        AddressService $addressService,
    ): DataTransferObjectResource {
        try {
            $address = $addressService->getAddressForGesellschaft($gesellschaft);
        } catch (\Throwable $exception) {
            Log::error(
                'Could not resolve Address for Gesellschaft - an address should always be available',
                [
                    'exception' => $exception,
                    'exceptionTrace' => $exception->getTraceAsString(),
                    'gesellschaftId' => $gesellschaft->external_id,
                ],
            );
            abort(Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return ReflectionDataTransferObjectResource::fromObject($address);
    }
}
