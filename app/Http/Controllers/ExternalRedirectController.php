<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Domain\External\Services\ExternalIdMappingService;
use App\Models\Gesellschaft;
use App\Models\Kunde;
use App\Models\Vorgang;
use Illuminate\Http\RedirectResponse;

class ExternalRedirectController extends Controller
{
    const COURTAGEERFASSUNG_BASE_URL = '/home/<USER>/neu';
    const BESTANDSUEBERTRAGUNG_BASE_URL = '/bestandsuebertragung/portfolio/create';
    const SCHADENERFASSUNG_BASE_URL = '/home/<USER>/schaeden/create/%s';
    const GENERATE_MAKLERAUFTRAG_URL = '/maklerdokumente/generateBrokerLetter';

    public function courtageerfassung(Vorgang $vorgang, ExternalIdMappingService $idMappingService): RedirectResponse
    {
        $vorgang->loadMissing(['kunde', 'owner']);

        $params = sprintf(
            '?CourtageData[user_id]=%s',
            $vorgang->kunde->user_external_id ?? $vorgang->owner->external_id,
        );

        if ($vorgang->kunde?->external_id !== null) {
            $params .= sprintf(
                '&CourtageData[client_id]=%s',
                $vorgang->kunde->external_id,
            );
        }

        if ($vorgang->gesellschaft_id !== null) {
            $params .= sprintf(
                '&CourtageData[insurance_company_id]=%s',
                $idMappingService->getExternalId($vorgang->gesellschaft_id, Gesellschaft::class),
            );
        }

        if ($vorgang->sparte_id !== null) {
            $params .= sprintf(
                '&CourtageData[product_combo_id]=%s',
                $vorgang->sparte_id,
            );
        }

        return redirect()->to(sprintf(
            '%s%s%s',
            config('professionalworks_sdk.baseUrl'),
            self::COURTAGEERFASSUNG_BASE_URL,
            $params,
        ));
    }

    public function generateMaklerauftrag(Kunde $kunde, ExternalIdMappingService $idMappingService): RedirectResponse
    {
        $params = '?documentTypeId=56'; // maklerauftrag

        $params .= sprintf(
            '&clientId=%s',
            $idMappingService->getExternalId($kunde->id, Kunde::class),
        );

        return redirect()->to(sprintf(
            '%s%s%s',
            config('professionalworks_sdk.baseUrl'),
            self::GENERATE_MAKLERAUFTRAG_URL,
            $params,
        ));
    }

    public function schadenerfassung(Vorgang $vorgang): RedirectResponse
    {
        if ($vorgang->kunde === null) {
            abort(400, 'Dem Vorgang ist kein Kunde zugeordnet.');
        }

        $params = '';
        $vertrag = $vorgang->vertraege->first();

        if ($vertrag !== null) {
            $params .= sprintf(
                '?contract_id=%s',
                $vertrag->external_id,
            );
        }

        $baseUrl = sprintf(self::SCHADENERFASSUNG_BASE_URL, $vorgang->kunde->external_id);

        return redirect()->to(sprintf(
            '%s%s%s',
            config('professionalworks_sdk.baseUrl'),
            $baseUrl,
            $params,
        ));
    }

    public function bestandsuebertragung(?Kunde $kunde): RedirectResponse
    {
        $params = '';
        if ($kunde !== null) {
            $params .= sprintf(
                '?client_id=%s',
                $kunde->external_id,
            );
        }

        return redirect()->to(sprintf(
            '%s%s%s',
            config('professionalworks_sdk.baseUrl'),
            self::BESTANDSUEBERTRAGUNG_BASE_URL,
            $params,
        ));
    }
}
