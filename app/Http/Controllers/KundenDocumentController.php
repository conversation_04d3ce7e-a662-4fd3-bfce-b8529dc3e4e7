<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\DokumentTyp;
use App\Models\Gesellschaft;
use App\Models\Kunde;
use App\Models\Sparte;
use App\Models\Vertrag;
use App\Services\KundenDokumentService;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\Document as PwKundenDocument;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\LazyCollection;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;

class KundenDocumentController extends Controller
{
    private const PAGE_SIZE = 50;

    public function __construct(readonly private KundendoKumentService $kundenDokumentService)
    {
    }

    public function index(
        Kunde $kunde,
        Request $request,
    ): array {
        $gesellschaft = $request->input('gesellschaft');
        $vertrag = $request->input('vertrag');
        $dokumentTypName = $request->input('dokumentTyp');
        $query = $request->input('query');

        $offset = (int) $request->input('offset');

        try {
            $documents = $this->kundenDokumentService->getDocuments(
                $kunde,
                self::PAGE_SIZE,
                $offset,
                $query ?? '',
                $gesellschaft,
                $vertrag,
                $dokumentTypName
            );

            $documentDtos = self::mapDocumentsToDtos($documents, $kunde);
        } catch (\Throwable $exception) {
            Log::notice('Could not fetch kundenDocuments', [
                'exception' => $exception,
                'kundeId' => $kunde->external_id,
                'userId' => Auth::user()?->external_id,
            ]);

            abort(Response::HTTP_NOT_FOUND);
        }

        return [
            'data' => $documentDtos,
            'links' => [
                'prev' => $offset === 0
                    ? null
                    : $request->fullUrlWithQuery([
                        'offset' => $offset - self::PAGE_SIZE,
                    ]),
                'next' => count($documentDtos) < self::PAGE_SIZE
                    ? null
                    : $request->fullUrlWithQuery([
                        'offset' => $offset + self::PAGE_SIZE,
                    ]),
            ],
            'meta' => [
                'per_page' => self::PAGE_SIZE,
            ],
        ];
    }

    public function show(
        Kunde $kunde,
        int $documentExternalId,
    ): array {
        $pwDocument = $this->kundenDokumentService->getDocument($kunde, $documentExternalId);

        $gesellschaft = null;
        $sparte = null;
        $vertrag = null;
        $dokumentTyp = null;

        if ($pwDocument->gesellschaftId !== null) {
            $gesellschaft = Gesellschaft::query()
                ->where('external_id', $pwDocument->gesellschaftId)
                ->first(['external_id', 'abkuerzung']);
        }

        if ($pwDocument->sparteId !== null) {
            /** @var Sparte|null $sparte */
            $sparte = Sparte::query()->find($pwDocument->sparteId);
        }

        if ($pwDocument->vertragId !== null) {
            /** @var Vertrag|null $vertrag */
            $vertrag = Vertrag::query()->where('external_id', $pwDocument->vertragId)->first();
        }

        if ($pwDocument->documentTypeId !== null) {
            /** @var DokumentTyp|null $dokumentTyp */
            $dokumentTyp = DokumentTyp::query()->find($pwDocument->documentTypeId);
        }

        return [
            'data' => self::pwDocumentToDto($pwDocument, $kunde, $dokumentTyp, $gesellschaft, $sparte, $vertrag),
        ];
    }

    public function download(
        Kunde $kunde,
        int $documentExternalId,
    ): RedirectResponse {
        $document = $this->kundenDokumentService->getDocument($kunde, $documentExternalId);

        Log::info('Downloading file from redirect url', [
            'kunde' => $kunde->external_id,
            'document' => $documentExternalId,
            'url' => $document->url,
        ]);

        if ($document->url === null) {
            throw new RuntimeException('DocumentUrl not found');
        }

        return redirect()->to($document->url);
    }

    /**
     * @param LazyCollection<PwKundenDocument> $documents
     */
    private static function mapDocumentsToDtos(LazyCollection $documents, Kunde $kunde): array
    {
        $sparten = Sparte::query()
            ->get(['id', 'display_name']);
        $vertraege = Vertrag::query()->whereBelongsTo($kunde)
            ->get(['id', 'external_id', 'gesellschaft_external_id']);
        $dokumentTypen = DokumentTyp::query()->where('is_for_kunde', '=', true)
            ->get(['id', 'name']);
        $gesellschaften = Gesellschaft::query()
            ->whereIn('external_id', $vertraege->pluck('gesellschaft_external_id'))
            ->get(['id', 'external_id', 'abkuerzung']);

        $documentDtos = $documents->map(
            static function (PwKundenDocument $doc) use (
                $kunde,
                $sparten,
                $vertraege,
                $dokumentTypen,
                $gesellschaften,
            ): array {
                $gesellschaft = null;
                $sparte = null;
                $vertrag = null;
                $dokumentTyp = null;

                if ($doc->gesellschaftId !== null) {
                    /** @var Gesellschaft|null $gesellschaft */
                    $gesellschaft = $gesellschaften->where('external_id', $doc->gesellschaftId)->first();

                    if ($gesellschaft === null) {
                        $gesellschaft = Gesellschaft::query()
                            ->where('external_id', $doc->gesellschaftId)
                            ->first(['external_id', 'abkuerzung']);

                        if ($gesellschaft !== null) {
                            $gesellschaften->add($gesellschaft);
                        }
                    }
                }

                if ($doc->sparteId !== null) {
                    /** @var Sparte|null $sparte */
                    $sparte = $sparten->find($doc->sparteId);
                }

                if ($doc->vertragId !== null) {
                    /** @var Vertrag|null $vertrag */
                    $vertrag = $vertraege->where('external_id', $doc->vertragId)->first();
                }

                if ($doc->documentTypeId !== null) {
                    /** @var DokumentTyp|null $dokumentTyp */
                    $dokumentTyp = $dokumentTypen->find($doc->documentTypeId);
                }

                return self::pwDocumentToDto($doc, $kunde, $dokumentTyp, $gesellschaft, $sparte, $vertrag);
            },
        );

        return $documentDtos->take(self::PAGE_SIZE)->all();
    }

    private static function pwDocumentToDto(
        PwKundenDocument $pwDocument,
        Kunde $kunde,
        ?DokumentTyp $dokumentTyp,
        ?Gesellschaft $gesellschaft,
        ?Sparte $sparte,
        ?Vertrag $vertrag,
    ): array {
        return [
            'type' => $pwDocument::class,
            'id' => (string) $pwDocument->id,
            'attributes' => [
                'name' => $pwDocument->name,
                'size' => $pwDocument->size,
                'extension' => $pwDocument->extension,
                'url' => route('documents.download', [
                    'kunde' => $kunde->id,
                    'document' => $pwDocument->id,
                ]),
                'vertragId' => $vertrag?->id === null ? null : (string) $vertrag->id,
                'gesellschaftId' => $gesellschaft?->id === null ? null : (string) $gesellschaft->id,
                'gesellschaftName' => $gesellschaft?->abkuerzung,
                'sparteName' => $sparte?->display_name,
                'dokumentTypName' => $dokumentTyp?->name,
                'createdAt' => $pwDocument->createdAt?->toIso8601String(),
            ],
        ];
    }
}
