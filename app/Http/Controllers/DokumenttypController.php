<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\DokumentTyp;
use App\Support\JsonApi\JsonApiQueryBuilder;
use App\Support\JsonApi\Resources\JsonApiCollection;

class DokumenttypController
{
    public function index(JsonApiQueryBuilder $queryBuilder): JsonApiCollection
    {
        $query = $queryBuilder->build(new DokumentTyp());

        return new JsonApiCollection($query->get());
    }
}
