<?php

declare(strict_types=1);

namespace App\Gateway;

use GuzzleHttp\Psr7\Response;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

class ApiManagerGateway implements GatewayInterface
{
    /**
     * @throws ConnectionException
     */
    public function request(
        string $method,
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throwHttpError = false,
    ): ResponseInterface {
        try {
            $url = $this->buildUrl($uri);
            $headers = config('api_manager.headers');
            $timeout = config('api_manager.timeout', 30);

            $httpClient = Http::withHeaders($headers)->timeout($timeout);

            if ($throwHttpError) {
                $httpClient = $httpClient->throw();
            }

            $response = match (strtoupper($method)) {
                'GET' => $httpClient->get($url, $requestOptions['query'] ?? []),
                'POST' => $httpClient->post($url, $requestOptions),
                'PUT' => $httpClient->put($url, $requestOptions),
                'DELETE' => $httpClient->delete($url),
                default => throw new \InvalidArgumentException("Unsupported HTTP method: {$method}")
            };

            return new Response(status: $response->status(), body: $response->body());

        } catch (ConnectionException $e) {
            $this->logException('Connection failed', $method, $uri, $e);
            return new Response(503, body: 'Service temporarily unavailable: ' . $e->getMessage());

        } catch (RequestException $e) {
            $this->logException('HTTP error', $method, $uri, $e);
            $statusCode = $e->response !== null ? $e->response->status() : 500;
            $body = $e->response !== null ? $e->response->body() : 'HTTP error: ' . $e->getMessage();
            return new Response($statusCode, body: $body);

        } catch (\Exception $e) {
            $this->logException('Unexpected error', $method, $uri, $e);
            return new Response(500, body: 'Internal server error: ' . $e->getMessage());
        }
    }

    /**
     * @throws ConnectionException
     */
    public function get(
        string $uri,
        array $params = [],
        array $middlewares = [],
        bool $throw = false,
    ): ResponseInterface {
        $requestOptions = ['query' => $params];
        return $this->request('GET', $uri, $requestOptions, $middlewares, $throw);
    }

    /**
     * @throws ConnectionException
     */
    public function post(
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throw = false,
    ): ResponseInterface {
        return $this->request('POST', $uri, $requestOptions, $middlewares, $throw);
    }

    /**
     * @throws ConnectionException
     */
    public function put(
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throw = false,
    ): ResponseInterface {
        return $this->request('PUT', $uri, $requestOptions, $middlewares, $throw);
    }

    /**
     * @throws ConnectionException
     */
    public function delete(string $endpoint): ResponseInterface
    {
        return $this->request('DELETE', $endpoint);
    }

    public function getBaseUrl(): string
    {
        return config('api_manager.base_url', '');
    }

    private function buildUrl(string $uri): string
    {
        $baseUrl = $this->getBaseUrl();

        if ($baseUrl === '') {
            return $uri;
        }

        if ($uri === '') {
            return $baseUrl;
        }

        return rtrim($baseUrl, '/') . '/' . ltrim($uri, '/');
    }

    private function logException(string $context, string $method, string $endpoint, \Exception $e): void
    {
        Log::error('API Manager: ' . $context, [
            'method' => $method,
            'endpoint' => $endpoint,
            'exception' => $e,
        ]);
    }
}
