<?php

declare(strict_types=1);

namespace App\Gateway;

use Psr\Http\Message\ResponseInterface;

interface GatewayInterface
{
    /**
     * @param callable[] $middlewares
     * @see https://docs.guzzlephp.org/en/stable/request-options.html
     */
    public function request(
        string $method,
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throwHttpError = false,
    ): ResponseInterface;

    public function get(
        string $uri,
        array $params = [],
        array $middlewares = [],
        bool $throw = false,
    ): ResponseInterface;

    public function post(
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throw = false,
    ): ResponseInterface;

    public function put(
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throw = false,
    ): ResponseInterface;

    public function getBaseUrl(): string;
}
