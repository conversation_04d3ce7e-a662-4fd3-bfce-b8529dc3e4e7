<?php

declare(strict_types=1);

namespace App\Gateway;

use App\Exceptions\GatewayException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\RequestOptions;
use Psr\Http\Message\ResponseInterface;

class MailerGateway implements GatewayInterface
{
    protected Client $client;

    public function __construct(
        protected string $baseUrl,
        protected string $token,
        protected string $origin,
        protected array $options = [],
    ) {
        $this->options[RequestOptions::TIMEOUT] = 60;
        $this->options[RequestOptions::HEADERS] = [
            'Accept' => 'application/json',
            'demv-origin' => $this->origin,
        ];


        $this->options['headers']['Authorization'] = trim('Bearer ' . $this->token);
        $this->options['base_uri'] = $this->baseUrl;

        $this->options = array_merge($this->options, $options);

        $this->client = new Client($this->options);
    }

    /**
     * @param string $method
     * @param string $uri
     * @param array $requestOptions
     * @param callable[] $middlewares
     * @param bool $throwHttpError
     * @return ResponseInterface
     * @throws GatewayException
     * @throws GuzzleException
     * @see https://docs.guzzlephp.org/en/stable/request-options.html
     */
    public function request(
        string $method,
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throwHttpError = false,
    ): ResponseInterface {
        $requestOptions['http_errors'] = $throwHttpError;
        $options = array_merge($this->options, $requestOptions);

        $stack = HandlerStack::create();

        foreach ($middlewares as $middleware) {
            $stack->push($middleware);
        }

        $options['handler'] = $stack;

        try {
            $response = $this->client->request($method, $uri, $options);
        } catch (ClientException $clientException) {
            throw new GatewayException(
                'HTTP Error during Mailer Gateway call',
                $clientException->getCode(),
                $clientException
            );
        }

        return $response;
    }

    /**
     * @throws GuzzleException
     * @throws GatewayException
     */
    public function get(
        string $uri,
        array $params = [],
        array $middlewares = [],
        bool $throw = false,
    ): ResponseInterface {
        return $this->request(
            'get',
            $uri,
            [
                'query' => $params,
            ],
            $middlewares,
            $throw,
        );
    }

    /**
     * @throws GuzzleException
     * @throws GatewayException
     */
    public function post(
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throw = false,
    ): ResponseInterface {
        return $this->request('post', $uri, $requestOptions, $middlewares, $throw);
    }

    /**
     * @throws GuzzleException
     * @throws GatewayException
     */
    public function put(
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throw = false,
    ): ResponseInterface {
        return $this->request('put', $uri, $requestOptions, $middlewares, $throw);
    }

    public function getBaseUrl(): string
    {
        return $this->baseUrl;
    }
}
