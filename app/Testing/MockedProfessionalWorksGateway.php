<?php

declare(strict_types=1);

namespace App\Testing;

use App\Testing\Fixtures\ProfessionalWorks\AgencyForUserResponse;
use App\Testing\Fixtures\ProfessionalWorks\AgencyImage;
use App\Testing\Fixtures\ProfessionalWorks\AgencyResponse;
use App\Testing\Fixtures\ProfessionalWorks\AllAgenciesResponse;
use App\Testing\Fixtures\ProfessionalWorks\AnsprechpartnerResponse;
use App\Testing\Fixtures\ProfessionalWorks\AvailableTagsResponse;
use App\Testing\Fixtures\ProfessionalWorks\BriefprofilResponse;
use App\Testing\Fixtures\ProfessionalWorks\DokumentTypenResponse;
use App\Testing\Fixtures\ProfessionalWorks\DuplicateMockResponse;
use App\Testing\Fixtures\ProfessionalWorks\EmailSuggestionsResponse;
use App\Testing\Fixtures\ProfessionalWorks\GesellschaftenResponse;
use App\Testing\Fixtures\ProfessionalWorks\KundenResponse;
use App\Testing\Fixtures\ProfessionalWorks\KundenSalutation;
use App\Testing\Fixtures\ProfessionalWorks\MissingAddressResponse;
use App\Testing\Fixtures\ProfessionalWorks\ParentsResponse;
use App\Testing\Fixtures\ProfessionalWorks\ReceiverAddressResponse;
use App\Testing\Fixtures\ProfessionalWorks\SchaedenResponse;
use App\Testing\Fixtures\ProfessionalWorks\SenderAddressResponse;
use App\Testing\Fixtures\ProfessionalWorks\SpartenResponse;
use App\Testing\Fixtures\ProfessionalWorks\UnderlingsResponse;
use App\Testing\Fixtures\ProfessionalWorks\UserForFirmaResponse;
use App\Testing\Fixtures\ProfessionalWorks\UserResponse;
use App\Testing\Fixtures\ProfessionalWorks\UserRightsResponse;
use App\Testing\Fixtures\ProfessionalWorks\VertraegeResponse;
use App\Testing\Fixtures\ProfessionalWorks\VertragsStatusResponse;
use Demv\SdkFramework\Gateway\GatewayInterface;

class MockedProfessionalWorksGateway extends MockedGateway implements GatewayInterface
{
    protected function getMockedResponses(): array
    {
        return [
            AgencyResponse::class,
            AllAgenciesResponse::class,
            AgencyForUserResponse::class,
            AvailableTagsResponse::class,
            AnsprechpartnerResponse::class,
            BriefprofilResponse::class,
            DokumentTypenResponse::class,
            GesellschaftenResponse::class,
            KundenResponse::class,
            SpartenResponse::class,
            UnderlingsResponse::class,
            ParentsResponse::class,
            SenderAddressResponse::class,
            ReceiverAddressResponse::class,
            UserResponse::class,
            UserForFirmaResponse::class,
            UserRightsResponse::class,
            VertraegeResponse::class,
            KundenSalutation::class,
            VertragsStatusResponse::class,
            SchaedenResponse::class,
            DuplicateMockResponse::class,
            EmailSuggestionsResponse::class,
            AgencyImage::class,
            MissingAddressResponse::class,
        ];
    }
}
