<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ApiManager;

use App\Testing\Fixtures\AbstractMockedResponse;

class VertriebswegResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return preg_match('#/api/v1/\d+/broker-nrs\?insurance_company_ids\[0]=\d+#', $url) === 1;
    }

    public static function getMockedResponseBody(string $url): string
    {
        preg_match('#/api/v1/(?\'user_id\'\d+)/broker-nrs#', $url, $matches);
        $userId = $matches['user_id'] ?? '';

        preg_match('#insurance_company_ids\[0]=(?\'company_id\'\d+)#', $url, $matches);
        $companyId = $matches['company_id'] ?? '';

        return <<<JSON
        {
          "data": []
        }
        JSON;
    }
}
