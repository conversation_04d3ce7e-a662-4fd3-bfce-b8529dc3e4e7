<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class AvailableTagsResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/api/Tags/Available';
    }

    public static function getMockedResponseBody(string $url): string
    {
        return '{"status":"success","data":[{"tag":"datum","description":"Aktuelles Datum","example":"16.09.2021","type":"text"},{"tag":"kunde:email","description":"E-Mail-Adresse des Kunden","example":"<EMAIL>","type":"text"},{"tag":"kunde:nummer","description":"Interne Kundennummer","example":"x125 b756","type":"text"},{"tag":"kunde:bankverbindung","description":"Bankverbindung des Kunden","example":"Dagobert Duck, IBAN: ****************, BIC: ENTHAUS1001","type":"text"},{"tag":"kunde:adresse","description":"Anschrift des Kunden","example":"Kundenstr. 23a - 01234 Kundenstadt","type":"text"},{"tag":"kunde:alter","description":"Alter des Kunden in Jahren","example":"42","type":"text"},{"tag":"kunde:name","description":"Vollst\u00e4ndiger Name des Kunden","example":"Otto Normalkunde","type":"text"},{"tag":"kunde:geburtsdatum","description":"Geburtsdatum des Kunden","example":"21.02.1985","type":"text"},{"tag":"kunde:anrede-1","description":"Die Anrede an den Kunden","example":"Sehr geehrte\/r | Liebe\/r ...","type":"text"},{"tag":"kunde:anrede-2","description":"Die Anrede an den Kunden","example":"Guten Tag | Hallo ...","type":"text"},{"tag":"kunde:unterschrift","description":"Unterschriftenlinie mit Name des Kunden zum Signieren","example":"<br \/><br \/><br \/>_________________________________________<br \/>Max Musterkunde","type":"html"},{"tag":"makler:ihk:regnr34d","description":"IHK Reg. Nr. nach \u00a734d","example":"D-XXXX-XXXXX-XX","type":"text"},{"tag":"makler:ihk:regnr34f","description":"IHK Reg. Nr. nach \u00a734f","example":"D-XXXX-XXXXX-XX","type":"text"},{"tag":"makler:ihk:regnr34i","description":"IHK Reg. Nr. nach \u00a734i","example":"D-XXXX-XXXXX-XX","type":"text"},{"tag":"makler:name","description":"Vollst\u00e4ndiger Name des Maklers","example":"Max Mustermann","type":"text"},{"tag":"makler:signatur:duzend","description":"Duzende Maklersignatur","example":"","type":"html"},{"tag":"makler:signatur:siezend","description":"Siezende Maklersignatur","example":"","type":"html"},{"tag":"makler:firma:name","description":"Name der Maklerfirma","example":"ACME Versicherungsberater","type":"text"},{"tag":"makler:adresse","description":"Makleranschrift","example":"Quackstr. 3 - 1234 Entenhausen","type":"text"},{"tag":"makler:anrede","description":"Die Anrede an den Makler","example":"Sehr geehrte Frau M\u00fcller","type":"text"},{"tag":"makler:unterschrift","description":"Unterschrift","example":"","type":"html"},{"tag":"gesellschaft:name","description":"Der Name der Gesellschaft","example":"ABC Versicherungen AG","type":"text"},{"tag":"gesellschaft:ansprechpartner:anrede","description":"Ansprechpartner bei der Gesellschaft f\u00fcr die Sparte","example":"Sehr geehrte Damen und Herren,","type":"text"},{"tag":"gesellschaft:adresse","description":"Gesellschaftsanschrift","example":"Gesellschaftsstr. 21b - 52064 Stadt","type":"text"},{"tag":"gesellschaft:vermittlernummer","description":"Hauptnummer","example":"AO123345","type":"text"},{"tag":"gesellschaft:nebennummer","description":"Nebennummer","example":"AO123345","type":"text"},{"tag":"sparte","description":"Sparte des aktuellen Vorganges","example":"Wassersportkasko","type":"text"},{"tag":"vertrag:ablaufdatum","description":"Ablaufdatum","example":"21.08.2042","type":"text"},{"tag":"vertrag:gesellschaft:name","description":"Der Name der Gesellschaft","example":"ABC Versicherungen AG","type":"text"},{"tag":"vertrag:gesellschaft:ansprechpartner:anrede","description":"Ansprechpartner bei der Gesellschaft f\u00fcr die Sparte","example":"Sehr geehrte Damen und Herren,","type":"text"},{"tag":"vertrag:gesellschaft:adresse","description":"Gesellschaftsanschrift","example":"Gesellschaftsstr. 21b - 52064 Stadt","type":"text"},{"tag":"vertrag:gesellschaft:vermittlernummer","description":"Hauptnummer","example":"AO123345","type":"text"},{"tag":"vertrag:gesellschaft:nebennummer","description":"Nebennummer","example":"AO123345","type":"text"},{"tag":"vertrag:vertragsnummer","description":"Die Vertragsnummern aller angegebenen Vertr\u00e4ge, kommasepariert","example":"ABC-1234","type":"text"},{"tag":"vertrag:beitrag:netto","description":"Die Nettobeitr\u00e4ge aller angegebenen Vertr\u00e4ge, kommasepariert","example":"50,49","type":"text"},{"tag":"vertrag:beitrag:brutto","description":"Die Bruttobeitr\u00e4ge aller angegebenen Vertr\u00e4ge, kommasepariert","example":"50,49","type":"text"},{"tag":"vertrag:sparte","description":"Sparte","example":"Wassersportkasko, Tier-Operations-Versicherung - TOV","type":"text"},{"tag":"vertrag:kfz:kennzeichen","description":"Kennzeichen","example":"RA KL 8136","type":"text"},{"tag":"schaden:nummer","description":"Schadennummer","example":"X12134","type":"text"}]}';
    }
}
