<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class AnsprechpartnerResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return (bool) preg_match('#/stammdaten/api/ansprechpartner/GetAnsprechpartner#', $url);
    }

    public static function getMockedResponseBody(string $url): string
    {
        return <<<JSON
        {
          "status": "success",
          "data": [
            {
              "id": 1,
              "email": "<EMAIL>",
              "firstname": "<PERSON>",
              "lastname": "Sprä<PERSON>",
              "sex_descriptor": "Frau",
              "fax": "030 538387321",
              "phone_business": "030 53837321",
              "phone_mobile": "0173 3589607",
              "homepage": "http://",
              "address": "An den Treptowers 3 - 12435 Berlin",
              "department": "Maklerbetreuung"
            }
          ]
        }
        JSON;
    }
}
