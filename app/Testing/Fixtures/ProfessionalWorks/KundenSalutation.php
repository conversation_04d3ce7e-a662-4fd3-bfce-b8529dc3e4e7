<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\KundeSalutation;

class KundenSalutation extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return preg_match('/api\/stammdaten\/client\/\d+\/salutation/', $url) === 1;
    }

    public static function getMockedResponseBody(string $url): string
    {
        $json_encode = json_encode([
            'status' => 'success',
            'data' => [
                'salutation' => 'Hallo Max Mustermann',
                'salutation_variation' => 'Moin <PERSON>',
                'salutation_type' => KundeSalutation::MR,
            ],
        ]);

        if (is_string($json_encode)) {
            return $json_encode;
        }

        throw new \Exception('Could not create Mock Request from JSON');
    }
}
