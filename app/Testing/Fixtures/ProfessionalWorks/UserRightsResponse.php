<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class UserRightsResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/api/stammdaten/user/1/rights';
    }

    public static function getMockedResponseBody(string $url): string
    {
        return '{"status":"success","data":{"rights":[45,22,19,20,12,48,37,38,39,36,34,29,13,6,5,14,15,16,17,18,35,46,49,50,7,8,9,33,30,10,32,47,11,27,24,31,25,26,23,41,42,43,40,44,21,28]}}';
    }
}
