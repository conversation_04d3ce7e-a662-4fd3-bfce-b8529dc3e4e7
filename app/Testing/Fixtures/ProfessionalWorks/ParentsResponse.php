<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class ParentsResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/api/stammdaten/user/1/hierarchy/parents';
    }

    public static function getMockedResponseBody(string $url): string
    {
        return '{"status":"success","data":[]}';
    }
}
