<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class EmailSuggestionsResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return preg_match('/\/api\/mailer\/forUser\/\d+\/suggestions/', $url) === 1;
    }

    public static function getMockedResponseBody(string $url): string
    {
        return <<<JSON
            {
              "status": "success",
              "data": [
                {
                  "context": "user",
                  "label": "Vermittler",
                  "email": "<EMAIL>",
                  "name": "Irgendein Typ"
                }
              ]
            }
        JSON;
    }
}
