<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class SchaedenResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/api/bestandsdaten/schaeden';
    }

    public static function getMockedResponseBody(string $url): string
    {
        $json = json_encode([
            'status' => 'success',
            'data' => [
                'limit' => 100,
                'offset' => 0,
                'count' => 1,
                'data' => [
                    [
                        'schadenNummer' => 'Schaden-13485',
                        'vertragId' => '13485',
                        'sparteId' => '171',
                        'kundeId' => '2',
                        'gesellschaftId' => '156',
                        'vertriebswegId' => '156',
                        'userId' => '1',
                        'agencyId' => '1',
                        'updatedAt' => '2021-09-13 10:34:49',
                        'damage' => '1',
                        'status' => '1',
                        'kfzKennzeichen' => '',
                    ],
                ],
            ],
        ]);

        if (is_string($json)) {
            return $json;
        }

        throw new \Exception('Could not create Mock Request from JSON');
    }
}
