<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class BriefprofilResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/api/profil/briefprofil/userId/1';
    }

    public static function getMockedResponseBody(string $url): string
    {
        return <<<JSON
          {
            "status": "success",
            "data": {
              "id": null,
              "userId": 1,
              "paddingTop": 4.9,
              "paddingLeft": 2,
              "paddingRight": 2,
              "hasFooter": true,
              "hasSender": true
            }
          }
        JSON;
    }
}
