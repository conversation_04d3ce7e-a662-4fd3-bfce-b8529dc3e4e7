<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class AllAgenciesResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/stammdaten/api/agency/firmen';
    }

    public static function getMockedResponseBody(string $url): string
    {
        return <<<JSON
        {
          "status": "success",
          "data": {
          "limit": null,
          "offset": null,
          "count": 1,
          "data": [{
            "id": "1",
            "name": "DEMV-System",
            "legalForm": null,
            "ceo": null,
            "commercialRegisterNr": "",
            "registerCourt": null,
            "landline": "123 456 789",
            "fax": null,
            "mobile": null,
            "mail": null,
            "street": "Systemstraße",
            "houseNr": "1",
            "zipcode": "20095",
            "city": "Hamburg",
            "lastEditDatetime":	"2023-02-23 09:01:29"
          }]
          }
        }
        JSON;
    }
}
