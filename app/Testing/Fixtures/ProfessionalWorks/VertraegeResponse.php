<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class VertraegeResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/api/bestandsdaten/vertraege';
    }

    public static function getMockedResponseBody(string $url): string
    {
        $json_encode = json_encode([
            'status' => 'success',
            'data' =>
                [
                    'limit' => 100,
                    'offset' => 0,
                    'count' => 5,
                    'data' => [
                        [
                            'vertragsnummer' => '123456777 - Gemeinsames KFZ MUSTER',
                            'vertragId' => '209898',
                            'sparteId' => '171',
                            'kundeId' => '2',
                            'agencyId' => '1',
                            'gesellschaftId' => '156',
                            'vertriebswegId' => '325',
                            'userId' => '1',
                            'updatedAt' => '2021-09-13 10:34:49',
                            'damage' => '0',
                            'status' => '1',
                            'kfzKennzeichen' => 'DE-MV-666',
                            'mitversicherteKundenIds' => json_encode([1]),
                        ], [
                            'vertragsnummer' => 'Bank Produkt Herr UND Frau Musterfamilie',
                            'vertragId' => '209899',
                            'sparteId' => '202',
                            'kundeId' => '1',
                            'agencyId' => '1',
                            'gesellschaftId' => '56',
                            'vertriebswegId' => '56',
                            'userId' => '1',
                            'updatedAt' => '2021-07-15 07:52:31',
                            'damage' => '0',
                            'status' => '1',
                            'kfzKennzeichen' => '',
                            'mitversicherteKundenIds' => json_encode([2]),
                        ], [
                            'vertragsnummer' => 'Herr Muster',
                            'vertragId' => '209900',
                            'sparteId' => '479',
                            'kundeId' => '1',
                            'agencyId' => '1',
                            'gesellschaftId' => '325',
                            'vertriebswegId' => '325',
                            'userId' => '1',
                            'updatedAt' => '2021-07-26 06:46:12',
                            'damage' => '0',
                            'status' => '1',
                            'kfzKennzeichen' => '',
                            'mitversicherteKundenIds' => json_encode([]),
                        ], [
                            'vertragsnummer' => 'Frau Bavarais Vertrag',
                            'vertragId' => '209901',
                            'sparteId' => '479',
                            'kundeId' => '3',
                            'agencyId' => '1',
                            'gesellschaftId' => '325',
                            'vertriebswegId' => '325',
                            'userId' => '1',
                            'updatedAt' => '2021-07-26 06:46:12',
                            'damage' => '0',
                            'status' => '1',
                            'kfzKennzeichen' => '',
                            'mitversicherteKundenIds' => json_encode([]),
                        ], [
                            'vertragsnummer' => 'swimming pool',
                            'vertragId' => '209902',
                            'sparteId' => '171',
                            'kundeId' => '2',
                            'agencyId' => '1',
                            'gesellschaftId' => '325',
                            'vertriebswegId' => null,
                            'userId' => '1',
                            'updatedAt' => '2021-09-13 10:34:49',
                            'damage' => '0',
                            'status' => '1',
                            'kfzKennzeichen' => '',
                            'mitversicherteKundenIds' => json_encode([1]),
                        ],
                    ],
                ],
        ]);

        if (is_string($json_encode)) {
            return $json_encode;
        }

        throw new \Exception('Could not create Mock Request from JSON');
    }
}
