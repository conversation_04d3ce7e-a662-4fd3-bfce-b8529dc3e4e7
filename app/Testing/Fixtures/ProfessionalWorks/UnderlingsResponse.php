<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class UnderlingsResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/api/stammdaten/user/1/hierarchy/underlings';
    }

    public static function getMockedResponseBody(string $url): string
    {
        return '{"status":"success","data":["1","2","3","4","5"]}';
    }
}
