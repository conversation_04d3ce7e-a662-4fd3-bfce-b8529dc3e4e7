<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class UserForFirmaResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/api/stammdaten/firma/1/user';
    }

    public static function getMockedResponseBody(string $url): string
    {
        return '{"status":"success","data":[
        {"id":1,"firstName":"Tester","lastName":"1","fullName":"Tester 1","agencyId":1,"userRole":4,"userStatus":1,"lastEditDatetime":"01.01.2022 12:00:00"},
        {"id":2,"firstName":"Tester","lastName":"2","fullName":"Tester 2","agencyId":1,"userRole":5,"userStatus":1,"lastEditDatetime":"01.01.2022 12:00:00"},
        {"id":3,"firstName":"Tester","lastName":"3","fullName":"Tester 3","agencyId":1,"userRole":5,"userStatus":1,"lastEditDatetime":"01.01.2022 12:00:00"},
        {"id":4,"firstName":"Tester","lastName":"4","fullName":"Tester 4","agencyId":1,"userRole":6,"userStatus":1,"lastEditDatetime":"01.01.2022 12:00:00"},
        {"id":5,"firstName":"Tester","lastName":"5","fullName":"Tester 5","agencyId":1,"userRole":6,"userStatus":1,"lastEditDatetime":"01.01.2022 12:00:00"}
        ]}';
    }
}
