<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class MissingAddressResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_POST;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return str_contains($url, '/api/bestandsdaten/missingaddress');
    }

    public static function getMockedResponseBody(string $url): string
    {
        return '{"data": ["1"]}';
    }
}
