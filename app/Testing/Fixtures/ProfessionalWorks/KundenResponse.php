<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class KundenResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/api/bestandsdaten/kunden';
    }

    public static function getMockedResponseBody(string $url): string
    {
        $json_encode = json_encode([
            'status' => 'success',
            'data' => [
                'limit' => 100,
                'offset' => 0,
                'count' => 6,
                'data' => [
                    [
                        'firstName' => 'Max',
                        'lastName' => 'Mustermann',
                        'title' => null,
                        'kundeId' => '1',
                        'email' => '<EMAIL>',
                        'userId' => '1',
                        'informal' => '1',
                        'salutationId' => '1',
                        'agencyId' => '1',
                        'updatedAt' => '2021-08-1209:56:16',
                    ],
                    [
                        'firstName' => 'Maria',
                        'lastName' => 'Musterfrau',
                        'title' => null,
                        'kundeId' => '2',
                        'email' => '<EMAIL>',
                        'userId' => '1',
                        'informal' => '1',
                        'salutationId' => '2',
                        'agencyId' => '1',
                        'updatedAt' => '2021-08-1209:56:16',
                    ],
                    [
                        'firstName' => 'Gabriela',
                        'lastName' => 'Bavarai',
                        'title' => null,
                        'kundeId' => '3',
                        'email' => '<EMAIL>',
                        'userId' => '2',
                        'informal' => '0',
                        'salutationId' => '2',
                        'agencyId' => '1',
                        'updatedAt' => '2021-08-1209:56:16',
                    ],
                    [
                        'firstName' => 'Mustafa',
                        'lastName' => 'Hinrichs',
                        'title' => null,
                        'kundeId' => '4',
                        'email' => '<EMAIL>',
                        'userId' => '2',
                        'informal' => '0',
                        'salutationId' => '1',
                        'agencyId' => '1',
                        'updatedAt' => '2021-08-1209:56:16',
                    ],
                    [
                        'firstName' => 'Bert',
                        'lastName' => 'Mayer',
                        'title' => null,
                        'kundeId' => '5',
                        'email' => '<EMAIL>',
                        'userId' => '3',
                        'informal' => '0',
                        'salutationId' => '2',
                        'agencyId' => '1',
                        'updatedAt' => '2021-08-1209:56:16',
                    ],
                    [
                        'firstName' => 'Ernie',
                        'lastName' => 'Mayer',
                        'title' => null,
                        'kundeId' => '6',
                        'email' => '<EMAIL>',
                        'userId' => '3',
                        'informal' => '0',
                        'salutationId' => '1',
                        'agencyId' => '1',
                        'updatedAt' => '2021-08-1209:56:16',
                    ],
                ],
            ],
        ]);

        if (is_string($json_encode)) {
            return $json_encode;
        }

        throw new \Exception('Could not create Mock Request from JSON');
    }
}
