<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class SenderAddressResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return (bool) preg_match('#/api/mailer/forUser/\d+/senderAddress#', $url);
    }

    public static function getMockedResponseBody(string $url): string
    {
        preg_match('#/api/mailer/forUser/(?\'id\'\d+)/senderAddress#',$url, $matches);
        $idSuffix = $matches['id'] ?? '';

        return <<<JSON
        {
          "status": "success",
          "data": {
            "email": "sender{$idSuffix}@demv.de",
            "name": "DEMV Tester"
          }
        }
        JSON;
    }
}
