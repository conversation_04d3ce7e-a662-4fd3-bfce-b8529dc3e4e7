<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class DuplicateMockResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return str_contains($url, 'api/duplicate');

    }

    public static function getMockedResponseBody(string $url): string
    {
        $type = null;

        if (str_contains($url, 'makler')) {
            $type = 'makler';
        }

        if (str_contains($url, 'kunde')) {
            $type = 'kunde';
        }

        if (str_contains($url, 'vertrag')) {
            $type = 'vertrag';
        }

        $json_encode = json_encode([
            'status' => 'success',
            'data' => [
                'requested_type' => $type,
                'agency_id' => (int) substr($url, (int) strripos($url, '/') + 1, strlen($url)),
                'duplicates' => [],
            ],
        ]);

        if (is_string($json_encode)) {
            return $json_encode;
        }

        throw new \Exception('Could not create Mock Request from JSON');
    }
}
