<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ProfessionalWorks;

use App\Testing\Fixtures\AbstractMockedResponse;

class VertragsStatusResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/stammdaten/api/vertragsstatus/all';
    }

    public static function getMockedResponseBody(string $url): string
    {
        return <<<JSON
{
   "status":"success",
   "data":[
      {
         "id":1,
         "name":"Aktiv"
      },
      {
         "id":2,
         "name":"Stornier<PERSON>"
      },
      {
         "id":3,
         "name":"Beitragsfrei"
      },
      {
         "id":4,
         "name":"<PERSON><PERSON><PERSON>"
      },
      {
         "id":5,
         "name":"Ruh<PERSON>"
      }
   ]
}
JSON;
    }
}
