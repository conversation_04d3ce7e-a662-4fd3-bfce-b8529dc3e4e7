<?php

declare(strict_types=1);

namespace App\Testing\Fixtures;

abstract class AbstractMockedResponse
{
    public const HTTP_GET = 'get';
    public const HTTP_POST = 'post';
    public const HTTP_PUT = 'put';
    public const HTTP_DELETE = 'delete';

    abstract public static function getRequestMethod(): string;

    abstract public static function isMatchingUrl(string $url): bool;

    abstract public static function getMockedResponseBody(string $url): string;
}
