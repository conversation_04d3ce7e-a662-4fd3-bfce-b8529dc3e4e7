<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\Mailer;

use App\Testing\Fixtures\AbstractMockedResponse;

class EmailSettingsResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/v1/users/me/email-settings';
    }

    public static function getMockedResponseBody(string $url): string
    {
        return <<<JSON
            {
              "status": "success",
              "response": {
                "userId": 1,
                "email": "<EMAIL>",
                "scheme": "smtp",
                "senderName": "Tester 1",
                "sendingStatus": null,
                "receivingStatus": null,
                "smtpImapConnection": {
                  "smtpHost": "string",
                  "smtpPort": 0,
                  "username": "string",
                  "imapHost": "string",
                  "imapPort": 0
                }
              }
            }
        JSON;
    }
}
