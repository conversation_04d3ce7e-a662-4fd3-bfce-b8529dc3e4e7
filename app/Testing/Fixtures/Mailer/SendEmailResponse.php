<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\Mailer;

use App\Testing\Fixtures\AbstractMockedResponse;

class SendEmailResponse extends AbstractMockedResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_POST;
    }

    public static function isMatchingUrl(string $url): bool
    {
        return $url === '/v1/emails';
    }

    public static function getMockedResponseBody(string $url): string
    {
        return <<<JSON
            {
              "status" : "success",
              "response" : {
                "sentEmailCount" : 0,
                "queuedEmailCount" : 1,
                "wontSendEmailCount" : 0,
                "failedEmailCount" : 0,
                "emails" : [ {
                  "id" : 1,
                  "userId" : 1,
                  "subject" : "hello world 21.10.2024",
                  "status" : "queued",
                  "fromAddress" : null,
                  "actuallySentFrom" : null,
                  "replyTo" : null,
                  "toAddress" : [ {
                    "address" : "<EMAIL>",
                    "name" : ""
                  }, {
                    "address" : "<EMAIL>",
                    "name" : ""
                  } ],
                  "ccAddress" : [ ],
                  "bccAddress" : [ {
                    "address" : "<EMAIL>",
                    "name" : "DEMV Admin"
                  } ],
                  "bodyHtml" : null,
                  "bodyText" : null,
                  "attachments" : [ ],
                  "sentAt" : null,
                  "createdAt" : {
                    "date" : "2025-01-15 11:08:17.031700",
                    "timezone_type" : 3,
                    "timezone" : "UTC"
                  },
                  "errors" : [ ]
                } ]
              }
            }
        JSON;
    }
}
