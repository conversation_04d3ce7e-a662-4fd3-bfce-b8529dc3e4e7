<?php

declare(strict_types=1);

namespace App\Testing;

use App\Gateway\GatewayInterface;
use App\Testing\Fixtures\Mailer\EmailSettingsResponse;
use App\Testing\Fixtures\Mailer\SendEmailResponse;

class MockedMailerGateway extends MockedGateway implements GatewayInterface
{
    protected function getMockedResponses(): array
    {
        return [
            EmailSettingsResponse::class,
            SendEmailResponse::class,
        ];
    }
}
