<?php

declare(strict_types=1);

namespace Tests\Unit\Domain\ErweiterteKundensuche;

use App\Domain\ErweiterteKundensuche\ErweiterteKundensuchePwConverter;
use Tests\TestCase;
use Tests\Fixtures\JsonFixtureLoader;

class ErweiterteKundensuchePwConverterTest extends TestCase
{
    private const REQUEST_FIXTURE = 'erweiterte_kundensuche_request.json';
    private const PW_FORMAT_FIXTURE = 'erweiterte_kundensuche_pw_format.json';

    private JsonFixtureLoader $fixtureLoader;

    protected function setUp(): void
    {
        parent::setUp();
        $this->fixtureLoader = new JsonFixtureLoader();
    }

    public function test_toPwFormat_converts_data_correctly(): void
    {
        // Arrange
        $converter = new ErweiterteKundensuchePwConverter();
        $formData = $this->fixtureLoader->loadFixture(self::REQUEST_FIXTURE);
        $expectedData = $this->fixtureLoader->loadFixture(self::PW_FORMAT_FIXTURE);

        // Act
        $result = $converter->toPwFormat($formData, 10);

        // Assert
        $this->assertEqualsCanonicalizing($expectedData, $result);
    }

    public function test_toPwFormat_converts_data_correctly_with_empty_data(): void
    {
        // Arrange
        $converter = new ErweiterteKundensuchePwConverter();
        $formData = [];
        $expectedData = ['current_user_id' => '10', 'exclude_from_bulkletter' => '0'];

        // Act
        $result = $converter->toPwFormat($formData, 10);

        // Assert
        $this->assertEqualsCanonicalizing($expectedData, $result);
    }
}
