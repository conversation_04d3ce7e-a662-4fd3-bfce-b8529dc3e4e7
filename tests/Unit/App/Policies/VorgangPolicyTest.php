<?php

declare(strict_types=1);

namespace Tests\Unit\App\Policies;

use App\Enums\UserPermission;
use App\Enums\VorgangParticipantType;
use App\Models\User;
use App\Models\Vorgang;
use App\Models\VorgangParticipant;
use App\Policies\VorgangPolicy;
use App\Services\UserHierarchieService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lab404\Impersonate\Services\ImpersonateManager;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class VorgangPolicyTest extends TestCase
{
    use RefreshDatabase;

    private MockInterface $userHierarchieService;
    private MockInterface $impersonateManager;
    private VorgangPolicy $vorgangPolicy;

    protected function setUp(): void
    {
        parent::setUp();

        $this->userHierarchieService = $this->mock(UserHierarchieService::class);
        $this->impersonateManager = $this->mock(ImpersonateManager::class);
        $this->impersonateManager->shouldReceive('getImpersonator')->andReturnNull();

        $this->vorgangPolicy = new VorgangPolicy(
            $this->userHierarchieService,
            $this->impersonateManager
        );
    }

    #[Test]
    public function user_can_view_vorgang_when_underling_is_participant(): void
    {
        $parentUser = User::factory()->create();

        $underlingUser = User::factory()->create();

        $vorgang = Vorgang::factory()->create([
            'owner_id' => User::factory()->create()->id,
        ]);

        $participant = new VorgangParticipant();
        $participant->vorgang_id = $vorgang->id;
        $participant->user_id = $underlingUser->id;
        $participant->participant_type = VorgangParticipantType::BEARBEITER;
        $participant->save();

        $this->userHierarchieService
            ->shouldReceive('getUnderlingsForUser')
            ->with($parentUser, true, true)
            ->andReturn([$underlingUser->id]);

        $this->assertTrue($this->vorgangPolicy->view($parentUser, $vorgang));
    }

    #[Test]
    public function user_cannot_view_vorgang_when_no_relation_exists(): void
    {
        $user = User::factory()->create();

        $vorgang = Vorgang::factory()->create([
            'owner_id' => User::factory()->create()->id,
        ]);

        $this->userHierarchieService
            ->shouldReceive('getUnderlingsForUser')
            ->with($user, true, true)
            ->andReturn([]);

        $this->assertFalse($this->vorgangPolicy->view($user, $vorgang));
    }

    #[Test]
    public function admin_can_view_any_vorgang(): void
    {
        $adminUser = User::factory()->create();
        $adminUser->givePermissionTo(UserPermission::ADMIN->value);

        $vorgang = Vorgang::factory()->create([
            'owner_id' => User::factory()->create()->id,
        ]);

        $this->userHierarchieService
            ->shouldReceive('getUnderlingsForUser')
            ->with($adminUser, true, true)
            ->andReturn([]);

        $this->assertTrue($this->vorgangPolicy->view($adminUser, $vorgang));
    }

    #[Test]
    public function user_can_view_vorgang_when_they_created_it(): void
    {
        $user = User::factory()->create();

        $vorgang = Vorgang::factory()->create([
            'owner_id' => $user->id,
        ]);

        $this->userHierarchieService
            ->shouldReceive('getUnderlingsForUser')
            ->with($user, true, true)
            ->andReturn([$user->id]);

        $this->assertTrue($this->vorgangPolicy->view($user, $vorgang));
    }

    #[Test]
    public function user_can_view_vorgang_when_underling_created_it(): void
    {
        $parentUser = User::factory()->create();
        $underlingUser = User::factory()->create();

        $vorgang = Vorgang::factory()->create([
            'owner_id' => $underlingUser->id,
        ]);

        $this->userHierarchieService
            ->shouldReceive('getUnderlingsForUser')
            ->with($parentUser, true, true)
            ->andReturn([$underlingUser->id]);

        $this->assertTrue($this->vorgangPolicy->view($parentUser, $vorgang));
    }

    #[Test]
    public function user_can_view_vorgang_when_they_are_participant(): void
    {
        $user = User::factory()->create();

        $vorgang = Vorgang::factory()->create([
            'owner_id' => User::factory()->create()->id,
        ]);

        $participant = new VorgangParticipant();
        $participant->vorgang_id = $vorgang->id;
        $participant->user_id = $user->id;
        $participant->participant_type = VorgangParticipantType::BEARBEITER;
        $participant->save();

        $this->userHierarchieService
            ->shouldReceive('getUnderlingsForUser')
            ->with($user, true, true)
            ->andReturn([$user->id]);

        $this->assertTrue($this->vorgangPolicy->view($user, $vorgang));
    }
}
