<?php

declare(strict_types=1);

namespace Tests\Fixtures;

class JsonFixtureLoader implements FixtureLoaderInterface
{
    public function loadFixture(string $filename): array
    {
        $fileContents = file_get_contents(__DIR__ . '/json/' . $filename);

        if ($fileContents === false) {
            throw new \RuntimeException('Could not read fixture file');
        }

        return (array) json_decode($fileContents, true, flags: JSON_THROW_ON_ERROR);
    }
}