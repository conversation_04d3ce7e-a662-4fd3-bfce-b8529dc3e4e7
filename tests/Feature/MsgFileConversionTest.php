<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Domain\Korrespondenz\Http\Controller\MailConverterController;
use App\Domain\Korrespondenz\Models\ExterneKorrespondenzElement;
use App\Domain\Korrespondenz\Services\MsgToMIMEConverterService;
use App\Exceptions\CannotConvertMsgException;
use App\Exceptions\CannotGetFileContentException;
use App\Exceptions\CannotGetMailContentException;
use App\Models\File;
use App\Models\User;
use App\Models\Vorgang;
use App\Services\MailConverterService;
use App\Services\TimelineService;
use App\Support\Files\Services\FileService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class MsgFileConversionTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Vorgang $vorgang;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->vorgang = Vorgang::factory()->create();
        Auth::login($this->user);
    }

    public function test_msg_conversion_succeeds_with_valid_config(): void
    {
        // Set valid ConvertApi config
        Config::set('convert_api.secret', 'test_secret_key_12345678901234567890');

        // Mock services
        $mockFileService = Mockery::mock(FileService::class);
        $mockConverterService = Mockery::mock(MsgToMIMEConverterService::class);
        $mockTimelineService = Mockery::mock(TimelineService::class);
        $mockMailConverterService = Mockery::mock(MailConverterService::class);

        // Create test file
        $file = File::factory()->create([
            'filename' => 'test-email.msg',
            'mimetype' => 'application/vnd.ms-outlook',
            'size' => 12345,
        ]);

        // Mock successful conversion
        $mockKorrespondenz = new \App\Domain\Korrespondenz\Models\KorrespondenzElement();
        $mockKorrespondenz->id = 123;
        $mockKorrespondenz->betreff = 'Test Email Subject';
        $mockKorrespondenz->content = '<p>Test email content</p>';

        $mockMailConverterService
            ->shouldReceive('convertUploadedMailToKorrespondenz')
            ->once()
            ->with($file, false)
            ->andReturn($mockKorrespondenz);

        $this->app->instance(MailConverterService::class, $mockMailConverterService);

        // Make request
        $response = $this->postJson("/api/vorgaenge/{$this->vorgang->id}/mail-converter", [
            'temp_path' => '/tmp/test-email.msg',
            'name' => 'test-email.msg',
            'mimetype' => 'application/vnd.ms-outlook',
            'size' => 12345,
            'includeAttachments' => false,
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'betreff',
                'content',
            ]
        ]);
    }

    public function test_msg_conversion_fails_without_convert_api_secret(): void
    {
        // Clear ConvertApi config
        Config::set('convert_api.secret', '');

        Log::shouldReceive('error')
            ->once()
            ->with('ConvertApi secret is not configured', Mockery::any());

        // Make request
        $response = $this->postJson("/api/vorgaenge/{$this->vorgang->id}/mail-converter", [
            'temp_path' => '/tmp/test-email.msg',
            'name' => 'test-email.msg',
            'mimetype' => 'application/vnd.ms-outlook',
            'size' => 12345,
        ]);

        $response->assertStatus(422);
        $response->assertJson([
            'message' => 'CONVERT_API_SECRET is not configured. Please set the environment variable.'
        ]);
    }

    public function test_msg_conversion_handles_convert_api_failure(): void
    {
        // Set valid ConvertApi config
        Config::set('convert_api.secret', 'test_secret_key_12345678901234567890');

        // Mock services
        $mockMailConverterService = Mockery::mock(MailConverterService::class);

        // Create test file
        $file = File::factory()->create([
            'filename' => 'corrupted-email.msg',
            'mimetype' => 'application/vnd.ms-outlook',
            'size' => 12345,
        ]);

        // Mock conversion failure
        $mockMailConverterService
            ->shouldReceive('convertUploadedMailToKorrespondenz')
            ->once()
            ->with($file, false)
            ->andThrow(new CannotConvertMsgException('ConvertApi conversion failed: Invalid MSG format'));

        $this->app->instance(MailConverterService::class, $mockMailConverterService);

        Log::shouldReceive('error')
            ->once()
            ->with('MSG conversion failed', Mockery::any());

        // Make request
        $response = $this->postJson("/api/vorgaenge/{$this->vorgang->id}/mail-converter", [
            'temp_path' => '/tmp/corrupted-email.msg',
            'name' => 'corrupted-email.msg',
            'mimetype' => 'application/vnd.ms-outlook',
            'size' => 12345,
        ]);

        $response->assertStatus(422);
        $response->assertJson([
            'message' => 'MSG-Datei konnte nicht konvertiert werden: ConvertApi conversion failed: Invalid MSG format'
        ]);
    }

    public function test_msg_conversion_handles_file_content_error(): void
    {
        // Set valid ConvertApi config
        Config::set('convert_api.secret', 'test_secret_key_12345678901234567890');

        // Mock services
        $mockMailConverterService = Mockery::mock(MailConverterService::class);

        // Create test file
        $file = File::factory()->create([
            'filename' => 'inaccessible-email.msg',
            'mimetype' => 'application/vnd.ms-outlook',
            'size' => 12345,
        ]);

        // Mock file content error
        $mockMailConverterService
            ->shouldReceive('convertUploadedMailToKorrespondenz')
            ->once()
            ->with($file, false)
            ->andThrow(new CannotGetFileContentException('Could not read file from storage'));

        $this->app->instance(MailConverterService::class, $mockMailConverterService);

        Log::shouldReceive('error')
            ->once()
            ->with('File content retrieval failed', Mockery::any());

        // Make request
        $response = $this->postJson("/api/vorgaenge/{$this->vorgang->id}/mail-converter", [
            'temp_path' => '/tmp/inaccessible-email.msg',
            'name' => 'inaccessible-email.msg',
            'mimetype' => 'application/vnd.ms-outlook',
            'size' => 12345,
        ]);

        $response->assertStatus(422);
        $response->assertJson([
            'message' => 'Datei-Inhalt konnte nicht gelesen werden: Could not read file from storage'
        ]);
    }

    public function test_msg_conversion_handles_mail_content_error(): void
    {
        // Set valid ConvertApi config
        Config::set('convert_api.secret', 'test_secret_key_12345678901234567890');

        // Mock services
        $mockMailConverterService = Mockery::mock(MailConverterService::class);

        // Create test file
        $file = File::factory()->create([
            'filename' => 'empty-email.msg',
            'mimetype' => 'application/vnd.ms-outlook',
            'size' => 12345,
        ]);

        // Mock mail content error
        $mockMailConverterService
            ->shouldReceive('convertUploadedMailToKorrespondenz')
            ->once()
            ->with($file, false)
            ->andThrow(new CannotGetMailContentException('Email has no readable content'));

        $this->app->instance(MailConverterService::class, $mockMailConverterService);

        Log::shouldReceive('error')
            ->once()
            ->with('Mail content extraction failed', Mockery::any());

        // Make request
        $response = $this->postJson("/api/vorgaenge/{$this->vorgang->id}/mail-converter", [
            'temp_path' => '/tmp/empty-email.msg',
            'name' => 'empty-email.msg',
            'mimetype' => 'application/vnd.ms-outlook',
            'size' => 12345,
        ]);

        $response->assertStatus(422);
        $response->assertJson([
            'message' => 'E-Mail-Inhalt konnte nicht extrahiert werden: Email has no readable content'
        ]);
    }

    public function test_debug_endpoint_shows_convert_api_status(): void
    {
        // Set valid ConvertApi config
        Config::set('convert_api.secret', 'test_secret_key_12345678901234567890');

        $response = $this->getJson('/api/debug/convert-api-status');

        $response->assertStatus(200);
        $response->assertJson([
            'status' => 'success',
            'message' => 'ConvertApi is configured',
            'secret_length' => 32,
        ]);
        $response->assertJsonStructure([
            'secret_preview'
        ]);
    }

    public function test_debug_endpoint_shows_missing_config(): void
    {
        // Clear ConvertApi config
        Config::set('convert_api.secret', '');

        $response = $this->getJson('/api/debug/convert-api-status');

        $response->assertStatus(500);
        $response->assertJson([
            'status' => 'error',
            'message' => 'CONVERT_API_SECRET not configured',
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
