set dotenv-load := true
set positional-arguments := true

compose_php_service := 'php-fpm'
node_service := 'node'
mysql_service := 'mysql'

# Import common Just recipes.
import 'just/common/docker-compose.just'
import 'just/common/php.just'
import 'just/common/laravel.just'

# Import project-specific Just recipes.
import 'just/artisan.just'
import 'just/composer.just'
import 'just/cypress.just'
import 'just/env.just'
import 'just/github.just'
import 'just/openapi.just'
import 'just/php.just'
import 'just/yarn.just'

# Install packages, see `just --list install` for details.
mod install 'just/common/install.just'

# Print list of recipes by default.
[private]
default:
    @just --list

# Run all linting recipes.
[group: 'Linting']
lint:
    @just php-lint
    @just yarn-lint

# Run all linting recipes and fix issues.
[group: 'Linting']
lint-fix:
    @just php-lint-fix
    @just yarn-lint-fix

# Run all testing recipes.
[group: 'Testing']
test:
    @just php-test
    @just yarn-test
