imports:
  - app/Domain/Avatars/dependencies.yaml
  - app/Domain/External/dependencies.yaml
  - app/Domain/Kommentar/dependencies.yaml
  - app/Domain/Korrespondenz/dependencies.yaml
  - app/Domain/Mailer/dependencies.yaml
  - app/Domain/Tags/dependencies.yaml
  - app/Domain/UserSettings/dependencies.yaml
  - app/Domain/Verknuepfungen/dependencies.yaml
  - app/Domain/VorgangAenderungen/dependencies.yaml
  - app/Domain/Vorlagen/dependencies.yaml

deptrac:
  paths:
    - ./app
  layers:
    - name: App
      collectors:
        - type: directory
          regex: app/(Console|Enums|Events|Exceptions|Gateway|Http|Listeners|Mail|Models|Providers|Relationships|Synchronization)/.*
    - name: Support
      collectors:
        - type: directory
          regex: app/Support/.*

  ruleset:
    App:
      - Support
    Support:
      - App #TODO: Das hier sollte verboten sein.
