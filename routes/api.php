<?php

declare(strict_types=1);

use App\Domain\Korrespondenz\Http\Controller\MailConverterController;
use App\Domain\Mailer\Http\Controllers\MailImportController;
use App\Domain\UserSettings\Http\Controllers\UserSettingsController;
use App\Http\Controllers as C;
use App\Http\Controllers\FirmenSettingsController;
use App\Support\BulkChanges\Http\Controllers\BulkCompleteVorgangController;
use App\Support\BulkChanges\Http\Controllers\BulkDeleteController;
use Illuminate\Support\Facades\Route;

// vorgaenge
Route::get('vorgaenge/count', [C\VorgangController::class, 'count']);
Route::apiResource('vorgaenge', C\VorgangController::class)
    ->parameter('vorgaenge', 'vorgang')
    ->only([
        'index',
        'show',
        'store',
        'update',
    ]);
Route::prefix('vorgaenge/{vorgang}')
    ->middleware('can:view,vorgang')
    ->group(static function (): void {
        Route::put('/untervorgaenge', [C\VorgangController::class, 'updateUntervorgaenge']);
        Route::get('/participants', [C\VorgangParticipantController::class, 'index']);
        Route::post('/participants/{participantType}', [C\VorgangParticipantController::class, 'store']);
        Route::delete('/participants/{participantType}/{user}', [C\VorgangParticipantController::class, 'destroy']);

        Route::apiResource('/vertraege', C\VorgangVertragController::class)
            ->only('store');

        Route::apiResource('/notifications', C\VorgangNotificationController::class)
            ->only(['store']);
        Route::put('/notifications', [C\VorgangNotificationController::class, 'deleteAll']);

        Route::get('/courtageerfassung', [C\ExternalRedirectController::class, 'courtageerfassung']);
        Route::get('/schadenerfassung', [C\ExternalRedirectController::class, 'schadenerfassung']);

        // MSG file conversion
        Route::post('/mail-converter', [MailConverterController::class, 'store']);
    });
Route::get('vorgaenge/{firmaId}/{vorgangsnummer}', [C\VorgangController::class, 'showByVorgangsnummer']);

Route::apiResource('vorgangTypen', C\VorgangTypController::class)
    ->parameter('vorgangTypen', 'vorgangTyp')
    ->only([
        'index',
        'show',
    ]);

Route::apiResource('kunden', C\KundeController::class)
    ->parameter('kunden', 'kunde')
    ->only(['index', 'show']);

Route::post('kunden/missing-address', [C\MissingAddressKundeController::class, 'missingAddress']);

Route::prefix('kunden/{kunde}')
    ->middleware('can:view,kunde')
    ->group(static function (): void {
        Route::get('/address', [C\KundeController::class, 'address']);

        Route::apiResource('vertraege', C\VertragController::class)
            ->only(['index']);

        Route::apiResource('documents', C\KundenDocumentController::class)
            ->only(['index', 'show']);
        Route::get('documents/{document}/download', [C\KundenDocumentController::class, 'download'])
            ->name('documents.download');

        Route::get('/generateMaklerauftrag', [C\ExternalRedirectController::class, 'generateMaklerauftrag'])
            ->name('generate-maklerauftrag');

        Route::get('/zustaendigerVermittler', [C\ZustaendigerVermittlerController::class, 'user']);
        Route::get(
            '/zustaendigerVermittler/receiverMailAddress',
            [C\ZustaendigerVermittlerController::class, 'receiverAddress'],
        );
    });

Route::get('dokumenttypen', [C\DokumenttypController::class, 'index']);

Route::apiResource('vertraege', C\VertragController::class)
    ->parameter('vertraege', 'vertrag')
    ->only(['show']);

Route::apiResource('/', C\UserController::class)
    ->parameter('users', 'users')
    ->only(['index']);

Route::apiSingleton('userSettings', UserSettingsController::class)
    ->only([
        'show',
        'update',
    ]);

Route::apiResource('firmenSettings', FirmenSettingsController::class)
    ->parameter('firmenSettings', 'firmenSetting')
    ->only([
        'index',
        'update',
    ]);

// current user
Route::prefix('/users')
    ->group(static function (): void {
        Route::apiResource('/', C\UserController::class)
            ->parameter('users', 'users')
            ->only(['index']);

        Route::prefix('/me')->group(
            static function (): void {
                Route::get('/queueMailImport', [MailImportController::class, 'queueImport']);
                Route::get('/senderMailAddress', [C\UserController::class, 'senderMailAddress']);
                Route::get('/receiverMailAddress', [C\UserController::class, 'receiverMailAddress']);
                Route::get('/logo', [C\UserController::class, 'logo']);
                Route::get('/', [C\UserController::class, 'currentUser']);
            },
        );

        Route::get('{user}/senderMailAddress', [C\UserController::class, 'senderMailAddress']);
        Route::get('{user}/receiverMailAddress', [C\UserController::class, 'receiverMailAddress']);
        Route::get('{user}/allows-impersonation', [C\UserController::class, 'allowsImpersonation']);
    });

Route::get('emailSuggestions', [C\EmailSuggestionController::class, 'suggestions']);

// hierarchy
Route::get('underlings', [C\UserHierarchyController::class, 'underlings']);
Route::get('parents', [C\UserHierarchyController::class, 'parents']);
Route::get('firmenMembers', [C\UserHierarchyController::class, 'firmenMembers']);

Route::get('gesellschaften/demv', [C\GesellschaftController::class, 'showDemv']);

Route::apiResource('gesellschaften', C\GesellschaftController::class)
    ->parameter('gesellschaften', 'gesellschaft')
    ->only(['index', 'show']);

Route::prefix('gesellschaften/{gesellschaft}')
    ->group(static function (): void {
        Route::get('/logo', [C\GesellschaftLogoController::class, 'index']);
        Route::get('/address', [C\GesellschaftController::class, 'address']);
    });

Route::apiResource('sparten', C\SpartenController::class)
    ->parameter('sparten', 'sparte')
    ->only(['index', 'show']);

Route::prefix('gesellschaft/{gesellschaft}')
    ->group(static function (): void {
        Route::get('ansprechpartner', [C\AnsprechpartnerController::class, 'index']);
    });

Route::apiResource('files', C\FileController::class)
    ->parameter('files', 'file')
    ->only(['store', 'show', 'destroy']);

Route::post('signed-storage-url', C\SignedStorageUrlController::class . '@store')
    ->middleware(config('vapor.middleware', 'api'));

Route::apiResource('bulkDeletes', BulkDeleteController::class)
    ->parameter('bulkDeletes', 'bulkDelete')
    ->only(['store', 'show']);

Route::apiResource('bulkCompletesVorgang', BulkCompleteVorgangController::class)
    ->parameter('bulkCompletesVorgang', 'bulkCompleteVorgang')
    ->only(['store', 'show']);

Route::prefix('vertriebsweg/vorgangTyp/{vorgangTyp}')->group(static function (): void {
    Route::apiResource('vertrag', C\VertriebswegController::class)
        ->only(['show']);

    Route::apiResource('gesellschaft', C\VertriebswegController::class)
        ->only(['show']);
});
