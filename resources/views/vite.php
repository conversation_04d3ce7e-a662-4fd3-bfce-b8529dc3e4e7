<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Http;
use Illuminate\Support\HtmlString;

$port = env('VITE_SERVER_PORT');
$devServerIsRunning = false;
$devEnvironment = app()->environment('testing', 'ci', 'local');

if ($devEnvironment) {
    try {
        Http::timeout(1)->get("//node:$port");
        $devServerIsRunning = true;
    } catch (Throwable) {
    }
}

if ($devServerIsRunning) {
    echo new HtmlString(<<<HTML
        <script type="module" src="//localhost:{$port}/@vite/client"></script>
        <script type="module" src="//localhost:{$port}/resources/js/app.ts"></script>
        <script>console.log('Connected to vite!')</script>
    HTML);

    return;
}

$manifest = json_decode(file_get_contents(
    public_path('build/.vite/manifest.json')
), true);

$mixUrl = config('app.mix_url', '');

echo new HtmlString(<<<HTML
    <script type="module" src="$mixUrl/build/{$manifest['resources/js/app.ts']['file']}"></script>
    <link rel="stylesheet" href="$mixUrl/build/{$manifest['resources/js/app.ts']['css'][0]}">
HTML);

if (!$devEnvironment) {
    echo new HtmlString(<<<HTML
    <script
        src="https://cloud.ccm19.de/app.js?apiKey=9a894bea62d1bd7ccbd643afb866c9f5f798fbceea72ff3f&amp;domain=6527c09885abc54ef0001632"
        referrerpolicy="origin"
    ></script>
HTML);
}
