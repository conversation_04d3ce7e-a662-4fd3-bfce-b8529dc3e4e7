import { get } from '@/api';
import { EmailElement, ExpectedSenderResource } from '@/store/resources/types';

export async function getSenderMailAddress(userId?: string): Promise<EmailElement> {
  const { data } = await get<ExpectedSenderResource>(
    `users/${userId ?? 'me'}/senderMailAddress`,
  );

  if (data?.data === undefined) {
    throw new Error('Could not get sender mail address');
  }

  return data.data.attributes;
}

export async function getReceiverMailAddress(userId?: string): Promise<EmailElement> {
  const { data } = await get<ExpectedSenderResource>(
    `users/${userId ?? 'me'}/receiverMailAddress`,
  );

  if (data?.data === undefined) {
    throw new Error('Could not get receiver mail address');
  }

  return data.data.attributes;
}
