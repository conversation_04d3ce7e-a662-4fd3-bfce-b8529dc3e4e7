import axios, { AxiosResponse } from 'axios';
import { merge } from 'lodash-es';

import { del, isAxiosError, post } from '@/api';
import { eventBus } from '@/store/resources/store';
import { ExternalFileResource, FileResource, UploadedFileResource } from '@/store/resources/types';

type SignedStorageUrlResponse = {
  path: string,
  url: string,
  headers: Record<string, string>,
};

/**
 * Upload a file to S3 and return the temp path
 */
export async function uploadFileToS3(file: File): Promise<string> {
  try {
    const response: AxiosResponse<SignedStorageUrlResponse> = await axios.post(
      '/api/signed-storage-url',
      {
        'Content-Type': 'application/json',
      },
    );

    const { headers } = response.data;
    if ('Host' in headers) {
      delete headers.Host;
    }

    await axios.put(response.data.url, file, {
      headers,
    });

    return response.data.path;
  } catch (e) {
    eventBus.emit('error');

    return '';
  }
}

export async function saveFile<T extends UploadedFileResource | ExternalFileResource>(
  file: T,
  ownerId: string,
  ownerType: string,
  sync: boolean = false, // if true, the file will be saved synchronously
): Promise<T> {
  try {
    const response = await post<FileResource>('files/', {
      sync,
      data: merge(file, {
        relationships: {
          owner: {
            data: {
              id: ownerId,
              type: ownerType,
            },
          },
        },
      }),
    });

    const newFile = response?.data?.data;

    if (newFile === undefined) {
      return file;
    }

    file.id = newFile.id;
    file.links = newFile.links;

    return file;
  } catch (e) {
    if (!isAxiosError(e) || e.response === undefined) {
      eventBus.emit('error');
      throw e;
    }
  }

  return file;
}

export async function deleteFile(id: string): Promise<boolean> {
  try {
    await del<FileResource>(`files/${id}`);

    return true;
  } catch (e) {
    if (!isAxiosError(e) || e.response === undefined) {
      eventBus.emit('error');
      throw e;
    }

    return false;
  }
}

export function toFileResource(
  { name, type: mimetype, size }: File,
  hidden: boolean = false,
): FileResource {
  return {
    type: 'files',
    id: '',
    attributes: {
      name,
      mimetype,
      size,
      status: 'pending',
      hidden,
    },
  };
}

export function toUploadedFileResource(
  file: File,
  tempPath: string,
  hidden?: boolean,
): UploadedFileResource {
  return merge(toFileResource(file, hidden), {
    attributes: {
      tempPath,
    },
  });
}
