import { MultiselectItem } from '@demvsystems/design-components';
import { computed, Ref, WritableComputedRef } from 'vue';

export function makeMultiselectItemProxy<
  Type extends string,
>(
  arr: Ref<Type[]>,
  labels: Record<Type, string>,
): WritableComputedRef<MultiselectItem<string>[]> {
  return computed<MultiselectItem<string>[]>({
    get: () => {
      return arr.value.map((value) => {
        return {
          value: value,
          label: labels[value],
        };
      });
    },
    set: (values) => {
      arr.value = values
        .map((val) => {
          if (val.value === undefined) {
            return undefined;
          }

          return val.value;
        })
        .filter((v): v is Type => v !== undefined);
    },
  });
}

export function getMultiselectOptions<
  Type extends string,
  Typeof extends { [key: number | string]: Type },
>(
  enumType: Typeof,
  labels: Record<Type, string>,
): MultiselectItem<string>[] {
  return Object.keys(enumType).map((key) => {
    return {
      value: enumType[key as keyof typeof enumType],
      label: labels[enumType[key as keyof typeof enumType]],
    };
  });
}
