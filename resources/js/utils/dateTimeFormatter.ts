// todo: add dateStyle, timestyle
// todo:update typescript (https://github.com/microsoft/TypeScript/issues/40806, https://github.com/microsoft/TypeScript/pull/41880)

const toDEformatter = new Intl.DateTimeFormat('de-DE', {
  timeZone: 'Europe/Berlin',
  year: 'numeric',
  month: 'numeric',
  day: 'numeric',
  hour: 'numeric',
  minute: 'numeric',
  second: 'numeric',
  formatMatcher: 'basic',
});

export { toDEformatter };
