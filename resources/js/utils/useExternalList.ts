import { captureException } from '@sentry/vue';
import { Ref, ref, watch } from 'vue';

import { get, JsonApiUrl } from '@/api';
import { Document, ResourceObject } from '@/types/jsonapi';

interface UseExternalListOptions {
  updateManually?: boolean,
}

export const useExternalList = <T extends ResourceObject>(
  url?: string | Ref<string | null | undefined>,
  jsonApiUrl?: JsonApiUrl<T[]> | Ref<JsonApiUrl<T[]> | undefined>,
  options: UseExternalListOptions = {},
): {
  document: Ref<Document<T[]> | undefined>,
  list: Ref<T[]>,
  isLoading: Ref<boolean>,
  update: () => Promise<void>,
} => {
  const {
    updateManually = false,
  } = options;

  let abortController = new AbortController();
  const refUrl = ref(url);
  const refJsonApiUrl = ref(jsonApiUrl) as Ref<JsonApiUrl<T[]> | undefined>;

  const list: Ref<T[]> = ref([]);
  const document = ref<Document<T[]>>();
  const isLoading = ref(false);

  const updateAbortable = async (signal: AbortSignal) => {
    list.value = [];
    let nextUrl: string | undefined = refUrl.value ?? undefined;

    try {
      isLoading.value = true;

      while (nextUrl !== undefined) {
        const response = await get<T[]>(nextUrl, refJsonApiUrl.value, {
          signal,
        });

        document.value = response.data;

        const data = response.data?.data ?? [];
        list.value.push(...data);

        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        nextUrl = (response.data.links?.next ?? undefined) as string | undefined;
      }
    } catch (e) {
      if (e instanceof Error && e.name !== 'CanceledError') {
        captureException(e);
      }
    } finally {
      isLoading.value = false;
    }
  };

  const update = async () => {
    abortController.abort();
    abortController = new AbortController();

    await updateAbortable(abortController.signal);
  };

  if (!updateManually) {
    watch([refUrl, refJsonApiUrl], () => {
      void update();
    }, { immediate: true, deep: true });
  }

  return {
    document,
    list,
    isLoading,

    update,
  };
};
