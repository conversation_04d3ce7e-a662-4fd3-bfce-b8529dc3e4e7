import { ref } from 'vue';

import { get } from '../api';
import { GesellschaftResource } from '../store/resources/types';

const demv = ref<GesellschaftResource>();

export default async (): Promise<GesellschaftResource> => {
  if (demv.value === undefined) {
    const response = await get<GesellschaftResource>('/gesellschaften/demv');

    demv.value = response.data?.data;

    if (demv.value === undefined) {
      throw new Error('Unable to get DEMV Gesellschaft!');
    }
  }

  return demv.value;
};
