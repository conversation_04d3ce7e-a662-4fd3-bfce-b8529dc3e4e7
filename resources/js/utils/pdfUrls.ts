function getPdfUrl(
  vorgangId: string,
  korrespondenzId: string,
  view: boolean,
): string {
  let url = `/api/vorgaenge/${vorgangId}/korrespondenzen/${korrespondenzId}/pdf`;

  if (view) {
    url += '?view';
  }

  return url;
}

export function getPdfDownloadUrl(
  vorgangId: string,
  korrespondenzId: string,
): string {
  return getPdfUrl(vorgangId, korrespondenzId, false);
}

export function getPdfViewUrl(
  vorgangId: string,
  korrespondenzId: string,
): string {
  return getPdfUrl(vorgangId, korrespondenzId, true);
}
