import { defineStore } from 'pinia';
import { computed } from 'vue';

import { VorgangTypResource } from '@/store/resources/types';
import { useExternalList } from '@/utils/useExternalList';

export const useVorgangstypenStore = defineStore('vorgangstypen', () => {
  const {
    document: vorgangTypenDoc,
    isLoading,
  } = useExternalList<VorgangTypResource>('vorgangTypen', {
    sort: [{
      name: 'titel',
    }],
    fields: {
      vorgangTypen: [
        'titel',
        'empfaengerTyp',
        'isBrief',
        'isEmail',
      ],
    },
  });

  const vorgangstypen = computed<VorgangTypResource[]>(() => (
    vorgangTypenDoc.value?.data ?? []
  ));

  const mailVorgangstypen = computed(() => (
    vorgangstypen.value?.filter((typ) => typ.attributes.isEmail)
  ));

  const briefVorgangstypen = computed(() => (
    vorgangstypen.value.filter((typ) => typ.attributes.isBrief)
  ));

  const findById = (id: string): VorgangTypResource | undefined => {
    return vorgangstypen.value.find((vorgangstyp) => vorgangstyp.id === id);
  };

  return {
    isLoading,
    vorgangstypen,
    mailVorgangstypen,
    briefVorgangstypen,
    findById,
  };
});
