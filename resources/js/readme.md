# Vorgänge-Frontend

## Ordnerstruktur

```
.
├── components/
│   ├── <complex_component>/
│   └── <simple_component>.vue
├── pages/
│   └── <page>/
│       ├── components/
│       ├── stores/
│       └── <page>.vue
├── stores/
│   ├── <complex_store>/
│   └── <simple_store>.ts
└── tests/
    ├── components/
    │   └── <component>.spec.ts
    └── pages/
        └── <page>/
            ├── <component>.spec.ts
            └── <page>.spec.ts
```

## File naming - front end

|                  | Style      |
|------------------|------------|
| Vue (Components) | PascalCase |
| Vue (Pages)      | camelCase  |
| Ts               | camelCase  |
| folders          | camelCase  |
| Cypress tests    | sneak_case |
