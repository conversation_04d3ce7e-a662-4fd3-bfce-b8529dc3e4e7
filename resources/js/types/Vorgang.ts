import { BadgeType } from '@demvsystems/design-components';

export enum VorgangStatus {
  Entwurf = 'entwurf',
  Offen = 'offen',
  Erinnerung1 = 'erinnerung_1',
  Erinnerung2 = 'erinnerung_2',
  Mahnung1 = 'mahnung_1',
  Mahnung2 = 'mahnung_2',
  Erledigt = 'erledigt',
}

export interface Status {
  value: VorgangStatus;
  label: string;
  type: BadgeType;
}

export const statuses: Status[] = [
  {
    value: VorgangStatus.Entwurf,
    label: 'Entwurf',
    type: 'default',
  },

  {
    value: VorgangStatus.Offen,
    label: 'Offen',
    type: 'warning',
  },

  {
    value: VorgangStatus.Erledigt,
    label: 'Erledigt',
    type: 'success',
  },

  {
    value: VorgangStatus.Erinnerung1,
    label: '1. Erinnerung',
    type: 'warning',
  },

  {
    value: VorgangStatus.Erinnerung2,
    label: '2. Erinnerung',
    type: 'warning',
  },

  {
    value: VorgangStatus.Mahnung1,
    label: '1. Mahnung',
    type: 'error',
  },

  {
    value: VorgangStatus.Mahnung2,
    label: '2. Mahnung',
    type: 'error',
  },
];
