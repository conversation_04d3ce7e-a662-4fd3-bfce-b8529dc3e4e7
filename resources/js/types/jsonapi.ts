// based on https://github.com/mike-north/jsonapi-typescript

type Primitive = string | number | boolean | null;

export interface JsonObject {
  [member: string]: JsonValue;
}

type JsonArray = JsonValue[];

type JsonValue = Primitive | JsonArray | JsonObject;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type MetaObject = Record<string, any>;

export type Link = string; // | { href: string; meta?: MetaObject };

export type Links = Record<string, Link> & {
  self?: Link; // the current response document
  direct?: Link; // frontend view
  related?: Link; // when the primary data represents a resource relationship
  external?: Link;
  logo?: Link;
};

export interface PaginationLinks {
  first?: Link | null; // the first page of data
  last?: Link | null; // the last page of data
  prev?: Link | null; // the previous page of data
  next?: Link | null; // the next page of data
}

export interface Error {
  id?: number | string;
  links?: Links;
  status?: string;
  code?: string;
  title?: string;
  detail?: string;
  source?: {
    pointer?: unknown;
    parameter?: string;
  };
  meta?: MetaObject;
}

export type AttributesObject<T extends JsonObject = JsonObject> = {
  [K in keyof T]: T[K]
};

export interface ResourceIdentifier<T extends string = string> {
  id: string;
  type: T;
  meta?: MetaObject;
}

export type ResourceLinkHasOne<T extends string = string> =
  | null
  | ResourceIdentifier<T>;

export type ResourceLinkHasMany<T extends string = string> =
  | never[]
  | ResourceIdentifier<T>[];

export type ResourceLink<T extends string = string> =
  | ResourceLinkHasOne<T>
  | ResourceLinkHasMany<T>;

export interface RelationshipObject<T extends ResourceLink = ResourceLink> {
  data: T;
  links?: Links;
  meta?: MetaObject;
}

export type RelationshipsObject = Record<string, RelationshipObject>;

export interface ResourceObject<
  T extends string = string,
  A extends AttributesObject = AttributesObject,
  R extends RelationshipsObject = RelationshipsObject,
> {
  id: string;
  type: T;
  attributes: AttributesObject<A>;
  relationships?: Partial<R>;
  links?: Links;
  meta?: MetaObject;
}

export type Data<
  T extends string = string,
  A extends AttributesObject = AttributesObject,
  R extends RelationshipsObject = RelationshipsObject,
> = ResourceObject<T, A, R> | ResourceObject<T, A, R>[];

export interface Document<T extends Data = Data> {
  links?: Links | PaginationLinks;
  data?: T;
  errors?: Error[];
  meta?: MetaObject;
  included?: ResourceObject[];
}

export type ExtractType<T> = T extends ResourceObject<infer Type> ? Type : never;

export type RelationshipHasOne<T extends Data> = RelationshipObject<
  ResourceLinkHasOne<ExtractType<T>>
>;

export type RelationshipHasMany<T extends Data> = RelationshipObject<
  ResourceLinkHasMany<ExtractType<T>>
>;
