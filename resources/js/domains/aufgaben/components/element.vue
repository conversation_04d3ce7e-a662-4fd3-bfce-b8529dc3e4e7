<template>
  <TimelineElement
    v-if="kommentar !== undefined"
    :element="element"
    :icon-config="iconConfig"
    :first="index === 0"
  >
    <template #default>
      <div v-if="tagContent !== null && tagIcon !== null">
        <DsTag
          :icon="tagIcon.name"
          :title="tagTitle"
          :category="tagIcon.helpText"
        >
          {{ tagContent }}
        </DsTag>
      </div>

      <div
        class="html-preview"
        v-html="kommentar.attributes.content"
      />

      <div v-if="files.length > 0">
        <div class="mb-1 text-sm font-medium text-gray-700">
          Anhänge
        </div>
        <FileList
          class="w-1/2"
          :files="files"
        />
      </div>
    </template>
  </TimelineElement>
</template>

<script setup lang="ts">
import { DsTag } from '@demvsystems/design-components';
import { format, formatRelative } from 'date-fns';
import { de } from 'date-fns/locale';
import { computed } from 'vue';

import TimelineElement from '@/components/TimelineElement/TimelineElement.vue';
import { IconConfig } from '@/components/TimelineElement/types';
import FileList from '@/components/fileList/FileList.vue';
import { injectStore } from '@/store/resources/composition';
import { GespraechsType, TimelineEintragResource } from '@/store/resources/types';

const props = defineProps<{
  element: TimelineEintragResource,
  index?: number,
}>();

const store = injectStore();

const kommentar = computed(() => {
  switch (props.element.relationships?.element?.data?.type) {
    case 'gespraechsnotizen':
      return store.gespraechsnotizen.find(props.element.relationships?.element?.data?.id);
    case 'systemkommentare':
      return store.systemkommentare.find(props.element.relationships?.element?.data?.id);
    default: // kommentare
      return store.kommentare.find(props.element.relationships?.element?.data?.id);
  }
});

const iconConfig = computed<IconConfig>(() => {
  if (kommentar.value?.type === 'systemkommentare') {
    return {
      name: 'robot-astromech',
      helpText: 'Systemkommentar',
    };
  }

  if (kommentar.value?.type === 'gespraechsnotizen') {
    return {
      name: 'comment-alt-edit',
      helpText: 'Gesprächsnotiz',
    };
  }

  if (props.index === 0) {
    return {
      name: 'tasks',
      helpText: 'Aufgabe',
    };
  }

  return {
    name: 'comment',
    helpText: 'Kommentar',
  };
});

const files = computed(
  () => store.files.findAllRelated(kommentar.value?.relationships?.files),
);

// tags!
const tagIcon = computed<IconConfig | null>(() => {
  if (kommentar.value?.type !== 'gespraechsnotizen') {
    return null;
  }

  switch (kommentar.value?.attributes.type) {
    case GespraechsType.Phone:
      return {
        name: 'phone',
        helpText: 'Telefon',
      };
    case GespraechsType.OnSite:
      return {
        name: 'home',
        helpText: 'Vor Ort',
      };
    default:
      return {
        name: '',
        helpText: 'Sonstige',
      };
  }
});

const now = new Date();

const tagContent = computed(() => {
  if (kommentar.value?.type !== 'gespraechsnotizen') {
    return null;
  }

  let fromFormatted = formatRelative(
    new Date(kommentar.value.attributes.dateFrom), now, { locale: de },
  );

  /**
   * FormatRelative omits the time if date is older than two weeks.
   * We have to use this function bc provides us with words like 'yesterday' or
   * 'last monday'. Thus we have to add the missing time.
   */
  if (fromFormatted.indexOf('um') === -1) {
    fromFormatted += format(
      new Date(kommentar.value.attributes.dateFrom),
      " 'von' p 'Uhr'",
      { locale: de },
    );
  } else {
    /**
     * But one last change need to be done, changing 'um' to 'von'
     * Sadly, we cant allways opt out for time, and add it always in our
     * desired format. https://github.com/date-fns/date-fns/issues/1218
     */
    fromFormatted = fromFormatted.replace(' um ', ' von ');
  }

  return fromFormatted + format(
    new Date(kommentar.value.attributes.dateTo),
    " 'bis' p 'Uhr'",
    { locale: de },
  );
});

const tagTitle = computed(() => {
  if (kommentar.value?.type !== 'gespraechsnotizen') {
    return null;
  }

  const fromFormatted = format(
    new Date(kommentar.value.attributes.dateFrom),
    'PPPPp',
    { locale: de },
  );

  return fromFormatted + format(
    new Date(kommentar.value.attributes.dateTo),
    " 'bis' p 'Uhr'",
    { locale: de },
  );
});
</script>
