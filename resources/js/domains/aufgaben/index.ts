import TimelineElement from '../aufgaben/components/element.vue';
import { createDomain } from '../registry';

export default createDomain({
  components: {
    timeline: {
      kommentare: TimelineElement,
      gespraechsnotizen: TimelineElement,
      systemkommentare: TimelineElement,
    },
  },
  vorgangsArt: {
    name: 'kommentare',
    title: 'Aufgabe',
    icon: 'tasks',
    description: 'Interner Vorgang zur Verwaltung',
  },
});
