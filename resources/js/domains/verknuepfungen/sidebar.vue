<template>
  <SidebarItem
    v-if="verknuepfteVorgaenge.length > 0"
    label="Verknüpfte Vorgänge"
  >
    <div class="space-y-1">
      <router-link
        v-for="verknuepfterVorgang in verknuepfteVorgaenge"
        :key="verknuepfterVorgang.id"
        :to="{
          name: 'vorgaenge.show',
          params: {
            firmaId,
            vorgangsnummer: verknuepfterVorgang.attributes.vorgangsnummer,
          },
        }"
        class="
          block space-y-1 rounded-md border p-3 text-sm
          ring-2 ring-inset ring-white
          transition-colors duration-75 hover:bg-gray-100
        "
      >
        <div class="flex justify-between space-x-3 text-base">
          <strong
            class="truncate"
            v-text="verknuepfterVorgang.attributes.titel"
          />

          <VorgangStatus :status="verknuepfterVorgang.attributes.status" />
        </div>

        <TimeAgoTag
          v-if="verknuepfterVorgang.attributes.faelligAt"
          :value="verknuepfterVorgang.attributes.faelligAt"
        />
      </router-link>
    </div>
  </SidebarItem>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import VorgangStatus from '@/components/VorgangStatus.vue';
import TimeAgoTag from '@/components/tags/TimeAgoTag.vue';
import useCurrentUser from '@/components/users/useCurrentUser';
import SidebarItem from '@/pages/vorgang/components/SidebarItem.vue';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import type { VorgangResource } from '@/store/resources/types';
import { RelationshipHasMany } from '@/types/jsonapi';

const store = injectStore();
const vorgang = useVorgangFromRoute();

const { user } = useCurrentUser();

const verknuepfteVorgaenge = computed(() => store.vorgaenge.findAllRelated(
  vorgang.value?.relationships?.verknuepfungen as (
    RelationshipHasMany<VorgangResource>
  ),
));

const firmaId = computed(() => user.value?.attributes.firmaId);
</script>
