<template>
  <TimelineElement
    v-if="verknuepfterVorgang !== undefined && ersteller !== undefined"
    :element="element"
    :icon-config="{
      name: aktion === 'erstellt' ? 'link' : 'unlink',
      helpText: 'Vorgangsverknüpfung',
    }"
    inline
  >
    <template v-if="aktion === 'erstellt'">
      <UserTag
        :user="ersteller"
        class="font-semibold"
        inline
      />
      hat den Vorgang mit
      <router-link
        :to="link"
      >
        <span
          class="text-base font-semibold"
          v-text="verknuepfterVorgang.attributes.titel"
        />
      </router-link>
      verknüpft
    </template>

    <template v-else-if="aktion === 'entfernt'">
      <UserTag
        :user="ersteller"
        class="font-semibold"
        inline
      />
      hat die Verknüpfung zu
      <router-link
        :to="link"
      >
        <span
          class="text-base font-semibold"
          v-text="verknuepfterVorgang.attributes.titel"
        />
      </router-link>
      aufgehoben
    </template>
  </TimelineElement>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';
import { RouteLocationRaw } from 'vue-router';

import TimelineElement from '../../components/TimelineElement/TimelineElement.vue';
import UserTag from '../../components/tags/UserTag.vue';
import useCurrentUser from '../../components/users/useCurrentUser';
import { injectStore } from '../../store/resources/composition';
import type { TimelineEintragResource } from '../../store/resources/types';

const props = defineProps<{
  element: TimelineEintragResource;
}>();

const store = injectStore();
// TODO support avatars for multiple ersteller types
const ersteller = computed(() => (
  store.users.find(props.element.relationships?.ersteller?.data?.id)
));
const verknuepfung = computed(() => (
  store.verknuepfungen.find(props.element.relationships?.element?.data?.id)
));
const verknuepfterVorgang = computed(() => store.vorgaenge.findRelated(
  verknuepfung.value?.relationships?.vorgang,
));
const { user } = useCurrentUser();

const aktion = computed(() => verknuepfung.value?.attributes.aktion);
const link = computed<RouteLocationRaw>(() => ({
  name: 'vorgaenge.show',
  params: {
    firmaId: user.value?.attributes.firmaId,
    vorgangsnummer: verknuepfterVorgang.value?.attributes.vorgangsnummer,
  },
}));
</script>
