import { shallowRef } from 'vue';

import type { ComponentDict, Domain } from './types';

export const sidebarRegistry = shallowRef<ComponentDict>({});
export const timelineRegistry = shallowRef<ComponentDict>({});

export const createDomain = <T extends Domain>(config: T): T => config;

export const registerDomain = ({
  components = {},
}: Domain): void => {
  const { sidebar, timeline } = components;

  if (sidebar !== undefined) {
    sidebarRegistry.value = { ...sidebarRegistry.value, ...sidebar };
  }

  if (timeline !== undefined) {
    timelineRegistry.value = { ...timelineRegistry.value, ...timeline };
  }
};
