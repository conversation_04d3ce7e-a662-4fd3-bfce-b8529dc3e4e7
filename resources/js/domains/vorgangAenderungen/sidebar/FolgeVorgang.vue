<template>
  <section data-test="vorgang__sidebar__related-vorgaenge">
    <h3
      v-if="folgeVorgaenge.length > 0"
      class="mb-2 text-left text-xs font-semibold uppercase leading-none tracking-wide text-gray-700 focus:outline-none"
    >
      VERKNÜPFTE VORGÄNGE
    </h3>
    <span
      v-for="folgeVorgang in folgeVorgaenge"
      :key="folgeVorgang?.id"
      data-test="vorgang__sidebar__related-vorgaenge__item"
    >
      <VorgangListItem
        v-if="folgeVorgang"
        class="mb-2"
        :vorgang="folgeVorgang"
        spaced
      />
    </span>
  </section>
</template>

<script setup lang="ts">
import VorgangListItem from '@/pages/vorgaenge/components/VorgangListItem.vue';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import { VorgangResource } from '@/store/resources/types';
import { RelationshipHasMany } from '@/types/jsonapi';

const store = injectStore();
const currentVorgang = useVorgangFromRoute();

const folgeVorgaenge = store.vorgaenge.findAllRelated(
  currentVorgang.value?.relationships?.folgeVorgaenge as  (RelationshipHasMany<VorgangResource>),
);
</script>
