<template>
  <div
    v-if="vorgang?.attributes.faelligAt"
    data-test="vorgang__sidebar__faelligkeit"
  >
    <component
      :is="disabled ? 'div' : DsDatepicker"
      v-model="nonValidatedFaelligAt"
      :datepicker-config="datePickerConfig"
      class="right-0"
      data-test="vorgang__sidebar__faelligkeit__datepicker"
    >
      <SidebarCta
        action-icon="cog"
        label="Fällig zum"
        :title="tooltip"
        :disabled="disabled"
      />
    </component>

    <TimeAgoTag
      :value="vorgang.attributes.faelligAt"
      :no-urgency-styling="vorgang.attributes.status === VorgangStatus.Erledigt"
    />
  </div>
</template>

<script setup lang="ts">
import { DsDatepicker } from '@demvsystems/design-components';
import { format, parseISO, subDays } from 'date-fns';
import { computed } from 'vue';

import TimeAgoTag from '@/components/tags/TimeAgoTag.vue';
import SidebarCta from '@/pages/vorgang/components/SidebarCta.vue';
import { useVorgangFromRoute } from '@/store/resources/composition';
import { Vorgangsart } from '@/store/resources/types';
import { VorgangStatus } from '@/types';

const vorgang = useVorgangFromRoute();

const nonValidatedFaelligAt = computed({
  get: () => parseISO(vorgang.value?.attributes?.faelligAt ?? ''),
  set: (value) => {
    // only change vorgag.attributes.faelligAt if date is in the future
    if (value > subDays(new Date(), 1) && vorgang.value) {
      vorgang.value.attributes.faelligAt = format(value, 'yyyy-MM-dd');
    }
  },
});

const datePickerConfig = {
  lowerLimit: new Date(),
};

const disabled = computed(() => (
  vorgang.value?.attributes.vorgangsart === Vorgangsart.Vorgangsgruppe
));

const tooltip = computed(() => {
  if (disabled.value) {
    return 'Das Fälligkeitsdatum der Vorgangsgruppe wird automatisch vom nächsten fälligen Untervorgang übernommen';
  }

  return 'Fälligkeitsdatum ändern';
});
</script>
