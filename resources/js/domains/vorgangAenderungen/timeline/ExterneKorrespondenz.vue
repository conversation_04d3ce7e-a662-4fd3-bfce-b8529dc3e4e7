<template>
  <TimelineElement
    v-if="author !== undefined && externeKorrespondenz?.attributes !== undefined"
    :element="element"
    :icon-config="{
      name: 'upload',
      helpText: 'Externe Korrespondenz',
    }"
    inline
  >
    hat eine neue Korrespondenz
    <span
      class="break-words font-semibold"
      v-text="file?.attributes.name"
    />
    hinzugefügt
  </TimelineElement>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import TimelineElement from '@/components/TimelineElement/TimelineElement.vue';
import { injectStore } from '@/store/resources/composition';
import type { TimelineEintragResource } from '@/store/resources/types';

const props = defineProps<{
  element: TimelineEintragResource,
}>();

const store = injectStore();
const author = computed(
  () => store.users.find(props.element.relationships?.owner?.data?.id),
);

const externeKorrespondenz = computed(
  () => store.externeKorrespondenzen.find(props.element.relationships?.element?.data?.id),
);

const file = computed(
  () =>  store.files.findAllRelated(externeKorrespondenz?.value?.relationships?.files)?.[0],
);
</script>
