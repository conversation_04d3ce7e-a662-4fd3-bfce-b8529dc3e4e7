<template>
  <TimelineElement
    :element="element"
    :icon-config="{
      name: 'file-plus',
      helpText: 'Verträge wurden hinzugefügt',
    }"
    inline
  >
    hat dem Vorgang {{ usePlural ? 'Verträge' : 'einen Vertrag' }} hinzugefügt
  </TimelineElement>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import TimelineElement from '@/components/TimelineElement/TimelineElement.vue';
import { useVorgangFromRoute } from '@/store/resources/composition';
import { TimelineEintragResource } from '@/store/resources/types';

defineProps<{
  element: TimelineEintragResource,
}>();

const vorgang = useVorgangFromRoute();

const usePlural = computed(() => {
  if (vorgang.value?.relationships?.vertraege?.data === undefined) {
    return false;
  }

  return vorgang.value?.relationships?.vertraege.data.length > 1;
});
</script>
