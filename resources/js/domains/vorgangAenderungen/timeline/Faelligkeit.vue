<template>
  <TimelineElement
    v-if="
      author !== undefined
        && neuerWert !== undefined
        && vorherigerWert !== undefined
    "
    :icon-config="{
      name: 'clock',
      helpText: 'Fälligkeitsdatum geändert',
    }"
    :element="element"
    inline
  >
    hat das Fälligkeitsdatum auf
    <TimeAgoText
      class="font-bold"
      :value="neuerWert"
      only-date
    />
    geändert
  </TimelineElement>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import TimelineElement from '@/components/TimelineElement/TimelineElement.vue';
import TimeAgoText from '@/components/tags/TimeAgoText.vue';
import { injectStore } from '@/store/resources/composition';
import type { TimelineEintragResource } from '@/store/resources/types';

const props = defineProps<{
  element: TimelineEintragResource;
}>();

const store = injectStore();

const faelligkeitAnderung = computed(() =>
  store.faelligkeitAenderungen.find(props.element.relationships?.element?.data?.id),
);

const author = computed(() =>
  store.users.find(props.element.relationships?.owner?.data?.id),
);

// TODO: Delete vorherigerWert if not needed.
const vorherigerWert = faelligkeitAnderung?.value?.attributes?.vorherigerWert;
const neuerWert = faelligkeitAnderung?.value?.attributes?.neuerWert;
</script>
