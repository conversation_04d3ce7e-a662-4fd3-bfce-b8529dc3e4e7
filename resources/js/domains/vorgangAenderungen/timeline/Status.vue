<template>
  <TimelineElement
    v-if="
      author !== undefined
        && neuerWert !== undefined
        && vorherigerWert !== undefined
    "
    :icon-config="{
      name: 'arrow-right',
      helpText: 'Statusänderung',
    }"
    :element="element"
    inline
  >
    hat den Status zu
    <VorgangStatus :status="neuerWert" />
    geändert
  </TimelineElement>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import TimelineElement from '@/components/TimelineElement/TimelineElement.vue';
import VorgangStatus from '@/components/VorgangStatus.vue';
import { injectStore } from '@/store/resources/composition';
import type { TimelineEintragResource } from '@/store/resources/types';

const props = defineProps<{
  element: TimelineEintragResource;
}>();

const store = injectStore();

const statusAnderung = computed(() =>
  store.statusAenderungen.find(props.element.relationships?.element?.data?.id),
);

const author = computed(() =>
  store.users.find(props.element.relationships?.owner?.data?.id),
);

const vorherigerWert = statusAnderung?.value?.attributes?.vorherigerWert;
const neuerWert = statusAnderung?.value?.attributes?.neuerWert;
</script>
