<template>
  <TimelineElement
    :icon-config="{
      name: 'trash',
      helpText: 'Element gelöscht',
    }"
    :element="element"
  >
    <p>
      {{ message }} wurde am <b>{{ deleteAtDate }}</b>
      von <b>{{ loeschKommentar?.attributes?.deletedBy }}</b>
      gelöscht mit folgender Begründung:
    </p>
    <p>{{ loeschKommentar?.attributes?.content }}</p>
  </TimelineElement>
</template>

<script setup lang="ts">
import { format, parseISO } from 'date-fns';
import { de } from 'date-fns/locale';
import { computed } from 'vue';

import TimelineElement from '@/components/TimelineElement/TimelineElement.vue';
import { injectStore } from '@/store/resources/composition';
import type { TimelineEintragResource } from '@/store/resources/types';

const props = defineProps<{
  element: TimelineEintragResource,
}>();

const store = injectStore();
const loeschKommentar = computed(
  () => store.loeschKommentare.find(props.element.relationships?.element?.data?.id),
);
const deleteAtDate = computed(
  () => {
    if (loeschKommentar.value?.attributes?.createdAt === undefined) {
      return '';
    }

    const date = parseISO(loeschKommentar.value.attributes.createdAt);

    return format(date, 'PPPPpp', { locale: de });
  },
);

const message = computed(
  () => {
    const elementType = loeschKommentar.value?.attributes.elementType;
    if (elementType === 'korrespondenzen') {
      return 'Diese Korrespondenz';
    }

    if (elementType === 'kommentare') {
      return 'Dieser Kommentar';
    }

    if (elementType === 'gespraechsnotizen') {
      return 'Diese Gesprächsnotiz';
    }

    return 'Dieses Timeline-Element';
  },
);
</script>
