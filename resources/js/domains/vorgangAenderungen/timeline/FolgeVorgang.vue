<template>
  <TimelineElement
    v-if="author !== undefined && folgeVorgangElement?.attributes !== undefined"
    :element="element"
    :icon-config="{
      name: 'fast-forward',
      helpText: 'Folgevorgang hinzugefügt',
    }"
    inline
  >
    hat den Folgevorgang
    <router-link
      v-if="firmaId && nachfolger?.attributes.vorgangsnummer"
      class="font-semibold text-blue-700 hover:underline"
      :to="{
        name: 'vorgaenge.show',
        params: {
          firmaId,
          vorgangsnummer: nachfolger?.attributes.vorgangsnummer,
        },
      }"
    >
      {{ `${nachfolger?.attributes.titel} (${nachfolger?.attributes.vorgangsnummer})` }}
    </router-link>

    erstellt
  </TimelineElement>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import TimelineElement from '@/components/TimelineElement/TimelineElement.vue';
import useCurrentUser from '@/components/users/useCurrentUser';
import { injectStore } from '@/store/resources/composition';
import type { TimelineEintragResource } from '@/store/resources/types';
const props = defineProps<{
  element: TimelineEintragResource,
}>();

const store = injectStore();
const author = computed(
  () => store.users.find(props.element.relationships?.owner?.data?.id),
);

const { user } = useCurrentUser();
const firmaId = computed(() => user.value?.attributes.firmaId);

// folgeVorgangElement !== nachfolger.
const folgeVorgangElement = computed(
  () => store.folgeVorgangElemente.find(props.element.relationships?.element?.data?.id),
);

const nachfolger = computed(
  () => store.vorgaenge.find(folgeVorgangElement.value?.attributes.nachfolger_id),
);
</script>
