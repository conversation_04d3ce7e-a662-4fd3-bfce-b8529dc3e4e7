<template>
  <TimelineElement
    v-if="author !== undefined && userAenderung?.attributes !== undefined"
    :element="element"
    :icon-config="iconConfig"
    :first="index === 0"
    inline
  >
    hat
    <span
      class="font-semibold"
      v-text="userAenderung.attributes.assignedUser"
    />
    {{ complementaryText }}
  </TimelineElement>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import TimelineElement from '@/components/TimelineElement/TimelineElement.vue';
import { injectStore } from '@/store/resources/composition';
import type { TimelineEintragResource } from '@/store/resources/types';

const props = defineProps<{
  element: TimelineEintragResource;
  index?: number;
}>();

const index = props.index ?? 0;

const store = injectStore();

const author = computed(() =>
  store.users.find(props.element.relationships?.owner?.data?.id),
);

const userAenderung = computed(() =>
  store.userAenderung.find(props.element.relationships?.element?.data?.id),
);

let complementaryText = '';

if (userAenderung.value?.attributes !== undefined) {
  const taskString =
    userAenderung.value.attributes.task === 'bearbeiter'
      ? 'Bearbeiter'
      : 'Beobachter';
  const actionString =
    userAenderung.value.attributes.action === 'entfernt'
      ? 'entfernt'
      : 'hinzugefügt';

  complementaryText = `als ${taskString} ${actionString} `;
}

const iconConfig = {
  name:
    userAenderung.value?.attributes.task === 'bearbeiter' ? 'user' : 'eye',
  helpText:
    userAenderung.value?.attributes.task === 'bearbeiter'
      ? 'Bearbeiter hinzugefügt/entfernt'
      : 'Beobachter hinzugefügt/entfernt',
};
</script>
