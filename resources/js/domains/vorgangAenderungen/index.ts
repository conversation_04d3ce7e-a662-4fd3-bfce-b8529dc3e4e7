import { createDomain } from '../registry';

import FaelligkeitSidebar from './sidebar/Faelligkeit.vue';
import FolgeVorgangSidebar from './sidebar/FolgeVorgang.vue';
import ExterneKorrespondenz from './timeline/ExterneKorrespondenz.vue';
import FaelligkeitElement from './timeline/Faelligkeit.vue';
import FolgeVorgang from './timeline/FolgeVorgang.vue';
import LoeschKommentar from './timeline/LoeschKommentar.vue';
import StatusElement from './timeline/Status.vue';
import UserElement from './timeline/User.vue';
import VertragAenderung from './timeline/VertragAenderung.vue';

export default createDomain({
  components: {
    sidebar: {
      faelligkeitAenderungen: FaelligkeitSidebar,
      folgeVorgangElemente: FolgeVorgangSidebar,
    },

    timeline: {
      faelligkeitAenderungen: FaelligkeitElement,
      statusAenderungen: StatusElement,
      // todo: change userAenderung to userAenderungen
      userAenderung: UserElement,
      externeKorrespondenzen: ExterneKorrespondenz,
      folgeVorgangElemente: FolgeVorgang,
      loeschKommentare: LoeschKommentar,
      vertragAenderungen: VertragAenderung,
    },
  },
});
