<template>
  <TimelineElement
    v-if="korrespondenz !== undefined"
    :element="element"
    :icon-config="iconConfig"
    :first="index === 0"
    :owner-name="absenderStr"
  >
    <div class="border-b-2 py-3">
      <span
        class="font-bold text-gray-800"
        v-text="korrespondenz.attributes.betreff"
      />
    </div>
    <div
      v-if="contentIsLoading"
      class="mt-1 grow space-y-2"
    >
      <DsSkeleton class="block h-3 w-10/12 rounded opacity-75" />
      <DsSkeleton class="block h-3 w-8/12 rounded opacity-50" />
    </div>
    <DsAlert
      v-else-if="sanitizedContent === null"
      label="Fehler beim Laden der Inhalte"
      type="error"
    >
      Der Inhalt dieser Korrespondenz konnte nicht geladen werden.
      Bitte laden sie die Seite erneut.
    </DsAlert>
    <div
      v-else
      ref="htmlContent"
      class="html-preview"
      :class="{'line-clamp-12 max-h-80': !isHtmlContentExpanded}"
      v-html="sanitizedContent"
    />
    <DsButton
      v-if="htmlContent?.scrollHeight !== htmlContent?.clientHeight && !isHtmlContentExpanded"
      icon="angle-right"
      icon-align="right"
      variant="outline"
      size="sm"
      @click="isHtmlContentExpanded = true"
    >
      Mehr anzeigen
    </DsButton>
    <template #actions>
      <div class="flex gap-2">
        <DsBadge
          v-if="korrespondenz.attributes.displayErrorBadge"
          type="error"
        >
          Versand fehlgeschlagen
        </DsBadge>
        <div class="flex flex-col gap-2">
          <DsButton
            v-if="korrespondenz.attributes.versandart === 'brief'"
            icon="at"
            variant="secondary"
            size="sm"
            class="shrink-0"
            @click="openNachrichtAction"
          >
            Als E-Mail
          </DsButton>
          <DetailsModal
            v-if="vorgang !== undefined"
            :disabled="korrespondenz.attributes.content === undefined"
            :vorgang="vorgang"
            :korrespondenz="korrespondenz"
            :files="files"
          />
        </div>
      </div>
    </template>
  </TimelineElement>
</template>

<script setup lang="ts">
import { DsAlert, DsBadge, DsButton, DsSkeleton } from '@demvsystems/design-components';
import axios from 'axios';
import DOMPurify from 'dompurify';
import { computed, ref, watch } from 'vue';

import TimelineElement from '@/components/TimelineElement/TimelineElement.vue';
import { IconConfig } from '@/components/TimelineElement/types';
import { EmailType } from '@/pages/vorgangAnlegen/types';
import { Versandart } from '@/pages/vorlagen/types';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import { eventBus } from '@/store/resources/store';
import type { FileResource, TimelineEintragResource } from '@/store/resources/types';
import { isEmailElement } from '@/store/resources/types';

import DetailsModal from './DetailsModal.vue';

const props = defineProps<{
  element: TimelineEintragResource
  index?: number
}>();

const store = injectStore();

const contentIsLoading = ref(false);

const korrespondenz = computed(() => {
  switch (props.element.relationships?.element?.data?.type) {
    case 'erinnerungen':
      return store.erinnerungen.find(props.element.relationships?.element?.data?.id);
    case 'mahnungen':
      return store.mahnungen.find(props.element.relationships?.element?.data?.id);
    default:
      return store.korrespondenzen.find(props.element.relationships?.element?.data?.id);
  }
});

const openNachrichtAction = () => {
  eventBus.emit(
    'openNachrichtVerfassen',
    store.korrespondenzen.find(props.element.relationships?.element?.data?.id),
  );
};

function getIconConfigForEmailKorrespondenz(emailType: EmailType | undefined): IconConfig {
  switch (emailType) {
    case EmailType.Outgoing:
      return {
        name: 'at',
        helpText: 'E-Mail',
        color: 'blue',
      };
    case EmailType.Incoming:
    default:
      return {
        name: 'at',
        helpText: 'E-Mail',
        color: 'green',
      };
  }
}

const iconConfig = computed<IconConfig>(() => {
  if (korrespondenz.value?.type === 'erinnerungen') {
    return {
      name: 'alarm-clock',
      helpText: 'Erinnerung',
      color: 'orange',
    } as IconConfig;
  }

  if (korrespondenz.value?.type === 'mahnungen') {
    return {
      name: 'exclamation-triangle',
      helpText: 'Mahnung',
      color: 'red',
    } as IconConfig;
  }

  if (korrespondenz.value?.attributes.versandart === 'email') {
    return getIconConfigForEmailKorrespondenz(korrespondenz.value?.attributes.emailType);
  }

  return {
    name: 'envelope',
    helpText: 'Brief',
    color: 'blue',
  } as IconConfig;
});

const vorgang = useVorgangFromRoute();

const files = computed<FileResource[]>(() => (
  store.files.findAllRelated(korrespondenz.value?.relationships?.files)
));

const htmlContent = ref<null | HTMLElement>(null);
const isHtmlContentExpanded = ref(false);

const absenderStr = computed((): string | undefined => {
  if (korrespondenz.value?.attributes.versandart === Versandart.Brief) {
    return undefined;
  }

  const absender = korrespondenz.value?.attributes.absender ?? undefined;

  if (absender === undefined || !isEmailElement(absender)) {
    return undefined;
  }

  if (korrespondenz.value?.attributes.emailType === EmailType.Outgoing) {
    return undefined;
  }

  const hasName = absender.name !== undefined && absender.name !== '';

  return hasName ? `${absender.name} <${absender.email}>` : absender.email;
});

watch(korrespondenz, async () => {
  if (korrespondenz.value?.links?.content === undefined) {
    return;
  }

  if (korrespondenz.value?.attributes.content !== undefined) {
    return;
  }

  contentIsLoading.value = true;

  try {
    const response = await axios.get<{ content: string }>(korrespondenz.value.links.content);
    korrespondenz.value.attributes.content = response.data.content;
  } catch (e) { /* empty */ } finally {
    contentIsLoading.value = false;
  }
}, {
  immediate: true,
});

const sanitizedContent = computed(() => {
  if (korrespondenz.value?.attributes.content === undefined) {
    return null;
  }

  return DOMPurify.sanitize(korrespondenz.value.attributes.content, {
    FORBID_TAGS: ['style'],
  });
});
</script>
