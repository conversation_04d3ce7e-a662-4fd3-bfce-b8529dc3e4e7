<template>
  <DsButton
    variant="secondary"
    size="sm"
    icon="eye"
    :disabled="disabled"
    @click="modalOpen = true"
  >
    Details
  </DsButton>

  <DsModal
    :show="modalOpen"
    size="md"
    anchor="top"
    hide-buttons
    custom-content
    @close="modalOpen = false"
  >
    <div
      class="flex flex-col"
      style="max-height: calc(100vh - 7rem)"
    >
      <DsAlert
        v-if="korrespondenz.attributes.displayErrorBadge"
        type="error"
        label="Fehler beim Versand"
        class="mb-4"
      >
        {{ korrespondenz.attributes.errorMessage }}
      </DsAlert>
      <KorrespondenzDetails
        v-if="vorgang"
        :vorgang="vorgang"
        :korrespondenz="korrespondenz"
        :files="files"
        :expected-sender="expectedSender"
        :class="{'bg-gray-100': korrespondenz.attributes.versandart === 'brief'}"
        class="-mx-4 -mt-4 overflow-auto p-6 sm:-mx-6 sm:-mt-6"
      />

      <footer class="-mx-6 flex items-center justify-end space-x-4 border-t px-6 pt-6">
        <DsButton
          v-if="korrespondenz.attributes.versandart === 'brief'"
          size="lg"
          variant="secondary"
          icon="download"
          :href="pdfDownloadUrl"
          external
        >
          PDF herunterladen
        </DsButton>

        <DsButton
          v-if="!$slots.cta"
          size="lg"
          @click="modalOpen = false"
        >
          Schließen
        </DsButton>

        <slot name="cta" />
      </footer>
    </div>
  </DsModal>
</template>

<script setup lang="ts">
import { DsAlert, DsButton, DsModal } from '@demvsystems/design-components';
import { computed, ref } from 'vue';

import KorrespondenzDetails from '@/pages/korrespondenzDetails/korrespondenzDetails.vue';
import {
  BasicVorgangResource,
  EmailElement,
  ErinnerungResource,
  FileResource,
  KorrespondenzResource,
  KorrespondenzStatus,
  MahnungResource,
} from '@/store/resources/types';
import { getPdfDownloadUrl } from '@/utils/pdfUrls';

const props = defineProps<{
  korrespondenz: KorrespondenzResource | ErinnerungResource | MahnungResource,
  vorgang: BasicVorgangResource,
  files: FileResource[],
  disabled?: boolean,
}>();

const modalOpen = ref(false);

// get expectedSender and log errors
const expectedSender = computed(() => {
  if (props.korrespondenz.attributes.versandart === 'brief') {
    return undefined; // no expected sender for brief.
  }

  if (
    props.korrespondenz.attributes.status === KorrespondenzStatus.Fehler
  ) {
    return undefined;
  }

  return props.korrespondenz.attributes.absender as EmailElement | undefined;
});

const pdfDownloadUrl = computed(() => {
  if (props.korrespondenz.attributes.versandart === 'brief') {
    return getPdfDownloadUrl(props.vorgang.id, props.korrespondenz.id);
  }

  return undefined;
});
</script>
