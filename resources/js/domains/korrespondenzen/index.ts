import { createDomain } from '../registry';

import TimelineElement from './components/element.vue';

export default createDomain({
  components: {
    timeline: {
      korrespondenzen: TimelineElement,
      erinnerungen: TimelineElement,
      mahnungen: TimelineElement,
    },
  },

  vorgangsArt: {
    name: 'korrespondenzen',
    title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    icon: 'comments-alt',
    description: 'E-Mail oder Brief an Kunde oder Gesellschaft',
  },
});
