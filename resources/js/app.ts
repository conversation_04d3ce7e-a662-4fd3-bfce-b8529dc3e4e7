import * as Sentry from '@sentry/vue';
import { createPinia } from 'pinia';
import { createApp } from 'vue';
import '../css/app.css';

import App from '@/pages/App.vue';
import router from '@/router';

import './icons';

const app = createApp(App);

if (
  import.meta.env.PROD
  && import.meta.env.VITE_SENTRY_DSN !== undefined
) {
  Sentry.init({
    app,
    dsn: import.meta.env.VITE_SENTRY_DSN,
    integrations: [
      Sentry.browserTracingIntegration({ router }),
    ],
    tracesSampleRate: Number.parseFloat(
      `${import.meta.env.VITE_SENTRY_TRACES_SAMPLE_RATE}`,
    ),
  });
}

app.use(createPinia())
  .use(router)
  .mount('#app');
