<template>
  <SideList
    :is-loading="isLoading"
    actions-button-text="# Vorlagen ausgewählt"
    data-test="vorlagenliste"
  >
    <template #form>
      <DsInput
        v-model="filterString"
        inline
        class="grow"
        icon="search"
        placeholder="Suche nach Vorlagenname"
        data-test="vorlagenliste__search-input"
      />
    </template>
    <template #actions>
      <p
        v-if="meta !== undefined"
        class="text-xs font-semibold uppercase leading-none tracking-wide text-gray-700"
        data-test="vorlagenliste__results"
      >
        <samp>
          {{ meta.total }}
          {{ meta.total === 1 ? 'Ergebnis' : 'Ergebnisse' }}
        </samp>
      </p>
    </template>
    <template #nav>
      <!-- empty list -->
      <div
        v-if="vorlagen.length === 0"
        class="p-3 py-3.5 text-center text-gray-800"
      >
        Keine Vorlagen {{
          filterString === ''
            ? 'vorhanden'
            : `für "${filterString}" gefunden`
        }}.
      </div>

      <DsVirtualList
        v-else
        :items="vorlagen"
        :item-height="73"
        :load-next-handler="loadNext"
        :scroll-throttle="200"
        item-class="border-b"
        outer-container-class="overflow-y-auto flex-1 min-h-0"
      >
        <template #default="{item: vorlage}">
          <VorlageListItem
            :key="vorlage.id"
            :vorlage="vorlage"
          />
        </template>
      </DsVirtualList>
    </template>
  </SideList>
</template>

<script setup lang="ts">
import { DsInput, DsVirtualList } from '@demvsystems/design-components';
import { computed, onBeforeUnmount, ref, watch } from 'vue';

import { get } from '@/api';
import SideList from '@/components/sideList/SideList.vue';
import { eventBus } from '@/store/resources/store';
import { VorlageResource } from '@/store/resources/types';
import { useVorgangstypenStore } from '@/stores/vorgangstypenStore';

import useVorlageListStore from '../../uses/useVorlageListStore';

import VorlageListItem from './VorlageListItem.vue';

const {
  next,
  store,
  meta,
  isLoading: isVorlageListLoading,
  reloadVorlagenList,
} = useVorlageListStore();

const vorgangstypenStore = useVorgangstypenStore();
const filterString = ref('');

const isLoading = computed(() => isVorlageListLoading && vorgangstypenStore.isLoading);

eventBus.on('vorlageErstellt', () => {
  filterString.value = '';
});
eventBus.on('vorlageGeloescht', () => {
  filterString.value = '';
});

function reloadAndFilterVorlagenList(query: string) {
  reloadVorlagenList({
    include: [
      'vorlage',
      'vorlage.vorgangTyp',
      'ersteller',
    ],
    find: {
      key: query,
      fields: {
        vorlagen: ['name'],
      },
    },
  });
}

watch(filterString, reloadAndFilterVorlagenList);
// clears the filter when the user navigates away.
onBeforeUnmount(() => reloadAndFilterVorlagenList(''));

async function loadNext() {
  if (next.value === null) {
    return;
  }

  const response = await get<VorlageResource>(next.value);

  store.load(response.data);

  next.value = response.data.links?.next ?? null;
}

const vorlagen = computed(() => store.vorlagen.getAll());
</script>
