<template>
  <div class="relative z-0 flex flex-1 grow flex-col">
    <div class="relative z-10 flex px-4 py-3 shadow-sm sm:px-6">
      <h2
        class="mx-auto flex w-full text-xl font-semibold text-gray-800 md:max-w-lg"
        @click="magma"
      >
        Einstellungen
        <AutoSaveIcon />
      </h2>
    </div>
    <div class="overflow-y-auto p-4 sm:p-6">
      <DsForm
        class="mx-auto flex flex-col space-y-7 md:max-w-lg"
        :validation-errors="formErrors"
        @submit.prevent
      >
        <template v-if="userSettingsStore.isInitialized">
          <UserSettings />
          <FirmenSettings
            v-if="user?.attributes?.canFirmendatenVerwalten"
            @emit-errors="formErrors = $event"
          />
        </template>

        <template v-else>
          <div class="space-y-1.5">
            <DsSkeleton
              id="skeleton"
              class="h-5 w-48"
            />
            <DsSkeleton class="h-8 w-96" />
          </div>
          <div class="space-y-1.5 opacity-50">
            <DsSkeleton class="h-5 w-48" />
            <DsSkeleton class="h-8 w-96" />
          </div>
          <div class="space-y-1.5 opacity-25">
            <DsSkeleton class="h-5 w-48" />
            <DsSkeleton class="h-8 w-96" />
          </div>
        </template>
      </DsForm>
      <div
        v-if="count >= 10"
        class="m-auto mt-7 flex flex-col px-6 md:max-w-lg md:px-0"
      >
        <h2 class="text-lg font-bold">
          --- CREDITS ---
        </h2>
        <h3 class="my-1 font-bold">
          From team magma, with <span class="text-red-700">&lt;3</span> :
        </h3>
        <ul class="ml-4">
          <li>Jan</li>
          <li>Alex</li>
          <li>Jannik H.</li>
          <li>Jannik B.</li>
          <li>Rrez</li>
          <li>Hagop</li>
          <li>Tim</li>
        </ul>

        <h3 class="my-1 font-bold">
          No longer with us:
        </h3>
        <ul class="ml-4">
          <li><i>Martin</i></li>
          <li><i>Kian</i></li>
          <li><i>Leonie</i></li>
          <li><i>Keno</i></li>
          <li><i>Phillip</i></li>
          <li><i>Henrik</i></li>
          <li><i>Alonso</i></li>
          <li><i>Kim</i></li>
          <li><i>Chris</i></li>
          <li><i>Stephan</i></li>
          <li><i>Sir Hamster Williams</i></li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsForm, DsSkeleton } from '@demvsystems/design-components';
import { onMounted, ref } from 'vue';

import useCurrentUser from '@/components/users/useCurrentUser';
import AutoSaveIcon from '@/pages/settings/components/AutoSaveIcon.vue';
import FirmenSettings from '@/pages/settings/components/FirmenSettings.vue';
import UserSettings from '@/pages/settings/components/UserSettings.vue';

import { useUserSettingsStore } from './stores/userSettingsStore';

const count = ref(0);

const { user } = useCurrentUser();

const formErrors = ref({});

function magma() {
  count.value++;
}

const userSettingsStore = useUserSettingsStore();

onMounted(() => {
  void userSettingsStore.load();
});
</script>
