import axios, { AxiosResponse } from 'axios';

import {
  useVorgangAnlegenStore,
} from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/vorgangAnlegenStore';

export function useVertriebsweg(): {
  setVertriebswegId: (
    vertragId: string,
    vorgangTypId: string,
  ) => Promise<void>,
} {
  const vorgangAnlegenStore = useVorgangAnlegenStore();

  async function setVertriebswegId(vertragId: string, vorgangTypId: string): Promise<void> {
    try {
      const response: AxiosResponse<{ id: string | null }> = await axios.get(`/api/vertriebsweg/vertrag/${vertragId}/vorgangtyp/${vorgangTypId}`);

      vorgangAnlegenStore.vertriebswegIdProxy = response?.data.id ?? null;
    } catch {
      // ignore
    }
  }

  return {
    setVertriebswegId,
  };
}
