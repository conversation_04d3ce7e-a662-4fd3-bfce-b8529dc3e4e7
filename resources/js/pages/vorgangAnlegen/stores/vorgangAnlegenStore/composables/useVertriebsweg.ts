import axios, { AxiosResponse } from 'axios';

export function useVertriebsweg(): {
  fetchVertriebswegId: (
    vertragId: string,
    vorgangTypId: string,
  ) => Promise<string | null>,
  fetchVertriebswegIdByGesellschaft: (
    gesellschaftId: string,
    vorgangTypId: string,
  ) => Promise<string | null>,
} {
  async function fetchVertriebswegId(
    vorgangTypId: string,
    vertragId: string,
  ): Promise<string | null> {
    try {
      const response: AxiosResponse<{ id: string | null }> = await axios.get(`/api/vertriebsweg/vorgangTyp/${vorgangTypId}/vertrag/${vertragId}`);

      return response?.data.id ?? null;
    } catch {
      return null;
    }
  }

  async function fetchVertriebswegIdByGesellschaft(
    vorgangTypId: string,
    gesellschaftId: string,
  ): Promise<string | null> {
    try {
      const response: AxiosResponse<{ id: string | null }> = await axios.get(`/api/vertriebsweg/vorgangTyp/${vorgangTypId}/gesellschaft/${gesellschaftId}`);

      return response?.data.id ?? null;
    } catch {
      return null;
    }
  }

  return {
    fetchVertriebswegId,
    fetchVertriebswegIdByGesellschaft,
  };
}
