<template>
  <Accordion
    v-model="erweiterteKundensuche.accordionIsOpen.beziehungen"
    title="Beziehungen"
  >
    <div class="grid grid-cols-2 gap-x-4 p-1">
      <DsFormGroup
        label="Kinder"
        validation-name="kinder"
      >
        <DsRadioGroup
          v-model="kinderProxy"
          variant="button"
          data-test="erweiterte-kundensuche__kundenakte__kinder"
        >
          <DsRadioButton
            :value="KundensucheRadioOptions.Ja"
          >
            Ja
          </DsRadioButton>
          <DsRadioButton
            :value="KundensucheRadioOptions.Nein"
          >
            Nein
          </DsRadioButton>
          <DsRadioButton
            :value="KundensucheRadioOptions.NichtBeachten"
          >
            Nicht beachten
          </DsRadioButton>
        </DsRadioGroup>
      </DsFormGroup>

      <div class="grid gap-x-4 md:grid-cols-2">
        <DsFormGroup
          label="Alter von"
          validation-name="kinderAlter.von"
        >
          <DsInput
            v-model="erweiterteKundensuche.kinderAlter.von"
            type="number"
            data-test="erweiterte-kundensuche__kundenakte__kinderalter-von"
          />
        </DsFormGroup>
        <DsFormGroup
          label="Alter bis"
          validation-name="kinderAlter.bis"
        >
          <DsInput
            v-model="erweiterteKundensuche.kinderAlter.bis"
            type="number"
            data-test="erweiterte-kundensuche__kundenakte__kinderalter-bis"
          />
        </DsFormGroup>
      </div>
    </div>
  </Accordion>
</template>

<script setup lang="ts">
import { DsFormGroup, DsInput, DsRadioButton, DsRadioGroup } from '@demvsystems/design-components';
import { storeToRefs } from 'pinia';

import Accordion
  from '@/components/Accordion.vue';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';
import { KundensucheRadioOptions } from '@/pages/kampagnen/types';

import { useKundensucheRadioOptions } from './useKundensucheRadioOptions';

const erweiterteKundensuche = useErweiterteKundensucheStore();

const { createProxy } = useKundensucheRadioOptions();
const { kinder } = storeToRefs(useErweiterteKundensucheStore());

const kinderProxy = createProxy(kinder);
</script>
