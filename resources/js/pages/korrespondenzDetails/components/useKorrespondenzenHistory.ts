import axios from 'axios';

import {
  BasicVorgangResource,
  ErinnerungResource,
  KorrespondenzResource,
  MahnungResource,
} from '@/store/resources/types';

export const useKorrespondenzenHistory = (): {
  getHistoryForKorrespondenz: (
  vorgang: BasicVorgangResource,
  korrespondenz: (KorrespondenzResource | MahnungResource | ErinnerungResource)
  ) => Promise<string>;
} => {
  const getHistoryForKorrespondenz = async (
    vorgang: BasicVorgangResource,
    korrespondenz: (KorrespondenzResource | MahnungResource | ErinnerungResource),
  ) => {
    return axios.get(
      `/api/vorgaenge/${vorgang.id}/${korrespondenz.type}/${korrespondenz.id}/history`,
    ).then(
      (response) =>  response.data as string ?? '',
    );
  };

  return {
    getHistoryForKorrespondenz,
  };
};
