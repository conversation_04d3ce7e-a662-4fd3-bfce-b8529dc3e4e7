<template>
  <DsSelect
    v-model="model"
    :data="filteredGesellschaften"
    :search-keys="['attributes.name']"
    :item-height="28"
    :is-loading="isLoading"
    :disabled="disabled ?? false"
    :value-key="valueKey"
    virtualized
  >
    <template #entry="{entry}">
      <div
        class="truncate"
        :title="entry.attributes?.name"
      >
        {{ entry.attributes?.name }}
      </div>
    </template>
  </DsSelect>
</template>

<script setup lang="ts">
import { DsSelect } from '@demvsystems/design-components';
import { computed } from 'vue';

import { gesellschaften, isLoading } from './gesellschaftenList';

const {
  disabled,
  onlyPools,
  onlyGesellschaften,
  valueKey = 'id',
} = defineProps<{
  disabled?: boolean,
  onlyPools?: boolean,
  onlyGesellschaften?: boolean,
  valueKey?: string,
}>();

const model = defineModel<string | null>({ required: true });

const filteredGesellschaften = computed(() => {
  if (!onlyGesellschaften && !onlyPools) {
    return gesellschaften.value;
  }

  return gesellschaften.value.filter((gesellschaft) => {
    if (onlyGesellschaften) {
      return !gesellschaft.attributes.isPool;
    }

    return gesellschaft.attributes.isPool;
  });
});
</script>
