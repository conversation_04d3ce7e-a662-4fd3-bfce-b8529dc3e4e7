<template>
  <DsFormGroup
    label="Sparte"
    :required="required ?? false"
  >
    <DsSelect
      :model-value="modelValue ?? undefined"
      :data="sparten"
      :search-keys="['attributes.name', 'attributes.abkuerzung']"
      :is-loading="isLoading"
      :disabled="disabled ?? false"
      :required="required ?? false"
      :value-key="valueKey"
      data-test="basis-info__sparte__select"
      @update:model-value="emits('update:modelValue', $event)"
    >
      <template
        v-if="!isLoading"
        #entry="{entry}"
      >
        <span
          v-for="(part, idx) in entry.attributes.name.split('->')"
          :key="idx"
        >
          <span
            v-if="idx !== 0"
            class="text-gray-500"
          >-></span>{{ part }}
        </span>
      </template>
    </DsSelect>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsSelect } from '@demvsystems/design-components';

import { isLoading, sparten } from './spartenList';

withDefaults(defineProps<{
  modelValue: string | null,
  disabled?: boolean,
  required?: boolean,
  valueKey?: string,
}>(), {
  valueKey: 'id',
});
const emits = defineEmits<{
  (event: 'update:modelValue', sparteId: string | null): void
}>();
</script>
