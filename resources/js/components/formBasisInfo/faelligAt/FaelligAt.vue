<template>
  <DsFormGroup
    label="Fälligkeit"
    validation-name="faelligAt"
    required
  >
    <div class="flex flex-col-reverse gap-x-4 gap-y-3 sm:flex-row">
      <DsRadioGroup
        v-model="quickSelectProxy"
        class="shrink-0"
        variant="button"
      >
        <DsRadioButton
          value="ohne_faelligkeit"
          :data-test="`${dataTestPrefix}__ohne__faelligkeit`"
        >
          Ohne Fälligkeit
        </DsRadioButton>
        <DsRadioButton
          value="in_einer_woche"
          :data-test="`${dataTestPrefix}__faellig-at__in-einer-woche`"
        >
          In einer Woche
        </DsRadioButton>

        <DsRadioButton
          value="in_zwei_wochen"
          :data-test="`${dataTestPrefix}__faellig-at__in-zwei-wochen`"
        >
          In zwei Wochen
        </DsRadioButton>

        <DsRadioButton
          value="eigenes_datum"
          :data-test="`${dataTestPrefix}__faellig-at__eigenes-datum`"
        >
          Eigenes Datum
        </DsRadioButton>
      </DsRadioGroup>

      <DsInput
        :model-value="modelValue"
        class="faellig-at grow"
        type="date"
        :datepicker-config="datePickerConfig"
        :data-test="`${dataTestPrefix}__faellig-at`"
        :disabled="quickSelectProxy === 'ohne_faelligkeit'"
        @update:model-value="emits('update:modelValue', $event)"
      />
    </div>
  </DsFormGroup>
</template>

<script setup lang="ts">
import {
  DsInput,
  DsFormGroup,
  DatepickerConfig, DsRadioGroup, DsRadioButton,
} from '@demvsystems/design-components';
import { addWeeks, isSameDay } from 'date-fns';
import { computed } from 'vue';

type QuickSelection = 'in_einer_woche' | 'in_zwei_wochen' | 'eigenes_datum' | 'ohne_faelligkeit';

const props = defineProps<{
  modelValue?: Date | null,
  dataTestPrefix: string,
}>();

const emits = defineEmits<{
  (event: 'update:modelValue', faelligAt: Date | null): void
}>();

const datePickerConfig = <DatepickerConfig>{
  lowerLimit: new Date(),
};

const quickSelectProxy = computed({
  get: () => {
    if (props.modelValue && isSameDay(props.modelValue, addWeeks(new Date(), 1))) {
      return 'in_einer_woche';
    }

    if (props.modelValue && isSameDay(props.modelValue, addWeeks(new Date(), 2))) {
      return 'in_zwei_wochen';
    }

    if (props.modelValue === null) {
      return 'ohne_faelligkeit';
    }

    return 'eigenes_datum';
  },
  set: (selection: QuickSelection) => {
    switch (selection) {
      case 'ohne_faelligkeit':
        emits('update:modelValue', null);

        break;
      case 'in_einer_woche':
        emits('update:modelValue', addWeeks(new Date(), 1));

        break;
      case 'in_zwei_wochen':
        emits('update:modelValue', addWeeks(new Date(), 2));

        break;

      default:
        if (props.modelValue === null) {
          emits('update:modelValue', new Date());
        }
    }
  },
});
</script>

<style scoped>
::v-deep( .faellig-at .v3dp__popout ) {
  right: 0
}
</style>
