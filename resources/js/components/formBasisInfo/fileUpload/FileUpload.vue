<template>
  <DsFormGroup
    label="Neue Anhänge"
    validation-name="files.size"
  >
    <DsFileUpload
      v-model="fileItems"
      :accept="accept"
      :max-size="20"
      multiple
    />
  </DsFormGroup>
</template>

<script setup lang="ts">
import {
  DsFormGroup,
  DsFileUpload,
  FileUploadItem,
} from '@demvsystems/design-components';
import { ref, watch } from 'vue';

import { UploadedFileResource } from '@/store/resources/types';
import { toUploadedFileResource, uploadFileToS3 } from '@/utils/fileUtils';

const uploadedFiles = defineModel<UploadedFileResource[]>('uploadedFiles', {
  required: true,
});

defineProps<{
  accept?: string,
}>();

const fileItems = ref<FileUploadItem[]>(
  uploadedFiles.value.map((fileResource: UploadedFileResource) => ({
    name: fileResource.attributes.name,
    size: fileResource.attributes.size,
    isLoading: false,
  })),
);

function areEqual(
  a: { name: string, size: number },
  b: { name: string, size: number },
) {
  return a.name === b.name && a.size === b.size;
}

watch(fileItems, async (updatedFiles: FileUploadItem[]): Promise<void> => {
  // remove files from store that are not selected anymore
  uploadedFiles.value = uploadedFiles.value.filter((fileResource: UploadedFileResource) => (
    updatedFiles.some((fileItem: FileUploadItem) => (
      areEqual(fileResource.attributes, fileItem)
    ))
  ));

  // get all files that are not yet uploaded
  const notYetUploaded = updatedFiles.filter((fileItem: FileUploadItem) => (
    fileItem.file !== undefined
    && !uploadedFiles.value.some((fileResource: UploadedFileResource) => (
      areEqual(fileResource.attributes, fileItem)
    ))
  ));

  // upload not yet uploaded files
  await Promise.all(notYetUploaded.map(async (newFileItem) => {
    const fileItem = fileItems.value.find((item: FileUploadItem) => (
      areEqual(newFileItem, item)
    ));

    if (fileItem === undefined) {
      return;
    }

    const file = newFileItem.file as File;

    fileItem.isLoading = true;
    const tempPath = await uploadFileToS3(file);
    fileItem.isLoading = false;

    uploadedFiles.value = [
      ...uploadedFiles.value,
      toUploadedFileResource(file, tempPath),
    ];
  }));
});
</script>
