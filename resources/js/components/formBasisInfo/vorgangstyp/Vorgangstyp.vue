<template>
  <DsFormGroup
    label="Vorgangstyp"
    class="grow"
    validation-name="relationships.vorgangTyp.data.id"
    data-test="basis-info__vorgangstyp__select"
    required
  >
    <DsSelect
      v-model="model"
      :data="vorgangstypen"
      :search-keys="['attributes.titel']"
      :item-height="28"
      :is-loading="vorgangstypenStore.isLoading"
      value-key="id"
      virtualized
      required
    >
      <template #entry="{entry}">
        <div
          class="truncate"
          :title="entry.attributes?.titel"
        >
          {{ entry.attributes?.titel }}
        </div>
      </template>
    </DsSelect>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsSelect } from '@demvsystems/design-components';
import { computed } from 'vue';

import { Versandart } from '@/pages/vorlagen/types';
import { useVorgangstypenStore } from '@/stores/vorgangstypenStore';

const props = defineProps<{
  versandart: Versandart,
}>();

const model = defineModel<string | null>({ required: true });

const vorgangstypenStore = useVorgangstypenStore();

const vorgangstypen = computed(
  () => props.versandart === Versandart.Mail
    ? vorgangstypenStore.mailVorgangstypen
    : vorgangstypenStore.briefVorgangstypen,
);
</script>
