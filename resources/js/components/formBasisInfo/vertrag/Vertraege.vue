<template>
  <DsFormGroup label="Verträge">
    <DsMultiselect
      v-model="selectedVertraegeProxy"
      :options="groupedVertragList"
      :disabled="isDisabled"
      :title="vertraegeSelectTitle"
      :group-key-order="['Aktiv']"
      :is-loading="isLoading"
      group-key="group"
      data-test="basis-info__vertraege__select"
      object-as-value
    >
      <template #option="{option, isSelected}">
        <VertragOption
          :vertrag="findVertragForOption(option)"
          :gesellschaft="findGesellschaftForOption(option)"
          :sparte="findSparteForOption(option)"
          :is-selected="isSelected"
          class="-ml-2"
        />
      </template>
    </DsMultiselect>
  </DsFormGroup>
</template>

<script setup lang="ts">
import {
  DsMultiselect,
  DsFormGroup,
  MultiselectItem,
} from '@demvsystems/design-components';
import { orderBy } from 'lodash-es';
import { computed } from 'vue';

import { gesellschaften } from '@/components/formBasisInfo/gesellschaft/gesellschaftenList';
import { sparten } from '@/components/formBasisInfo/sparte/spartenList';
import {
  GesellschaftResource,
  SparteResource,
  VertragResource,
} from '@/store/resources/types';
import { useExternalList } from '@/utils/useExternalList';

import VertragOption from './VertragOption.vue';

const props = defineProps<{
  kundeId?: string | null,
  disabled?: boolean,
}>();

const model = defineModel<VertragResource[]>({ required: true });

const vertraegeUrl = computed(() => (
  props.kundeId
    ? `kunden/${props.kundeId}/vertraege`
    : undefined
));

const {
  list: vertragList,
  isLoading,
} = useExternalList<VertragResource>(vertraegeUrl);

const vertraegeToItems = (arr: VertragResource[]) => (
  arr.map((vertrag: VertragResource) => ({
    value: vertrag.id,
    label: vertrag.attributes.vertragsnummer,
    group: vertrag.attributes.isSchaden ? 'Schaden' : vertrag.attributes.status,
    sortBy: sparten.value.find(
      (e) => e.id === vertrag.relationships?.sparte?.data?.id,
    )?.attributes.name,
  }))
);

// group by status and filtered by gesellschaft
const groupedVertragList = computed<MultiselectItem[]>(() => {
  return orderBy(
    vertraegeToItems(vertragList.value),
    ['sortBy'], ['asc'],
  );
});

const isKundeSelected = computed(() => {
  return !!props.kundeId;
});

const vertraegeSelectTitle = computed<string | undefined>(() => {
  if (!props.kundeId) {
    return 'Kein Kunde ausgewählt';
  }

  if (groupedVertragList.value.length === 0) {
    return 'Keine Verträge bei diesem Kunden verfügbar';
  }

  return undefined;
});

const findVertragForOption = (
  option: MultiselectItem,
): VertragResource => {
  const vertrag = vertragList.value.find((r) => r.id === option.value);

  if (vertrag === undefined) {
    throw new Error('Vertrag must exist in vertraege');
  }

  return vertrag;
};

const findGesellschaftForOption = (
  option: MultiselectItem,
): GesellschaftResource | undefined => {
  try {
    const vertrag = findVertragForOption(option);

    return gesellschaften.value.find(
      (e) => e.id === vertrag.relationships?.gesellschaft?.data?.id,
    );
  } catch {
    return undefined;
  }
};

const findSparteForOption = (
  option: MultiselectItem,
): SparteResource | undefined => {
  try {
    const vertrag = findVertragForOption(option);

    return sparten.value.find(
      (e) => e.id === vertrag.relationships?.sparte?.data?.id,
    );
  } catch {
    return undefined;
  }
};

const selectedVertraegeProxy = computed<MultiselectItem[]>({
  get: () => vertraegeToItems(model.value),
  set: (items: MultiselectItem[]) => {
    model.value = items
      .map((item) => findVertragForOption(item))
      .filter((vertrag): vertrag is VertragResource => !!vertrag);
  },
});

const isDisabled = computed((): boolean => {
  return props.disabled || !isKundeSelected.value || vertragList.value.length === 0;
});
</script>
