<template>
  <div class="flex items-center space-x-0.5">
    <div class="w-5 shrink-0 text-gray-500">
      <!-- use v-show instead of v-if to preload fa icons -->
      <DsIcon
        v-show="isSelected"
        name="check"
      />

      <!-- fixes render problem -->
      <template v-if="!isSelected" />
    </div>

    <div>
      <div class="space-x-1 text-xs tracking-wide text-gray-700">
        <span
          class="font-bold"
          :title="gesellschaft?.attributes?.name"
          v-text="gesellschaft?.attributes?.abkuerzung"
        />
        <span
          :title="sparte?.attributes?.name"
          v-text="sparte?.attributes?.displayName ?? ''"
        />
      </div>

      <div class="flex items-center space-x-1.5 text-gray-800">
        <div
          class="font-semibold"
          v-text="vertrag.attributes?.vertragsnummer"
        />

        <div
          v-if="vertrag.attributes?.kfzKennzeichen !== null && vertrag.attributes?.kfzKennzeichen !== ''"
          class="flex h-5 items-center overflow-hidden rounded-sm border border-gray-700 bg-white"
        >
          <div
            class="bg-blue-500 px-0.5 font-bold text-white"
            style="font-size: .5rem;"
          >
            D
          </div>
          <div
            class="px-1 text-xs font-semibold"
            v-text="vertrag.attributes.kfzKennzeichen.replaceAll('-', ' ')"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsIcon } from '@demvsystems/design-components';

import {
  GesellschaftResource,
  SparteResource,
  VertragResource,
} from '@/store/resources/types';

defineProps<{
  vertrag: VertragResource,
  gesellschaft?: GesellschaftResource,
  sparte?: SparteResource,
  isSelected: boolean,
}>();
</script>
