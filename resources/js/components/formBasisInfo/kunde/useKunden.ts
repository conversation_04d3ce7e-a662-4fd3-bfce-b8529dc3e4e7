import { merge } from 'lodash-es';
import { ref, Ref, watch } from 'vue';

import { get, JsonApiUrl } from '@/api';
import { KundeResource } from '@/store/resources/types';
import { useExternalList } from '@/utils/useExternalList';

const DEFAULT_OPTIONS: JsonApiUrl<KundeResource> = {
  fields: {
    kunden: ['name', 'title', 'email', 'informal', 'salutationType', 'externalId', 'userExternalId'],
  },
  sort: [{
    name: 'lastName',
  }],
};

const options = ref<JsonApiUrl<KundeResource>>(DEFAULT_OPTIONS);

const url = ref('kunden');

const {
  list: kunden,
  isLoading,
  update,
} = useExternalList<KundeResource>(url, options, { updateManually: true });

export const useKunden = (
  passedOptions?: JsonApiUrl<KundeResource> | Ref<JsonApiUrl<KundeResource>>,
): {
    kunden: Ref<KundeResource[]>,
    isLoading: Ref<boolean>,
    getKunde: (id: string) => Promise<KundeResource | null>,
    updateKunden: () => Promise<void>,
    url: Ref<string>,
  } => {
  if (passedOptions !== undefined) {
    const passedOptionsRef = ref(passedOptions);
    watch(passedOptionsRef, (newPassedOptions) => {
      if (newPassedOptions === undefined) {
        options.value = DEFAULT_OPTIONS;
      }

      options.value = merge(DEFAULT_OPTIONS, newPassedOptions);
    }, { immediate: true });
  }

  const getKunde = async (id: string): Promise<KundeResource | null> => {
    try {
      const response = await get<KundeResource>(`kunden/${id}`);

      return response.data.data ?? null;
    } catch {
      return null;
    }
  };

  return {
    kunden,
    isLoading,

    getKunde,
    updateKunden: update,
    url,
  };
};
