<template>
  <DsSelect
    v-model="model"
    :data="kunden"
    :search-keys="['attributes.name', 'attributes.email']"
    :item-height="28"
    :on-search="onSearch"
    :min-search-length="2"
    :object-as-value="kundeAsValue ?? false"
    :is-loading="isLoading ?? false"
    :disabled="disabled ?? false"
    :placeholder="placeholder"
    :value-key="valueKey"
    debounce-search
    virtualized
    data-test="basis-info__kunde__select"
  >
    <template #entry="{entry}">
      <div
        class="truncate"
        :title="entry.attributes?.name"
      >
        {{ entry.attributes?.name }}
      </div>
    </template>
  </DsSelect>
</template>

<script setup lang="ts">
import { DsSelect } from '@demvsystems/design-components';
import { nextTick, onMounted, ref } from 'vue';

import { JsonApiUrl } from '@/api';
import { useKunden } from '@/components/formBasisInfo/kunde/useKunden';
import { KundeResource } from '@/store/resources/types';

const options = ref<JsonApiUrl<KundeResource>>({
  fields: {
    kunden: [
      'name',
      'firstName',
      'lastName',
      'title',
      'email',
      'informal',
      'salutationType',
      'externalId',
    ],
  },
});

const {
  kunden,
  updateKunden,
  getKunde,
  url,
} = useKunden(options);

const props = withDefaults(defineProps<{
  placeholder?: string,
  // use KundeResource as v-model
  kundeAsValue?: boolean,
  isLoading?: boolean,
  disabled?: boolean,
  overrideUrl?: string,
  valueKey?: string,
}>(), {
  placeholder: 'Keine Auswahl',
  overrideUrl: undefined,
  valueKey: 'id',
});

const model = defineModel<Record<string, unknown> | string | null>({ required: true });

const getSelectedKunde = async (id :string) => {
  if (model.value === null) {
    return;
  }

  if (kunden.value.find((element) => element.id === id) !== undefined) {
    return;
  }

  const kunde = await getKunde(id);
  if (kunde !== null) {
    kunden.value.push(kunde);
  }
};

onMounted(async () => {
  if (props.overrideUrl !== undefined) {
    url.value = props.overrideUrl;
  }

  if (model.value === null) {
    return;
  }

  if (props.kundeAsValue) {
    return;
  }

  await getSelectedKunde(model.value as string);
});

const onSearch = async (query: string) => {
  options.value = {
    find: {
      key: query,
      fields: {
        kunden: ['name', 'external_id'],
      },
    },
  };

  await nextTick();

  await updateKunden();
};
</script>
