<template>
  <DsFormGroup
    label="Kommunikation über"
    :required="required"
  >
    <div class="flex flex-col gap-x-4 gap-y-3 sm:flex-row">
      <DsRadioGroup
        v-model="vorgangAnlegenStore.vertriebswegArt"
        variant="button"
        class="flex"
      >
        <DsRadioButton
          value="gesellschaft"
          data-test="basis-info__vertriebsweg-art__gesellschaft"
        >
          Gesellschaft
        </DsRadioButton>
        <DsRadioButton
          value="pool"
          data-test="basis-info__vertriebsweg-art__pool"
        >
          Pool
        </DsRadioButton>
      </DsRadioGroup>
      <Gesellschaft
        v-model="vertriebswegIdSelectProxy"
        class="grow"
        :only-gesellschaften="vorgangAnlegenStore.vertriebswegArt === 'gesellschaft'"
        :only-pools="vorgangAnlegenStore.vertriebswegArt === 'pool'"
        data-test="basis-info__vertriebsweg__select"
      />
    </div>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsRadioButton, DsRadioGroup } from '@demvsystems/design-components';
import { computed } from 'vue';

import Gesellschaft from '@/components/formBasisInfo/gesellschaft/Gesellschaft.vue';
import {
  useVorgangAnlegenStore,
} from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/vorgangAnlegenStore';

const vorgangAnlegenStore = useVorgangAnlegenStore();
const required = computed(() => vorgangAnlegenStore.gesellschaftId !== null);
const vertriebswegIdSelectProxy = computed({
  get: () => vorgangAnlegenStore.vertriebswegArt === 'pool'
    ? vorgangAnlegenStore.vertriebswegPoolId
    : vorgangAnlegenStore.vertriebswegGesellschaftId,

  set: (newId) => {
    if (vorgangAnlegenStore.vertriebswegArt === 'pool') {
      vorgangAnlegenStore.vertriebswegPoolId = newId;
    } else {
      vorgangAnlegenStore.vertriebswegGesellschaftId = newId;
    }
  },
});
</script>
