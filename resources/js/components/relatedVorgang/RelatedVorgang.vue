<template>
  <DsAlert
    v-if="relatedVorgang && relatedVorgang.attributes"
    :icon="icon"
    class="mb-5"
    type="info"
  >
    <span class="font-semibold text-blue-800">
      {{ introText }}

      <router-link
        class="hover:underline"
        data-test="related_vorgang__router-link"
        :to="{
          name: 'vorgaenge.show',
          params: {
            firmaId,
            vorgangsnummer: relatedVorgang.attributes.vorgangsnummer,
          },
        }"
      >
        {{ relatedVorgang.attributes.titel }}
        ({{ relatedVorgang.attributes.vorgangsnummer }})
      </router-link>
    </span>
  </DsAlert>
</template>

<script setup lang="ts">
import { DsAlert } from '@demvsystems/design-components';
import { computed } from 'vue';

import { BasicVorgangResource } from '@/store/resources/types';

import useCurrentUser from '../users/useCurrentUser';

defineProps<{
  relatedVorgang: BasicVorgangResource,
  introText: string,
  icon: string,
}>();

const { user } = useCurrentUser();
const firmaId = computed(() => user.value?.attributes.firmaId);
</script>
