import { ref, Ref, onMounted } from 'vue';

import { get } from '@/api';
import { eventBus } from '@/store/resources/store';
import { AuthUserResource, FirmaRessource } from '@/store/resources/types';

const user = ref<AuthUserResource | undefined>(undefined);
const firma = ref<FirmaRessource | undefined>(undefined);
const hadError = ref(false);
const isLoading = ref(false);

export default (): {
  isLoading: Ref<boolean>,
  user: Ref<AuthUserResource | undefined>,
  firma: Ref<FirmaRessource | undefined>,
  loadUser: () => Promise<void>,
} => {
  const loadUser = async (force = false) => {
    if (hadError.value || isLoading.value || (!force && user.value !== undefined)) {
      return;
    }

    try {
      isLoading.value = true;
      const response = await get<AuthUserResource>(
        '/users/me',
        {
          include: ['firma'],
          fields: {
            firmen: ['kuerzel'],
          },
        },
      );
      user.value = response.data.data as AuthUserResource;
      firma.value = response.data.included?.find((elem) => elem.type === 'firmen') as FirmaRessource;
    } catch (e) {
      hadError.value = true;

      const msg = 'Nutzer konnte nicht geladen werden.';
      eventBus.emit('error', msg);

      throw Error(msg);
    } finally {
      isLoading.value = false;
    }
  };

  onMounted(() => void loadUser());

  return {
    isLoading,
    firma,
    user,
    loadUser,
  };
};
