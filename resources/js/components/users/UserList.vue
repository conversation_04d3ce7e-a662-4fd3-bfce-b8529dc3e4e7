<template>
  <div class="flex flex-col space-y-2">
    <div
      v-for="user in users"
      :key="user.id"
      class="flex items-center"
    >
      <UserTag :user="user" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';

import { UserResource } from '@/store/resources/types';

import UserTag from '../tags/UserTag.vue';

defineProps<{
  users: UserResource[];
}>();
</script>
