import { orderBy, uniqBy } from 'lodash-es';
import { computed, ComputedRef, Ref, ref, UnwrapRef } from 'vue';

import { get } from '@/api';
import { UserResource } from '@/store/resources/types';

const underlings = ref<UserResource[]>([]);
const parents = ref<UserResource[]>([]);
const visibleFirmenMembers = ref<UserResource[]>([]);

const isLoadingUnderlingsAndParents = ref(false);
const isLoadingVisibleFirmenMembers = ref(false);

const orderUsersByName = (users: UserResource[]) => (
  orderBy(users, (user) => user.attributes.name)
);

const underlingsAndParents = computed<UserResource[]>(() => {
  const merged = uniqBy(
    [...underlings.value, ...parents.value],
    (user) => user.id,
  );

  return orderUsersByName(merged);
});

const loadUnderlingsAndParents = async (force = false) => {
  if (!force && (isLoadingUnderlingsAndParents.value || underlingsAndParents.value.length > 0)) {
    return;
  }

  try {
    isLoadingUnderlingsAndParents.value = true;

    const { data: responseUnderlings } = await get<UserResource[]>('underlings');
    underlings.value = orderUsersByName(responseUnderlings.data ?? []);

    const { data: responseParents } = await get<UserResource[]>('parents');
    parents.value = orderUsersByName(responseParents.data ?? []);
  } catch (e) {
    console.log(e);
  } finally {
    isLoadingUnderlingsAndParents.value = false;
  }
};

const loadVisibleFirmenMembers = async (force = false) => {
  if (!force && (isLoadingVisibleFirmenMembers.value || visibleFirmenMembers.value.length > 0)) {
    return;
  }

  try {
    isLoadingVisibleFirmenMembers.value = true;

    const { data } = await get<UserResource[]>('firmenMembers');
    visibleFirmenMembers.value = orderUsersByName(data.data ?? []);
  } catch (e) {
    console.log(e);
  } finally {
    isLoadingVisibleFirmenMembers.value = false;
  }
};

export default (): {
  isLoading: ComputedRef<boolean>;
  loadUnderlingsAndParents: (force?: boolean) => Promise<void>;
  underlingsAndParents: ComputedRef<UserResource[]>;
  underlings: Ref<UnwrapRef<UserResource[]>>;
  visibleFirmenMembers: Ref<UnwrapRef<UserResource[]>>;
  loadVisibleFirmenMembers: (force?: boolean) => Promise<void>;
  parents: Ref<UnwrapRef<UserResource[]>>
} => ({
  loadUnderlingsAndParents,
  loadVisibleFirmenMembers,
  isLoading: computed(() => (
    isLoadingUnderlingsAndParents.value || isLoadingVisibleFirmenMembers.value
  )),
  underlings,
  parents,
  visibleFirmenMembers,
  underlingsAndParents,
});
