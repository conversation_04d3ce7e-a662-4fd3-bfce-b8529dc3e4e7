<template>
  <div
    v-if="versandart === 'brief' && previewPdf !== ''"
    class="-mx-4 -mt-4 overflow-auto p-6 sm:-mx-6 sm:-mt-6"
  >
    <PdfReader
      data-test="details-view__pdf-reader"
      :pdf-document="previewPdf"
      :pdf-url="pdfUrl"
      :suppress-errors-and-retry="suppressErrorsAndRetry"
      :page-scale="1.15"
      class="-m-6"
    />
  </div>
  <div
    v-else
    class="space-y-8 bg-white"
  >
    <div class="space-y-1.5">
      <KorrespondenzDetailsEmailRow
        v-if="absender.length > 0"
        data-test="mail__details-preview__absender"
        :empfaengers="absender"
        title="Absender"
      />
      <KorrespondenzDetailsEmailRow
        v-if="empfaenger.length > 0"
        data-test="mail__details-preview__empfaenger"
        :empfaengers="empfaenger"
        title="An"
      />
      <KorrespondenzDetailsEmailRow
        v-if="cc !== null && cc.length > 0"
        data-test="mail__details-preview__cc"
        :empfaengers="cc"
        title="CC"
      />
      <KorrespondenzDetailsEmailRow
        v-if="bcc !== null && bcc.length > 0"
        data-test="mail__details-preview__bcc"
        :empfaengers="bcc"
        title="BCC"
      />
      <div
        v-if="isVersendet"
        class="flex items-center"
      >
        <div class="w-24 shrink-0 text-sm font-medium text-gray-700">
          Versandt
        </div>

        <TimeAgoText
          v-if="versendetAt"
          :value="versendetAt"
          capitalize
        />
      </div>
      <div class="flex items-center">
        <div
          class="w-24 shrink-0 text-sm font-medium text-gray-700"
        >
          Betreff
        </div>
        <div
          class="overflow-hidden break-words font-bold"
          data-test="mail__details-preview__subject"
          v-html="subject"
        />
      </div>
    </div>
    <div
      class="html-preview"
      data-test="mail__details-preview__content"
      v-html="content"
    />
    <slot />
  </div>
</template>

<script setup lang="ts">
import PdfReader from '@/components/pdfReader/PdfReader.vue';
import TimeAgoText from '@/components/tags/TimeAgoText.vue';
import KorrespondenzDetailsEmailRow from '@/pages/korrespondenzDetails/components/KorrespondenzDetailsEmailRow.vue';
import { Versandart } from '@/pages/vorlagen/types';
import { EmailElement } from '@/store/resources/types';

defineProps<{
  absender: EmailElement[],
  empfaenger: EmailElement[],
  cc: EmailElement[] | null,
  bcc: EmailElement[] | null,
  subject?: string,
  versandart: Versandart,
  isVersendet? : boolean,
  versendetAt?: string,
  previewPdf?: string,
  pdfUrl?: string,
  content?: string,
  suppressErrorsAndRetry?: boolean,
}>();
</script>
