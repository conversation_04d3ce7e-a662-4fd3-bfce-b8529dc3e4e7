<template>
  <div class="space-y-3">
    <FormSection title="Empfänger">
      <div class="flex flex-col gap-x-4 gap-y-3 sm:flex-row">
        <DsFormGroup
          :validation-name="validationNames?.empfaenger ?? ''"
          class="grow"
          label="An"
          required
          data-test="empfaenger__select"
        >
          <slot name="empfaenger" />
        </DsFormGroup>

        <div class="self-center sm:pt-6">
          <DsSwitch
            :model-value="hasCcAndBcc"
            data-test="empfaenger__cc-bcc__switch"
            :disabled="disabled"
            @update:model-value="emit('update:hasCcAndBcc', $event)"
          >
            CC/BCC
          </DsSwitch>
        </div>
      </div>

      <!-- CC & BCC -->
      <div
        v-if="hasCcAndBcc"
        class="flex flex-col gap-x-4 gap-y-3 md:flex-row"
      >
        <DsFormGroup
          :validation-name="validationNames?.cc ?? ''"
          class="md:w-1/2"
          label="CC"
          data-test="empfaenger__cc"
        >
          <slot name="cc" />
        </DsFormGroup>
        <DsFormGroup
          :validation-name="validationNames?.bcc ?? ''"
          class="md:w-1/2"
          label="BCC"
          data-test="empfaenger__bcc"
        >
          <slot name="bcc" />
        </DsFormGroup>
      </div>
    </FormSection>
  </div>
</template>

<script setup lang="ts">
// styles and toggle logic. DsSelect is meant to be inside the slots.
import { DsFormGroup, DsSwitch } from '@demvsystems/design-components';

import FormSection from './FormSection.vue';

defineProps<{
  hasCcAndBcc: boolean
  validationNames?: {
    empfaenger?: string
    cc?: string
    bcc?: string
  }
  disabled?: boolean,
}>();

const emit = defineEmits<{
  (event: 'update:hasCcAndBcc', hasCcAndBcc: boolean): void
}>();
</script>
