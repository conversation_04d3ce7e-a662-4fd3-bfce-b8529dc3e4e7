<template>
  <DsMultiselect
    v-model="model"
    :validation="isEmail"
    :options="options"
    :is-loading="isLoading"
    :search-keys="['label', 'name', 'subtitle']"
    :group-key-order="orderedGroups"
    group-key="group"
    new-option-group="Andere E-Mail-Adresse"
    allow-new-options-text="Tippen <PERSON>, um zu suchen oder eine E-Mail-Adresse hinzuzufügen"
    object-as-value
    allow-new-options
  >
    <template #option="{option, isSelected, isDraft, isInvalid}">
      <div class="flex">
        <div class="w-5 shrink-0 text-gray-500">
          <DsIcon
            v-if="isDraft"
            name="plus"
          />
          <DsIcon
            v-else-if="isSelected"
            name="check"
          />
        </div>

        <p class="space-x-1 font-semibold text-gray-800">
          {{ option?.name ? `${option.name} <${option.label}>` : option.label }}
        </p>
      </div>

      <div class="ml-5">
        <p
          v-if="isDraft && isInvalid"
          class="text-gray-700"
        >
          Keine valide E-Mail-Adresse.
        </p>
        <p
          v-else-if="option?.subtitle"
          class="text-xs tracking-wide text-gray-700"
        >
          {{ option?.subtitle }}
        </p>
      </div>
    </template>
  </DsMultiselect>
</template>

<script setup lang="ts">
import { DsMultiselect, MultiselectItem, DsIcon } from '@demvsystems/design-components';
import { computed, readonly, toRef } from 'vue';

import {
  EmpfaengerSuggestion,
  EmpfaengerSuggestionContext,
  EmpfaengerSuggestionContextType,
  useEmpfaengerSuggestions,
} from '@/components/form/useEmpfaengerSuggestions';
import { isEmail } from '@/components/form/utils/mail';

type SuggestionMultiselectItem = MultiselectItem & {
  name?: string;
  subtitle?: string;
};

const model = defineModel<MultiselectItem[]>();

const {
  suggestionContext = {},
} = defineProps<{
  suggestionContext?: EmpfaengerSuggestionContext,
}>();

const {
  suggestions,
  isLoading,
} = useEmpfaengerSuggestions(toRef(() => suggestionContext));

// these are ordered by the order of the groups in the multiselect
const CONTEXT_GROUP_MAP = readonly<Record<EmpfaengerSuggestionContextType, string>>({
  vorgang: 'Vorgang',
  kunde: 'Kunde',
  vertriebsweg: 'Vertriebsweg',
  gesellschaft: 'Gesellschaft',
  user: 'Kollegen',
});

const orderedGroups = [
  'Andere E-Mail-Adresse',
  ...Object.values(CONTEXT_GROUP_MAP),
];

const options = computed<SuggestionMultiselectItem[]>(() => (
  suggestions.value.map((suggestion: EmpfaengerSuggestion) => ({
    label: suggestion.email,
    value: suggestion.email,
    name: suggestion.name,
    subtitle: suggestion.label,
    group: CONTEXT_GROUP_MAP[suggestion.context],
  }))
));
</script>
