<template>
  <div class="flex w-full flex-col gap-x-4 gap-y-3 xl:flex-row">
    <FileUpload
      :class="{
        'w-full xl:w-1/2': existingDocuments !== undefined,
        'w-full': existingDocuments === undefined,
      }"
      :accept="acceptUploadedFiles"
      :uploaded-files="uploadedFiles"
      @update:uploaded-files="emit('update:uploadedFiles', $event)"
    />
    <DsFormGroup
      v-if="existingDocuments && vertraegeIds"
      class="w-full xl:w-1/2"
      data-test="form-files__bestehende-dokumente"
      validation-name="files.size"
    >
      <div class="flex items-center">
        <span>Bestehende Dokumente</span>
        <ElPopover
          v-if="showTooltip"
          placement="top"
          :width="300"
          trigger="hover"
        >
          <template #reference>
            <DsIcon
              name="info-circle"
              class="ml-1 cursor-help text-gray-500"
            />
          </template>
          <template #default>
            <div class="p-2 text-sm">
              <PERSON>ämtliche Dokumente, welche Sie in der ursprünglichen Nachricht angehangen haben,
              werden in der Erinnerung/ Mahnung erneut automatisch angehangen.
              Sollten noch weitere Dokumente aus dem Vorgang fehlen,
              rufen Sie bitte die Funktion 'Bestehende Dokumente' auf
              und wechseln Sie auf den Tab 'Aktueller Vorgang'
            </div>
          </template>
        </ElPopover>
      </div>
      <SelectExistingDocumentsModal
        :model-value="existingDocuments"
        :kunde-id="kundeId"
        :gesellschaft-id="gesellschaftId"
        :vertraege-ids="vertraegeIds"
        :vorgang-id="vorgangId"
        :kundendokument-ids="kundendokumentIds ?? []"
        :add-docs-from-first-element="addDocsFromFirstElement"
        :vorlage-attachments="vorlageAttachments ?? []"
        :sparte-id="sparteId"
        :vorgangstyp-id="vorgangstypId"
        @remove-existing-document="
          removeFileFromExistingDocuments !== undefined
            ? removeFileFromExistingDocuments($event)
            : undefined
        "
        @update:model-value="emit('update:existingDocuments', $event)"
      />
    </DsFormGroup>
  </div>

  <FileSizeAlerts
    v-if="showTotalFileSizeAlerts"
    :uploaded-files="uploadedFiles"
    :existing-documents="existingDocuments"
  />
</template>

<script setup lang="ts">
// joins DsFileUpload with SelectingExistingDocumentsModal in a single row.

import { DsFormGroup, DsIcon } from '@demvsystems/design-components';
import { ElPopover } from 'element-plus';

import { Attachment, ExternalFileResource, UploadedFileResource } from '@/store/resources/types';

import SelectExistingDocumentsModal from '../documents/SelectExistingDocumentsModal.vue';
import FileUpload from '../formBasisInfo/fileUpload/FileUpload.vue';

import FileSizeAlerts from './FileSizeAlerts.vue';

withDefaults(defineProps<{
  uploadedFiles: UploadedFileResource[]
  acceptUploadedFiles?: string,
  existingDocuments?: ExternalFileResource[],
  removeFileFromExistingDocuments?: ({ url }: { url: string }) => void,
  kundeId?: string,
  gesellschaftId?: string,
  vertraegeIds?: string[],
  vorgangId?: string,
  vorlageAttachments?: Attachment[],
  sparteId?: string,
  vorgangstypId?: string,
  kundendokumentIds?: string[],
  addDocsFromFirstElement?: boolean,
  showTotalFileSizeAlerts?: boolean,
  showTooltip?: boolean,
}>(), {
  acceptUploadedFiles: undefined,
  existingDocuments: undefined,
  removeFileFromExistingDocuments: undefined,
  kundeId: undefined,
  gesellschaftId: undefined,
  vertraegeIds: () => [],
  vorlageAttachments: () => [],
  sparteId: undefined,
  vorgangstypId: undefined,
  vorgangId: undefined,
  kundendokumentIds: () => [],
});

const emit = defineEmits<{
  (event: 'update:uploadedFiles', files: UploadedFileResource[]): void
  (event: 'update:existingDocuments', files: ExternalFileResource[]): void
}>();
</script>
