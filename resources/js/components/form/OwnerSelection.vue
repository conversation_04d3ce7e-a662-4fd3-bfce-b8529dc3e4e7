<template>
  <div class="space-y-3">
    <h3 class="text-base font-semibold leading-none">
      {{ headline }}
    </h3>

    <div class="flex space-x-3">
      <DsFormGroup
        :label="formGroupLabel"
        class="shrink-0"
      >
        <DsRadioGroup
          v-model="isOnBehalfOfProxy"
          variant="button"
        >
          <DsRadioButton
            value="false"
            label="Für mich"
            :data-test="`${dataTestPrefix}__for-me`"
          />
          <DsRadioButton
            value="true"
            label="Im Auftrag von"
            :disabled="!hasUnderlings"
            :title="hasUnderlings ? null : 'Es existieren keine <PERSON>utzer, in deren Auftrag Sie handeln können.'"
            :data-test="`${dataTestPrefix}__im-auftrag-von`"
          />
        </DsRadioGroup>
      </DsFormGroup>

      <DsFormGroup
        v-if="isOnBehalfOf"
        label="Im Auftrag von"
        class="w-full"
        required
      >
        <DsSelect
          v-model="ownerProxy"
          :data="underlingsWithoutMe"
          :search-keys="['attributes.name', 'attributes.email']"
          value-key="id"
          :data-test="`${dataTestPrefix}__im-auftrag-von__select`"
        >
          <template #entry="{entry}">
            {{ entry.attributes.name }}
          </template>
        </DsSelect>
      </DsFormGroup>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  DsFormGroup,
  DsRadioButton,
  DsRadioGroup,
  DsSelect,
} from '@demvsystems/design-components';
import { computed, onBeforeMount, ref, watch } from 'vue';

import useCurrentUser from '@/components/users/useCurrentUser';
import useHierarchy from '@/components/users/useHierarchy';

const { underlings, loadUnderlingsAndParents } = useHierarchy();
const { user: currentUser } = useCurrentUser();

onBeforeMount(async () => {
  await loadUnderlingsAndParents();
});

const props = defineProps<{
  ownerId?: string,
  headline: string,
  formGroupLabel: string,
  dataTestPrefix: string,
}>();

const emit = defineEmits<{
  (event: 'update:ownerId', ownerId: string | undefined): void
}>();

const underlingsWithoutMe = computed(() => (
  underlings.value.filter((user) => user.id !== currentUser.value?.id)
));

const hasUnderlings = computed(() => underlingsWithoutMe.value.length > 0);

const isOnBehalfOf = ref(props.ownerId !== undefined);

const isOnBehalfOfProxy = computed({
  get: () => isOnBehalfOf.value ? 'true' : 'false',
  set: (value: string) => {
    isOnBehalfOf.value = value === 'true';
  },
});

const ownerProxy = computed({
  get: () => props.ownerId,
  set: (newOwner) => {
    emit('update:ownerId', newOwner ?? undefined);
  },
});

watch(isOnBehalfOf, (newValue) => {
  if (!newValue) {
    emit('update:ownerId', undefined);
  }
});
</script>
