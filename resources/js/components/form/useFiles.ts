import { ref, computed, Ref, ComputedRef } from 'vue';

import {
  UploadedFileResource,
  isExternalFileResource,
  isUploadedFileResource,
  ExternalFileResource,
  FileResource,
} from '@/store/resources/types';
import { deleteFile, saveFile } from '@/utils/fileUtils';

type Options = {
  /**
   * In preview mode, `useFiles` will keep track of saved files to prevent uploading a file
   * multiple times. It will also actively delete files that are no longer present in subsequent
   * calls to `saveFile`. After the final `saveFile` call, `clearSavedFileIds` has to be called
   * to prevent deleting files in the next saveFile call.
   */
  preview?: boolean,
};

type ReturnValue = {
  files: Ref<(UploadedFileResource | ExternalFileResource)[]>,
  uploadedFiles: Ref<UploadedFileResource[]>,
  externalFiles: Ref<ExternalFileResource[]>,
  totalFileSize: ComputedRef<number>,
  removeFileFromExistingDocuments: ({ url }: { url: string }) => void,
  saveFiles: (ownerId: string, ownerType: string) => Promise<void>,
  clearSavedFileIds: () => void,
  deleteSavedFiles: () => void,
  setInitialUploadedFiles: (files: FileResource[]) => void,
};

export const TEN_MB_IN_B = 10_485_760;
export const FIFTEEN_MB_IN_B = 15_728_640;

// Manage saving of external and uploaded files
export function useFiles({ preview = false }: Options = {}): ReturnValue {
  // all files
  const files = ref<(UploadedFileResource | ExternalFileResource)[]>([]);

  // files the user just uploaded in the form
  const uploadedFiles = computed<UploadedFileResource[]>({
    get: () => files.value.filter(isUploadedFileResource),
    set: (newFiles: UploadedFileResource[]) => {
      // replace only UploadedFileResources
      files.value = [
        ...newFiles,
        ...files.value
          .filter((file) => !isUploadedFileResource(file)),
      ];
    },
  });

  // existing files, e.g. from kundenakte or other vorgang
  const externalFiles = computed<ExternalFileResource[]>({
    get: () => files.value.filter(isExternalFileResource),
    set: (newFiles: ExternalFileResource[]) => {
      // replace only ExternalFileResources
      files.value = [
        ...newFiles,
        ...files.value
          .filter((file) => !isExternalFileResource(file)),
      ];
    },
  });

  const totalFileSize = computed(() => (
    files.value.reduce((sum, file: UploadedFileResource | ExternalFileResource) => (
      sum + (file.attributes.size ?? 0)
    ), 0)),
  );

  // keep track of already saved files (only if options.preview = true)
  let savedFileIds: string[] = [];

  function removeFileFromExistingDocuments({ url }: { url: string }) {
    externalFiles.value = externalFiles.value.filter(
      (doc) => doc.attributes.url !== url,
    );
  }

  function deleteSavedFiles() {
    files.value.forEach((file) => {
      if (file.id === '') {
        return;
      }

      void deleteFile(file.id);
    });

    files.value = [];
  }

  function clearSavedFileIds() {
    savedFileIds = [];
  }

  /**
   * Saves `files` and sets their (new) ids. In preview mode, this will also delete files
   * that were uploaded but are no longer present in `files`.
   */
  async function saveFiles(ownerId: string, ownerType: string) {
    if (preview) {
      // delete files that were uploaded but are no longer selected
      savedFileIds = savedFileIds.filter((fileId) => {
        if (files.value.some(({ id }) => fileId === id)) {
          return true;
        }

        void deleteFile(fileId);

        return false;
      });
    }

    const filePromises = files.value.map<Promise<UploadedFileResource | ExternalFileResource>>(
      async (file: UploadedFileResource | ExternalFileResource) => {
        if (preview && file.id !== '' && savedFileIds.includes(file.id)) {
          return file; // file was already uploaded
        }

        const savedFile = await saveFile(file, ownerId, ownerType);

        if (preview) {
          savedFileIds.push(savedFile.id);
        }

        return savedFile;
      },
    );

    // set ids of saved files
    files.value = await Promise.all(filePromises);
  }

  // set initially uploaded files as saved
  function setInitialUploadedFiles(includedFiles: FileResource[]): void {
    savedFileIds = includedFiles.map((file) => file.id);
    uploadedFiles.value = includedFiles as UploadedFileResource[];
  }

  return {
    files,
    uploadedFiles,
    externalFiles,
    totalFileSize,
    removeFileFromExistingDocuments,
    setInitialUploadedFiles,
    saveFiles,
    clearSavedFileIds,
    deleteSavedFiles,
  };
}
