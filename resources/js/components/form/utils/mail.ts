import { MultiselectItem } from '@demvsystems/design-components';
import { computed, WritableComputedRef, Ref } from 'vue';

import {
  EmailElement,
  isEmailElement,
  VorlageMailEmpfaenger,
  VorlageMailEmpfaengerType,
} from '@/store/resources/types';

export function emailElementToMultiselectItem(
  empfaengerArr: EmailElement[],
): MultiselectItem[] {
  return empfaengerArr
    .filter(isEmailElement)
    .map((e: EmailElement) => ({
      value: e.email,
      label: e.email,
      name: e.name,
    }));
}

export function multiselectItemToEmailElement(
  items: Array<MultiselectItem & {
    name?: string,
  }>,
): EmailElement[] {
  return items
    .map((item) => ({
      ...(item.name && { name: item.name }),
      email: item.value as string,
    }));
}

export function isEmail(inputStr: string): boolean {
  return /^\S+@\S+\.\S+/.test(inputStr);
}

const empfaengerTypeLabels = <Record<string, string>>{
  [VorlageMailEmpfaengerType.ZustaendigerVermittler]: 'Zuständiger Vermittler',
};

export function getLabelForEmpfaengerType(type: VorlageMailEmpfaengerType): string {
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  return empfaengerTypeLabels[type] ?? Object.keys(VorlageMailEmpfaengerType)[
    Object.values(VorlageMailEmpfaengerType).indexOf(type)
  ]!;
}

export function makeVorlageMailEmpfaengerMultiselectProxy(
  empfaengers: Ref<VorlageMailEmpfaenger[]>,
): WritableComputedRef<MultiselectItem[]>  {
  return computed<MultiselectItem[]>({
    get: () => empfaengers.value.map((empfaenger) => {
      let label: VorlageMailEmpfaenger = empfaenger;

      if (
        Object.values(VorlageMailEmpfaengerType).includes(empfaenger as VorlageMailEmpfaengerType)
      ) {
        label = getLabelForEmpfaengerType(empfaenger as VorlageMailEmpfaengerType);
      }

      return {
        value: empfaenger,
        label: label,
      };
    }),
    set: (newEmpfaengers) => {
      empfaengers.value = newEmpfaengers
        .map((newEmpfaenger) => <string>newEmpfaenger.value);
    },
  });
}
