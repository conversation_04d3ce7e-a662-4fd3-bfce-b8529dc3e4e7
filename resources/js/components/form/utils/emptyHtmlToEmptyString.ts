/**
 * Converts an HTML string to an empty string if it contains no text or images
 */
export function emptyHtmlToEmptyString(value: string | null): string {
  if (value === null) {
    return '';
  }

  const parser = new DOMParser();
  const doc = parser.parseFromString(value, 'text/html');

  const hasTextContent = doc.body.textContent !== null
    && doc.body.textContent.trim().length > 0;
  const hasImage = doc.body.querySelector('img') !== null;
  const hasTag = doc.body.querySelector('span[data-type="tag"]') !== null;

  if (hasTextContent || hasImage || hasTag) {
    return value;
  }

  return '';
}
