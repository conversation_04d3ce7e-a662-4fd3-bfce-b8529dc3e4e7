import { describe, it, expect } from 'vitest';

import { emptyHtmlToEmptyString } from '../emptyHtmlToEmptyString';

describe('emptyHtmlToEmptyString', () => {
  const testCases = [
    {
      // nested html tag with text content
      content: `
        <p style="min-height: 1.5em; font-family: Arial, sans-serif; font-size: 12pt; color: #000;">
          <span style="font-family: Arial">
            test
          </span>
        </p>
      `,
      expect_empty: false,
    },

    // nested html without text content
    {
      content: `
        <p style="min-height: 1.5em; font-family: Arial, sans-serif; font-size: 12pt; color: #000;">
          <span style="font-family: Arial"></span>
        </p>
      `,
      expect_empty: true,
    },

    // nested html with image
    {
      content: `
        <p style="min-height: 1.5em; font-family: Arial, sans-serif; font-size: 12pt; color: #000;">
          <div>
            <div>
              <img src="https://via.placeholder.com/150" alt="Test Image">
            </div>
          </div>
        </p>
      `,
      expect_empty: false,
    },

    // multiple html tags with text
    {
      content: `
        <p style="min-height: 1.5em; font-family: Arial, sans-serif; font-size: 12pt; color: #000;">
          <span style="font-family: Arial"> 
            Text 
            <strong> Test 123 </strong>
          </span>
        </p>
        <p style="min-height: 1.5em; font-family: Arial, sans-serif; font-size: 12pt; color: #000;">
          <em>Italic 3</em>
        </p>
      `,
      expect_empty: false,
    },

    // multiple html tags without text
    {
      content: `
        <p style="min-height: 1.5em; font-family: Arial, sans-serif; font-size: 12pt; color: #000;">
          <span style="font-family: Arial">
            <strong></strong>
          </span>
        </p>
        <p style="min-height: 1.5em; font-family: Arial, sans-serif; font-size: 12pt; color: #000;">
          <em></em>
        </p>
      `,
      expect_empty: true,
    },

    // multiple html tags with text and without
    {
      content: `
        <p style="min-height: 1.5em; font-family: Arial, sans-serif; font-size: 12pt; color: #000;">
          <span style="font-family: Arial"> 
            <strong></strong>
          </span>
        </p>
        <p style="min-height: 1.5em; font-family: Arial, sans-serif; font-size: 12pt; color: #000;">
          <em>Italic 3</em>
        </p>
      `,
      expect_empty: false,
    },

    // null
    {
      content: null,
      expect_empty: true,
    },

    // empty string
    {
      content: '',
      expect_empty: true,
    },
  ];

  testCases.forEach(({ content, expect_empty }, index) => {
    it(`should return correct output for dataset ${index + 1}`, () => {
      const result = emptyHtmlToEmptyString(content);
      expect(result).toBe(expect_empty ? '' : content);
    });
  });
});
