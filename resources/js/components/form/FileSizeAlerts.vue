<template>
  <DsAlert
    v-if="showFileSizeWarning"
    type="warning"
    icon="files"
    label="Die Dateien in Ihrem Anhang überschreiten eine Größe von 10 MB"
  >
    Bitte beachten Sie, dass Ihre E-Mail aufgrund der Größe der Anhänge
    beim Empfänger gar nicht oder nur unvollständig ankommen kann.
  </DsAlert>
  <DsAlert
    v-else-if="showFileSizeError"
    type="error"
    icon="files"
    label="Die Dateien in Ihrem Anhang überschreiten eine Größe von 15 MB"
  >
    Bitte beachten Sie, dass Ihre E-Mail aufgrund der Größe der Anhänge sehr wahrscheinlich
    nicht beim Empfänger ankommen wird und deshalb nicht verschickt werden kann.
  </DsAlert>
</template>

<script lang="ts" setup>
import { DsAlert } from '@demvsystems/design-components';
import { computed } from 'vue';

import { ExternalFileResource, UploadedFileResource } from '@/store/resources/types';

import { FIFTEEN_MB_IN_B, TEN_MB_IN_B } from './useFiles';

const props = defineProps<{
  uploadedFiles: UploadedFileResource[],
  existingDocuments?: ExternalFileResource[],
}>();

const totalFileSize = computed(() => [
  ...props.uploadedFiles,
  ...(props.existingDocuments ?? []),
].reduce((sum, file: UploadedFileResource | ExternalFileResource) => (
  sum + (file.attributes.size ?? 0)
), 0));

const showFileSizeError = computed(() => (
  totalFileSize.value > FIFTEEN_MB_IN_B
));

const showFileSizeWarning = computed(() => (
  !showFileSizeError.value
    && totalFileSize.value > TEN_MB_IN_B
));
</script>
