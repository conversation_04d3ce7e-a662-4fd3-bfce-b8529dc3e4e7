<template>
  <div class="space-y-3">
    <h3 class="text-base font-semibold leading-none">
      Vorgang
    </h3>
    <DsSwitch
      v-model="hasFaelligAtProxy"
      data-test="aktionen__faellig-at__switch"
    >
      Fälligkeitsdatum aktualisieren
    </DsSwitch>

    <FaelligAt
      v-if="hasFaelligAt"
      v-model="faelligAtProxy"
      data-test-prefix="aktionen"
    />
  </div>
</template>

<script setup lang="ts">
import { DsSwitch } from '@demvsystems/design-components';
import { computed } from 'vue';

import FaelligAt from '@/components/formBasisInfo/faelligAt/FaelligAt.vue';

const emit = defineEmits<{
  (event: 'update:faelligAt', faelligAt: Date | undefined): void,
  (event: 'update:hasFaelligAt', hasFaelligAt: boolean): void,
}>();

const props = defineProps<{
  faelligAt?: Date,
  hasFaelligAt: boolean,
}>();

const faelligAtProxy = computed({
  get: () => props.faelligAt,
  set: (newFaelligAt) => {
    emit('update:faelligAt', newFaelligAt);
  },
});

const hasFaelligAtProxy = computed({
  get: () => props.hasFaelligAt,
  set: (newHasFaelligAt) => {
    emit('update:hasFaelligAt', newHasFaelligAt);
  },
});
</script>
