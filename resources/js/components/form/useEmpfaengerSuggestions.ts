import axios from 'axios';
import { computed, ComputedRef, ref, Ref, watch } from 'vue';

export type EmpfaengerSuggestionContext = {
  kundeId?: string | null;
  gesellschaftId?: string | null;
  sparteId?: string | null;
  vorgangstypId?: string | null;
  vorgangId?: string | null;
  vertriebswegId?: string | null;
};

export type EmpfaengerSuggestionContextType = 'user' | 'kunde' | 'gesellschaft' | 'vorgang' | 'vertriebsweg';

export type EmpfaengerSuggestion = {
  email: string;
  name: string,
  context: EmpfaengerSuggestionContextType,
  label: string,
};

const currentlyLoadingContextKey = ref<string>();
const isLoading = computed(() => currentlyLoadingContextKey.value !== undefined);

const contextSuggestionMap = ref<Record<string, EmpfaengerSuggestion[]>>({});

function getContextKey(context: EmpfaengerSuggestionContext): string {
  return JSON.stringify(context);
}

async function loadSuggestionsFromServer(context: EmpfaengerSuggestionContext) {
  const contextKey = getContextKey(context);

  if (currentlyLoadingContextKey.value === contextKey) {
    return;
  }

  let newSuggestions = [];

  try {
    currentlyLoadingContextKey.value = contextKey;
    newSuggestions = ((await axios.get('/api/emailSuggestions', {
      params: context,
    })).data ?? []) as EmpfaengerSuggestion[];

    if (newSuggestions.length > 0) {
      contextSuggestionMap.value[contextKey] = newSuggestions;
    }
  } finally {
    currentlyLoadingContextKey.value = undefined;
  }
}

export function useEmpfaengerSuggestions(context: Ref<EmpfaengerSuggestionContext>): {
  suggestions: ComputedRef<EmpfaengerSuggestion[]>,
  isLoading: Ref<boolean>,
} {
  watch(context, async (newContext) => {
    if (!(getContextKey(newContext) in contextSuggestionMap.value)) {
      await loadSuggestionsFromServer(newContext);
    }
  }, { immediate: true, deep: true });

  const suggestions = computed(() => {
    return contextSuggestionMap.value[getContextKey(context.value)] ?? [];
  });

  return {
    suggestions,
    isLoading,
  };
}
