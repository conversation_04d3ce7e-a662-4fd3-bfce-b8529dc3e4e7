import {
  defineAsyncComponent as defineAs<PERSON>VueComponent,
  AsyncComponentLoader,
  Component,
  ComponentPublicInstance,
} from 'vue';

import ComponentLoader from './ComponentLoader.vue';

export const defineAsyncComponent = <T extends Component = {
  new (): ComponentPublicInstance;
}>(source: AsyncComponentLoader<T>): T => (
  defineAsyncVueComponent({
    loader: source,
    loadingComponent: ComponentLoader,
    delay: 100,
  })
);
