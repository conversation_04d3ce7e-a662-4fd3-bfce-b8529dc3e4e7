<template>
  <div class="space-y-5">
    <h3 class="text-base font-bold leading-none tracking-wide">
      Bestehende Dokumente anhängen
      <span v-if="visibleTabs.length === 1">
        ({{ visibleTabs[0]?.title }})
      </span>
    </h3>

    <component
      :is="visibleTabs[0]?.component"
      v-if="visibleTabs.length === 1"
    />

    <DsTabs
      v-else-if="visibleTabs.length > 1"
      :active-tab="activeTab"
    >
      <DsTab
        v-for="tab in visibleTabs"
        :key="tab.title"
        :title="tab.title"
        :badge="getSelectedDocsCountFor(tab.origin)"
        always-render
      >
        <component :is="tab.component" />
      </DsTab>
    </DsTabs>

    <DsAlert
      v-else
      type="warning"
    >
      Keine Dokumente verfügbar.
    </DsAlert>

    <div class="flex justify-end space-x-3">
      <DsButton
        variant="secondary"
        @click="$emit('close')"
      >
        Abbrechen
      </DsButton>
      <DsButton
        :disabled="isLoading || selectedDocs.length === 0"
        @click="selectedDocs.length > 0 && updateModelValueAndClose()"
      >
        {{ submitLabel }}
      </DsButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsAlert, DsButton, DsTab, DsTabs } from '@demvsystems/design-components';
import { computed, ref } from 'vue';

import { ExternalFileOrigin, ExternalFileResource } from '@/store/resources/types';

import useTabAktuellerVorgang from './tabContent/aktuellerVorgang/useTabAktuellerVorgang';
import useTabKundenakte from './tabContent/kundenakte/useTabKundenakte';
import { DocumentsTabMeta } from './types';

const props = defineProps<{
  modelValue: ExternalFileResource[],
  kundeId?: string,
  gesellschaftId?: string,
  vertraegeIds: string[],
  vorgangId?: string,
}>();

const emit = defineEmits<{
  (event: 'close'): void,
  (event: 'update:modelValue', modelValue: ExternalFileResource[]): void,
}>();

const activeTab = ref(0);

const {
  createTab: createTabKundenakte,
  selectedDocs: selectedDocsKundenakte,
  isLoading: isLoadingKundenakte,
} = useTabKundenakte();
const {
  createTab: createTabAktuellerVorgang,
  selectedDocs: selectedDocsAktuellerVorgang,
} = useTabAktuellerVorgang();

const tabs = ref<DocumentsTabMeta[]>([
  createTabKundenakte(
    props.modelValue,
    props.kundeId,
    props.gesellschaftId,
    props.vertraegeIds,
  ),
  await createTabAktuellerVorgang(
    props.modelValue,
    props.vorgangId,
  ),
]);

const selectedDocs = computed(() => [
  ...selectedDocsKundenakte.value,
  ...selectedDocsAktuellerVorgang.value,
]);

const isLoading = computed(
  () => isLoadingKundenakte.value,
);

const submitLabel = computed(
  () => {
    if (isLoading.value) {
      return '... Dokumente anhängen';
    }

    return selectedDocs.value.length.toString() + ' Dokument'
      + (selectedDocs.value.length !== 1 ? 'e' : '') + ' anhängen';
  },
);

const getSelectedDocsCountFor = (origin: ExternalFileOrigin): number => (
  selectedDocs.value
    .filter((doc) => doc.attributes.origin === origin)
    .length
);

const visibleTabs = computed(() => tabs.value.filter((tab) => tab.isVisible));

const updateModelValueAndClose = () => {
  emit('update:modelValue', selectedDocs.value);
  emit('close');
};
</script>
