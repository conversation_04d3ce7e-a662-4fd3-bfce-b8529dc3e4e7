<template>
  <div
    class="flex items-center space-x-3 px-3 py-2 text-sm"
  >
    <DsCheckbox
      v-model="model"
    />

    <div class="grow truncate">
      <div
        v-if="!!typeName"
        :title="typeName"
        class="text-xs font-medium text-gray-700"
        v-text="typeName"
      />

      <div
        :title="fileName ?? file?.attributes.name"
        v-text="fileName ?? file?.attributes.name"
      />

      <div
        v-if="!!subTitle"
        :title="subTitle"
        class="truncate text-xs text-gray-500"
        v-text="subTitle"
      />
    </div>

    <span class="shrink-0 space-x-1.5">
      <FileListViewButton
        v-if="file && isFileViewable(file.attributes.mimetype)"
        :file="file"
      />
      <DsButton
        v-else-if="!!viewUrl"
        :href="viewUrl"
        size="sm"
        icon="eye"
        variant="secondary"
        external
      />
      <FileListDownloadButton
        v-if="!!downloadUrl"
        :href="downloadUrl"
        title="Dokument herunterladen"
      />
    </span>
  </div>
</template>

<script setup lang="ts">
import { DsButton, DsCheckbox } from '@demvsystems/design-components';

import FileListDownloadButton from '@/components/fileList/FileListDownloadButton.vue';
import FileListViewButton from '@/components/fileList/FileListViewButton.vue';
import isFileViewable from '@/components/fileList/isFileViewable';
import { FileResource } from '@/store/resources/types';

defineProps<{
  file?: FileResource,
  typeName?: string,
  fileName?: string,
  subTitle?: string,
  viewUrl?: string,
  downloadUrl?: string,
}>();

const model = defineModel<boolean>({ required: true });
</script>
