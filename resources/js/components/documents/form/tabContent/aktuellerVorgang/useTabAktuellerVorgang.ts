import { computed, markRaw, ref } from 'vue';

import { get } from '@/api';
import { injectStore } from '@/store/resources/composition';
import {
  ExternalFileResource,
  FileResource,
  VorgangResource,
} from '@/store/resources/types';

import { DocumentsTabMeta } from '../../types';

import AktuellerVorgangDocuments from './AktuellerVorgangDocuments.vue';
import { AktuellerVorgangTab } from './types';

const selectedDocs = ref<ExternalFileResource[]>([]);

const ORIGIN = 'aktuellerVorgang';

let files: FileResource[] | undefined;

const createTab = async (
  initialSelectedDocs: ExternalFileResource[],
  vorgangId: string | undefined,
): Promise<DocumentsTabMeta> => {
  selectedDocs.value = initialSelectedDocs.filter(
    (doc) => doc.attributes.origin === ORIGIN,
  );

  try {
    const store = injectStore();
    let fetchedFiles: FileResource[] = [];

    if (vorgangId !== undefined) {
      const response = await get<VorgangResource>(`/vorgaenge/${vorgangId}`, {
        include: [
          'timeline.element.files',
        ],
        sort: [{
          name: 'updatedAt',
        }],
      });
      fetchedFiles = (response.data?.included?.filter((elem) => elem.type === 'files') ?? []) as FileResource[];
    }

    files = [...store.files.getAll(), ...fetchedFiles]
      .filter((
        resource: FileResource,
        index: number,
        self: FileResource[],
      ) => (
        // filter duplicate files
        index === self.findIndex(
          ({ attributes }) => attributes.size === resource.attributes.size
              && attributes.name === resource.attributes.name,
        )
      ))
      .sort((a, b) => (
        a.attributes.name.localeCompare(b.attributes.name)
      ));
  } catch (_) {
    // no current vorgang available
  }

  return {
    title: 'Aktueller Vorgang',
    component: markRaw(AktuellerVorgangDocuments),
    origin: ORIGIN,
    isVisible: files !== undefined && files.length > 0,
  };
};

export default (): AktuellerVorgangTab => ({
  createTab,

  selectedDocs: computed({
    get: () => selectedDocs.value,
    set: (value) => {
      selectedDocs.value = value;
    },
  }),

  files,
});
