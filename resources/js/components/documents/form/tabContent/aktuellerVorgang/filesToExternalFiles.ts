import { ExternalFileOrigin, ExternalFileResource, FileResource } from '@/store/resources/types';

export function filesToExternalFiles(
  files: FileResource[],
  origin: ExternalFileOrigin,
): ExternalFileResource[] {
  return files.map((file: FileResource) => ({
    id: file.id,
    type: 'files',
    attributes: {
      name: file.attributes.name,
      size: file.attributes.size,
      url: file.links?.self ?? '',
      origin,
    },
  }));
}
