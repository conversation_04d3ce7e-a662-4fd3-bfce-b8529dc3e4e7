<template>
  <DsForm class="space-y-5">
    <DsFormGroup label="Suche">
      <DsInput
        ref="search"
        v-model="debouncedQueryProxy"
        icon="search"
        placeholder="Dateiname"
        immediate
        @keydown.enter.prevent
      />
    </DsFormGroup>

    <DsVirtualList
      v-if="searchedFiles.length > 0"
      v-slot="{item: file}"
      :items="searchedFiles"
      :item-height="41"
      inner-container-class="divide-y"
      outer-container-class="border rounded-md overflow-y-auto max-h-80"
    >
      <DocumentItem
        :download-url="file.links?.self ?? ''"
        :file="file"
        :model-value="isSelectedMap.get(file.id) ?? false"
        @update:model-value="selectDoc($event, file.id)"
      />
    </DsVirtualList>
  </DsForm>
</template>

<script setup lang="ts">
import { DsForm, DsFormGroup, DsInput, DsVirtualList } from '@demvsystems/design-components';
import Fuse from 'fuse.js';
import { computed, onMounted, ref, useTemplateRef } from 'vue';

import DocumentItem from '@/components/documents/form/DocumentItem.vue';
import {
  ExternalFileResource,
  FileResource,
} from '@/store/resources/types';

import { filesToExternalFiles } from './filesToExternalFiles';
import useTabAktuellerVorgang from './useTabAktuellerVorgang';

import FuseResult = Fuse.FuseResult;

const {
  files,
  selectedDocs,
} = useTabAktuellerVorgang();

if (files === undefined) {
  throw Error('Files are undefined!');
}

const fuse = new Fuse(files, {
  keys: ['attributes.name'],
  threshold: 0.4,
});

const query = ref('');
const search = useTemplateRef<HTMLInputElement>('search');

let debounceTimeout: number | null = null;
const debouncedQueryProxy = computed({
  get: () => query.value,
  set: (newQuery: string) => {
    if (debounceTimeout !== null) {
      clearTimeout(debounceTimeout);
    }

    debounceTimeout = window.setTimeout(() => {
      query.value = newQuery;
    }, 250);
  },
});

onMounted(() => {
  search.value?.focus();
});

const searchedFiles = computed<FileResource[]>(() => {
  if (query.value.length === 0) {
    return files;
  }

  fuse.setCollection(files);

  return fuse.search(query.value).map(
    (result: FuseResult<FileResource>) => result.item,
  );
});

const isSelectedMap = ref<Map<string, boolean>>(new Map());

onMounted(() => {
  selectedDocs.value.forEach((selected: ExternalFileResource) => {
    const selectedFile = files.find(
      (file: FileResource) => (
        file.links?.self === selected.attributes.url
      ),
    );

    if (selectedFile === undefined) {
      return;
    }

    isSelectedMap.value.set(selectedFile.id, true);
  });
});

const selectDoc = (isChecked: boolean, url: string) => {
  isSelectedMap.value.set(url, isChecked);

  selectedDocs.value = filesToExternalFiles(
    files.filter((file) => isSelectedMap.value.get(file.id)),
    'aktuellerVorgang',
  );
};
</script>
