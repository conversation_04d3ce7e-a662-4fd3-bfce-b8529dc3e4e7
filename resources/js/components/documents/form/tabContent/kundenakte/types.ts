import { ComputedRef, Ref } from 'vue';

import { ExternalFileResource } from '@/store/resources/types';

import { BaseDocumentsTab, DocumentsTabMeta } from '../../types';

export type KundenakteTab = BaseDocumentsTab & {
  createTab: (
    initialSelectedDocs: ExternalFileResource[],

    kundeId: string | undefined,
    gesellschaftId: string | undefined,
    vertraegeIds: string[],
  ) => DocumentsTabMeta;
  isLoading: ComputedRef<boolean>;
  isLoadingListCount: Ref<number>,
  isLoadingSize: Ref<boolean>;

  kundeId: string | undefined;
  gesellschaftId: string | undefined;
  vertraegeIds: string[];
};
