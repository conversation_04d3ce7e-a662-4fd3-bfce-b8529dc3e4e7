import { computed, markRaw, ref } from 'vue';

import { ExternalFileResource } from '@/store/resources/types';

import { DocumentsTabMeta } from '../../types';

import KundenakteDocuments from './KundenakteDocuments.vue';
import { KundenakteTab } from './types';

const selectedDocs = ref<ExternalFileResource[]>([]);
const isLoadingListCount = ref<number>(0);
const isLoadingSize = ref<boolean>(false);

const ORIGIN = 'kundenakte';

let kundeId: string | undefined;
let gesellschaftId: string | undefined;
let vertraegeIds: string[] = [];

const createTab = (
  initialSelectedDocs: ExternalFileResource[],
  newKundeId: string | undefined,
  newGesellschaftId: string | undefined,
  newVertraegeIds: string[],
): DocumentsTabMeta => {
  selectedDocs.value = initialSelectedDocs.filter(
    (doc) => doc.attributes.origin === ORIGIN,
  );

  kundeId = newKundeId;
  gesellschaftId = newGesellschaftId;
  vertraegeIds = newVertraegeIds;

  return {
    title: 'Kundenakte',
    component: markRaw(KundenakteDocuments),
    origin: ORIGIN,
    isVisible: kundeId !== undefined,
  };
};

export default (): KundenakteTab => ({
  createTab,

  selectedDocs: computed({
    get: () => selectedDocs.value,
    set: (newSelectedDocs) => {
      selectedDocs.value = newSelectedDocs;
    },
  }),

  isLoading: computed(() => isLoadingListCount.value > 0 || isLoadingSize.value),

  isLoadingListCount,
  isLoadingSize,
  kundeId,
  gesellschaftId,
  vertraegeIds,
});
