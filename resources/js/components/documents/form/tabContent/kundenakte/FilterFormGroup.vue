<template>
  <DsFormGroup label="Filtern nach">
    <div class="flex space-x-6">
      <div class="flex space-x-5">
        <DsSwitch
          v-model="doFilterGesellschaft"
          :disabled="doFilterGesellschaft === undefined"
        >
          Gesellschaft
        </DsSwitch>
        <DsSwitch
          v-model="doFilterVertrag"
          :disabled="doFilterVertrag === undefined"
        >
          Vertrag
        </DsSwitch>
      </div>

      <div class="w-px bg-gray-300" />

      <DsSwitch v-model="doFilterOhneZuordnung">
        Ohne Zuordnung
      </DsSwitch>
    </div>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsSwitch } from '@demvsystems/design-components';
import { computed } from 'vue';

import { KundenDocumentFilter } from '../../types';

const props = defineProps<{
  modelValue: KundenDocumentFilter,
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: KundenDocumentFilter): void
}>();

const doFilterGesellschaft = computed({
  get: () => props.modelValue.gesellschaft,
  set: (newValue) => {
    const filter = props.modelValue;

    filter.gesellschaft = newValue;

    if (newValue) {
      filter.ohneZuordnung = false;
    }

    emit('update:modelValue', filter);
  },
});

const doFilterVertrag = computed({
  get: () => props.modelValue.vertrag,
  set: (newValue) => {
    const filter = props.modelValue;

    filter.vertrag = newValue;

    if (newValue) {
      filter.ohneZuordnung = false;
    }

    emit('update:modelValue', filter);
  },
});

const doFilterOhneZuordnung = computed({
  get: () => props.modelValue.ohneZuordnung,
  set: (newValue) => {
    const filter = props.modelValue;

    filter.ohneZuordnung = newValue;

    if (newValue) {
      filter.gesellschaft = filter.gesellschaft === undefined ? undefined : false;
      filter.vertrag = filter.vertrag === undefined ? undefined : false;
    }

    emit('update:modelValue', filter);
  },
});
</script>
