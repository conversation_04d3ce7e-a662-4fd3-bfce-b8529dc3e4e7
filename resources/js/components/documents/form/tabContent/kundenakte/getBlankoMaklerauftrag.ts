import { format } from 'date-fns';

import { AutoAttachmentExternalFileResource } from '@/store/resources/types';

export function getBlankoMaklerauftrag(kundeId: string): AutoAttachmentExternalFileResource {
  return {
    id: '',
    type: 'files',
    attributes: {
      name: `Maklerauftrag-${format(new Date(), 'dd-MM-yyyy')}.pdf`,
      url: `${window.location.origin}/api/kunden/${kundeId}/generateMaklerauftrag`,
      origin: 'kundenakte',
      auto: true,
    },
  };
}
