import { DeepReadonly } from 'vue';

import {
  AutoAttachmentExternalFileResource,
  ExternalFileResource,
  KundenDocumentResource,
} from '@/store/resources/types';

export const mapKundenDocToExternalFile = (
  doc: DeepReadonly<KundenDocumentResource>,
  isAutoAttachment = false,
): ExternalFileResource | AutoAttachmentExternalFileResource => {
  const file = <ExternalFileResource>{
    id: '',
    type: 'files',
    attributes: {
      name: `${doc.attributes.name}.${doc.attributes.extension}`,
      size: doc.attributes.size,
      url: doc.attributes.url,
      origin: 'kundenakte',
    },
  };

  if (isAutoAttachment) {
    const autoAttachmentFile = file as AutoAttachmentExternalFileResource;
    autoAttachmentFile.attributes.auto = true;

    return autoAttachmentFile;
  }

  return file;
};
