<template>
  <DsForm class="space-y-5">
    <!-- kundendokumente -->
    <DsFormGroup label="Suche">
      <DsInput
        ref="search"
        v-model="debouncedQueryProxy"
        icon="search"
        placeholder="Dateiname, Dokumenttyp, Gesellschaft, Sparte"
        immediate
        @keydown.enter.prevent
      />
    </DsFormGroup>

    <FilterFormGroup v-model="filter" />

    <div class="space-y-3">
      <div
        v-if="isLoadingListCount > 0"
        class="space-y-5 p-3"
      >
        <DocumentListSkeleton />
      </div>

      <DsVirtualList
        v-else-if="documentList.length > 0"
        v-slot="{item: doc}"
        :items="documentList"
        :item-height="69"
        :load-next-handler="loadNext"
        :scroll-throttle="200"
        :load-event-offset="500"
        inner-container-class="divide-y"
        outer-container-class="overflow-y-auto border rounded-md max-h-80"
      >
        <DocumentItem
          :type-name="doc.attributes.dokumentTypName ?? undefined"
          :file-name="getFileName(doc)"
          :sub-title="getSubtitle(doc)"
          :download-url="doc.attributes.url"
          :model-value="isSelectedMap.get(doc.attributes.url) ?? false"
          @update:model-value="selectDoc($event, doc.attributes.url)"
        />
      </DsVirtualList>

      <DsAlert
        v-else-if="query.length > 0"
        type="info"
      >
        Für diesen Suchbegriff wurden keine Dokumente gefunden.
      </DsAlert>

      <DsAlert
        v-else-if="filter.gesellschaft || filter.vertrag || filter.ohneZuordnung"
        type="info"
      >
        Für die ausgewählten Filter wurden keine Dokumente gefunden.
      </DsAlert>

      <DsAlert
        v-else-if="hasError"
        type="error"
      >
        Ein unerwarteter Fehler ist aufgetreten.
        Bitte versuchen Sie es später erneut.
      </DsAlert>

      <DsAlert
        v-else
        type="warning"
      >
        Bei dem ausgewählten Kunden sind keine Dokumente vorhanden.
      </DsAlert>
    </div>

    <hr>

    <!-- sonstige dokumente -->
    <div class="rounded-md border">
      <!-- blanko maklerauftrag -->
      <DocumentItem
        :view-url="blankoMaklerauftrag.attributes.url"
        :model-value="isSelectedMap.get(blankoMaklerauftrag.attributes.url) ?? false"
        type-name="Maklervertrag - Maklerauftrag"
        file-name="Personalisierter Maklerauftrag (Blanko)"
        sub-title="Neu generiert"
        @update:model-value="selectDoc($event, blankoMaklerauftrag.attributes.url)"
      />
    </div>
  </DsForm>
</template>

<script setup lang="ts">
import {
  DsAlert,
  DsForm,
  DsFormGroup,
  DsInput,
  DsVirtualList,
} from '@demvsystems/design-components';
import { ignorableWatch } from '@vueuse/core';
import axios, { CanceledError } from 'axios';
import { format, parseISO } from 'date-fns';
import { computed, onMounted, ref, useTemplateRef } from 'vue';

import DocumentItem from '@/components/documents/form/DocumentItem.vue';
import {
  getBlankoMaklerauftrag,
} from '@/components/documents/form/tabContent/kundenakte/getBlankoMaklerauftrag';
import { ExternalFileResource, KundenDocumentResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

import DocumentListSkeleton from '../../DocumentListSkeleton.vue';
import { KundenDocumentFilter } from '../../types';

import FilterFormGroup from './FilterFormGroup.vue';
import { mapKundenDocToExternalFile } from './mapKundenDocToExternalFile';
import useTabKundenakte from './useTabKundenakte';

const hasError = ref(false);
const documentList = ref<KundenDocumentResource[]>([]);
const query = ref('');

let abortController = new AbortController();

const {
  kundeId,
  gesellschaftId,
  vertraegeIds,

  selectedDocs,
  isLoadingListCount,
  isLoadingSize,
} = useTabKundenakte();

if (kundeId === undefined) {
  throw Error('KundeId is missing for this tab!');
}

function getInitFilterValue({ clear } = { clear: false }): KundenDocumentFilter {
  const newFilter = <KundenDocumentFilter>{
    ohneZuordnung: false,
  };

  if (gesellschaftId !== undefined) {
    newFilter.gesellschaft = !clear;
  }

  if (vertraegeIds.length > 0) {
    newFilter.vertrag = !clear;
  }

  return newFilter;
}

const filter = ref<KundenDocumentFilter>(getInitFilterValue());

let debounceTimeout: number | null = null;
const debouncedQueryProxy = computed({
  get: () => query.value,
  set: (newQuery: string) => {
    if (newQuery === query.value) {
      return;
    }

    if (debounceTimeout !== null) {
      clearTimeout(debounceTimeout);
    }

    if (query.value.length === 0 && newQuery.length > 0) {
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      ignoreParamsUpdate(() => {
        filter.value = getInitFilterValue({ clear: true });
      });
    }

    debounceTimeout = window.setTimeout(() => {
      query.value = newQuery;
    }, 400);
  },
});

const search = useTemplateRef<HTMLInputElement>('search');

let nextPageUrl: string | undefined;

const isSelectedMap = ref<Map<string, boolean>>(new Map());
selectedDocs.value.forEach((selected: ExternalFileResource) => {
  isSelectedMap.value.set(selected.attributes.url, true);
});

const blankoMaklerauftrag = computed<ExternalFileResource>(() => (
  getBlankoMaklerauftrag(kundeId)
));

const params = computed(() => {
  if (filter.value.ohneZuordnung) {
    return {
      gesellschaft: 'none',
      vertrag: 'none',
      query: debouncedQueryProxy.value,
    };
  }

  return {
    gesellschaft: filter.value.gesellschaft ? gesellschaftId : null,
    vertrag: filter.value.vertrag ? vertraegeIds[0] : null,
    query: debouncedQueryProxy.value,
  };
});

async function loadDocuments(signal: AbortSignal) {
  if (kundeId === undefined) {
    return;
  }

  documentList.value = [];
  nextPageUrl = undefined;

  try {
    isLoadingListCount.value++;

    const response = await axios.get<Document<KundenDocumentResource[]>>(
      `/api/kunden/${kundeId}/documents`,
      { params: params.value, signal },
    );

    documentList.value = response.data.data ?? [];

    nextPageUrl = response.data.links?.next ?? undefined;
  } catch (error) {
    hasError.value = true;
  } finally {
    isLoadingListCount.value--;
  }
}

async function loadNext() {
  if (!nextPageUrl) {
    return;
  }

  const response = await axios.get<Document<KundenDocumentResource[]>>(
    nextPageUrl,
    { params: params.value },
  );

  documentList.value.push(...response.data.data ?? []);

  nextPageUrl = response.data.links?.next ?? undefined;
}

async function selectDoc(isChecked: boolean, url: string) {
  isSelectedMap.value.set(url, isChecked);

  abortController.abort();
  abortController = new AbortController();

  if (!isChecked) {
    selectedDocs.value = selectedDocs.value.filter((doc) => doc.attributes.url !== url);

    return;
  }

  if (url === blankoMaklerauftrag.value.attributes.url) {
    selectedDocs.value.push({ ...blankoMaklerauftrag.value });

    return;
  }

  let newSelectedDoc = documentList.value.find((doc) => doc.attributes.url === url);

  if (newSelectedDoc === undefined) {
    return;
  }

  try {
    isLoadingSize.value = true;
    // load file size
    newSelectedDoc = (await axios.get<Document<KundenDocumentResource>>(
      `/api/kunden/${kundeId}/documents/${newSelectedDoc.id}`,
      { signal: abortController.signal },
    )).data.data ?? newSelectedDoc;
  } catch (error) {
    if (error instanceof CanceledError) {
      return;
    }
  } finally {
    isLoadingSize.value = false;
  }

  selectedDocs.value.push(mapKundenDocToExternalFile(newSelectedDoc));
}

function getFormattedDateStr(unformatted: string) {
  return format(parseISO(unformatted), 'dd.MM.yyyy');
}

function getFileName({ attributes: attrs }: KundenDocumentResource) {
  return `${attrs.name}.${attrs.extension}`;
}

function getSubtitle({ attributes: attrs }: KundenDocumentResource) {
  return [
    attrs.gesellschaftName,
    attrs.sparteName,
    getFormattedDateStr(attrs.createdAt),
  ].filter((s: string | null) => s !== null).join(', ');
}

onMounted(async () => {
  search.value?.focus();

  await loadDocuments(abortController.signal);
});

const {
  ignoreUpdates: ignoreParamsUpdate,
} = ignorableWatch(params, () => {
  abortController.abort();
  abortController = new AbortController();

  void loadDocuments(abortController.signal);
}, { deep: true });
</script>
