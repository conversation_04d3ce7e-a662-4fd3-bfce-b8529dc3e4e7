import { Component, WritableComputedRef } from 'vue';

import {
  ExternalFileOrigin,
  ExternalFileResource,
} from '@/store/resources/types';

export type KundenDocumentFilter = {
  gesellschaft?: boolean;
  vertrag?: boolean;
  ohneZuordnung: boolean;
};

export type BaseDocumentsTab = {
  selectedDocs: WritableComputedRef<ExternalFileResource[]>;
};

export type DocumentsTabMeta = {
  title: string;
  component: Component;
  origin: ExternalFileOrigin;
  isVisible: boolean;
};
