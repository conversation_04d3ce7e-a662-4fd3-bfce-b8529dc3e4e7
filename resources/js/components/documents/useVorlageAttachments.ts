import axios, { AxiosResponse } from 'axios';
import { Ref, ref } from 'vue';

import {
  getBlankoMaklerauftrag,
} from '@/components/documents/form/tabContent/kundenakte/getBlankoMaklerauftrag';
import {
  mapKundenDocToExternalFile,
} from '@/components/documents/form/tabContent/kundenakte/mapKundenDocToExternalFile';
import { Attachment, ExternalFileResource, KundenDocumentResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

interface ReturnType {
  missingVorlageAttachments: Ref<string[]>,
  vorlageAttachmentFiles: Ref<ExternalFileResource[]>,

  loadVorlageAttachments: (
    attachments: Attachment[],
    kundeId: string | undefined,
    gesellschaftId: string | undefined,
    vertraegeIds: string[],
  ) => Promise<void>,
}

/**
 * Map vorlage attachment dokumenttyp to dokumenttypen
 * (can contain multiple, ordered by importance)
 */
const DOKUMENTTYP_MAP = <Record<string, string[]>>{
  [Attachment.Maklervertrag]: [
    'Maklervollmacht',
    'Maklervertrag - Maklerauftrag',
  ],
  [Attachment.ReisepassPersonalausweis]: [
    'Reisepass',
    'Personalausweis',
  ],
};

export function useVorlageAttachments(): ReturnType {
  const missingVorlageAttachments = ref<string[]>([]);
  const vorlageAttachmentFiles = ref<ExternalFileResource[]>([]);

  async function findDocWithAttributes(
    dokumentTypName: string,
    gesellschaftId: string | undefined,
    vertraegeIds: string[],
    kundeId: string,
  ): Promise<ExternalFileResource | undefined> {
    if (dokumentTypName === Attachment.MaklerauftragBlanko as string) {
      return getBlankoMaklerauftrag(kundeId);
    }

    let response: AxiosResponse<Document<KundenDocumentResource[]>>;
    try {
      response = await axios.get<Document<KundenDocumentResource[]>>(
        `/api/kunden/${kundeId}/documents`,
        {
          params: {
            'dokumentTyp': dokumentTypName,
          },
        },
      );
    } catch {
      return undefined;
    }

    if (response.data.data === undefined) {
      return undefined;
    }

    let doc = response.data.data.find(({ attributes }) => {
      const hasWrongGesellschaft = gesellschaftId !== undefined
        && attributes.gesellschaftId !== null
        && gesellschaftId !== attributes.gesellschaftId;
      const hasWrongVertrag = vertraegeIds.length > 0
        && attributes.vertragId !== null
        && !vertraegeIds.includes(attributes.vertragId ?? '');

      return !(hasWrongGesellschaft || hasWrongVertrag);
    });

    if (doc === undefined) {
      return undefined;
    }

    try {
      // load file size
      doc = (await axios.get<Document<KundenDocumentResource>>(
        `/api/kunden/${kundeId}/documents/${doc.id}`,
      )).data.data ?? doc;
    } catch {
      // do nothing
    }

    return mapKundenDocToExternalFile(doc, true);
  }

  async function loadVorlageAttachments(
    attachments: Attachment[],
    kundeId: string | undefined,
    gesellschaftId: string | undefined,
    vertraegeIds: string[],
  ) {
    missingVorlageAttachments.value = [];
    vorlageAttachmentFiles.value = [];

    if (kundeId === undefined || attachments.length === 0) {
      missingVorlageAttachments.value = [...attachments];

      return;
    }

    await Promise.all(attachments.map(async (attachment: Attachment) => {
      const mappedDokumenttypNames = DOKUMENTTYP_MAP[attachment] ?? [attachment];

      let attachmentFile: ExternalFileResource | undefined = undefined;

      // get newest matching document with highest priority
      for (const dokumentTypName of mappedDokumenttypNames) {
        attachmentFile = await findDocWithAttributes(
          dokumentTypName,
          gesellschaftId,
          vertraegeIds,
          kundeId,
        );

        if (attachmentFile !== undefined) {
          break; // get first doc that is matching the given attributes
        }
      }

      if (attachmentFile === undefined) {
        missingVorlageAttachments.value.push(attachment);

        return;
      }

      vorlageAttachmentFiles.value.push(attachmentFile);
    }));
  }

  return {
    missingVorlageAttachments,
    vorlageAttachmentFiles,

    loadVorlageAttachments,
  };
}
