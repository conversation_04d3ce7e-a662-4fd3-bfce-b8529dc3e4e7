<template>
  <DsModal
    :show="show"
    size="sm"
    anchor="top"
    hide-buttons
    custom-content
    @close="close"
  >
    <suspense>
      <SelectExistingDocumentsForm
        v-if="show"
        :model-value="modelValue"
        :kunde-id="kundeId"
        :gesellschaft-id="gesellschaftId"
        :vertraege-ids="vertraegeIds"
        :vorgang-id="vorgangId"
        data-test="select-existing-documents__modal"
        @update:model-value="emitSelectedDocuments"
        @close="close"
      />
    </suspense>
  </DsModal>

  <div class="space-y-3">
    <div class="flex items-center space-x-3">
      <DsButton
        variant="outline"
        icon="folder-open"
        button-icon-align="right"
        @click="open"
      >
        Aus Dokumenten auswählen
      </DsButton>
      <DsIcon
        v-if="isLoadingCount > 0"
        name="spinner-third"
        class="text-gray-700"
        title="Lade Dokumente aus Vorlage..."
        spin
      />
    </div>

    <DsAlert
      v-if="missingAttachments.length > 0"
      type="warning"
      icon="file-times"
      data-test="missing-vorlage-attachments-alert"
      :label="`${missingAttachments.length} Vorlagendokument${missingAttachments.length > 1 ? 'e' : ''} nicht verfügbar`"
    >
      <p class="my-2">
        Folgende Kundendokumente sind in der ausgewählten Vorlage gesetzt, aber
        {{ !!kundeId ? 'nicht in der Kundenakte verfügbar' : 'es ist kein Kunde ausgewählt' }}:
      </p>

      <ul>
        <li
          v-for="attachment in missingAttachments"
          :key="attachment"
          class="list-inside list-disc font-semibold capitalize"
        >
          {{ attachmentLabels[attachment as Attachment] }}
        </li>
      </ul>
    </DsAlert>

    <DsFileList
      v-if="fileItems.length > 0"
      v-slot="{file}"
      class="min-w-0 shrink"
      :files="fileItems"
    >
      <button
        type="button"
        class="
          flex h-6 w-7 shrink-0 items-center justify-center rounded text-base
          transition duration-200 ease-out hover:bg-red-100 focus:bg-red-100 focus:outline-none
        "
        @click="emit('removeExistingDocument', file)"
      >
        <DsIcon
          name="times"
          class="text-red-500"
          fixed-width
        />
      </button>
    </DsFileList>
  </div>
</template>

<script setup lang="ts">
import {
  DsAlert,
  DsButton,
  DsFileList,
  DsIcon,
  DsModal,
  FileListItem,
} from '@demvsystems/design-components';
import axios from 'axios';
import { startCase, uniqBy } from 'lodash-es';
import { computed, ref, watch } from 'vue';

import {
  filesToExternalFiles,
} from '@/components/documents/form/tabContent/aktuellerVorgang/filesToExternalFiles';
import {
  mapKundenDocToExternalFile,
} from '@/components/documents/form/tabContent/kundenakte/mapKundenDocToExternalFile';
import { injectStore } from '@/store/resources/composition';
import {
  Attachment,
  attachmentLabels,
  AutoAttachmentExternalFileResource,
  ExternalFileResource,
  KundenDocumentResource,
} from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

import SelectExistingDocumentsForm from './form/SelectExistingDocumentsForm.vue';
import { useSparteAttachments } from './useSparteAttachments';
import { useVorlageAttachments } from './useVorlageAttachments';

const props = defineProps<{
  modelValue: ExternalFileResource[],
  vertraegeIds: string[],
  kundeId?: string,
  vorgangId?: string,
  gesellschaftId?: string,
  vorlageAttachments: Attachment[],
  kundendokumentIds: string[],
  addDocsFromFirstElement?: boolean,
  sparteId?: string,
  vorgangstypId?: string,
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', externalFiles: ExternalFileResource[]): void,
  (event: 'removeExistingDocument', existingFile: { url:string }): void,
}>();

const show = ref(false);
const isLoadingCount = ref(0);

// we need the url to identify the file for removing
const fileItems = computed(
  () => props.modelValue.map(
    (doc: ExternalFileResource): FileListItem & { url: string } => ({
      name: `(${startCase(doc.attributes.origin)}) ${doc.attributes.name}`,
      size: doc.attributes.size,
      url: doc.attributes.url,
    }),
  ),
);

function open() {
  show.value = true;
}

function close() {
  show.value = false;
}

function emitSelectedDocuments(docs: ExternalFileResource[]) {
  emit('update:modelValue', docs);
}

const {
  missingVorlageAttachments,
  vorlageAttachmentFiles,
  loadVorlageAttachments,
} = useVorlageAttachments();

const {
  missingSparteAttachments,
  sparteAttachmentFiles,
  loadSparteAttachments,
} = useSparteAttachments();

const missingAttachments = computed(() => {
  return missingVorlageAttachments.value.concat(missingSparteAttachments.value);
});

const vorgangStore = injectStore();

function getFirstTimelineElementFiles() {
  const firstTimelineEintrag = vorgangStore.timelineEintraege.getAll()[0];
  const elementRelationship = firstTimelineEintrag?.relationships?.element;

  let timelineElement;
  if (elementRelationship?.data?.type === 'korrespondenzen') {
    timelineElement = vorgangStore.korrespondenzen.find(elementRelationship.data.id);
  } else if (elementRelationship?.data?.type === 'kommentare') {
    timelineElement = vorgangStore.kommentare.find(elementRelationship.data.id);
  }

  return filesToExternalFiles(
    vorgangStore.files.findAllRelated(timelineElement?.relationships?.files),
    'aktuellerVorgang',
  );
}

// On update of one of the props: update and emit unique document list
watch([
  () => props.kundeId,
  () => props.vorlageAttachments,
  () => props.gesellschaftId,
  () => props.vertraegeIds,
  () => props.kundendokumentIds,
  () => props.sparteId,
  () => props.vorgangstypId,
], async (
  [
    kundeId,
    attachments,
    gesellschaftId,
    vertraegeIds,
    kundendokumentIds,
    sparteId,
    vorgangstypId,
  ],
  [oldKundeId],
) => {
  isLoadingCount.value++;

  // clear files when kunde changes
  const manuallyAddedFiles = kundeId !== oldKundeId
    && oldKundeId !== undefined
    ? []
    : props.modelValue.filter((doc) => (
      (doc as AutoAttachmentExternalFileResource).attributes?.auto !== true
    ));

  await loadVorlageAttachments(attachments, kundeId, gesellschaftId, vertraegeIds);

  await loadSparteAttachments(sparteId, vorgangstypId, kundeId);

  const documents = [
    ...manuallyAddedFiles,
    ...vorlageAttachmentFiles.value,
    ...sparteAttachmentFiles.value,
  ];

  if (props.addDocsFromFirstElement) {
    documents.push(...getFirstTimelineElementFiles());
  }

  // add kundendokumente for given ids
  if (kundeId !== undefined) {
    const kundendokumentFiles = (await Promise.all(kundendokumentIds.map(async (id) => {
      try {
        const response = await axios.get<Document<KundenDocumentResource>>(
          `/api/kunden/${kundeId}/documents/${id}`,
        );

        if (response.data.data === undefined) {
          return undefined;
        }

        return mapKundenDocToExternalFile(response.data.data);
      } catch {
        return undefined;
      }
    }))).filter((
      file: undefined | ExternalFileResource,
    ): file is ExternalFileResource => file !== undefined);

    documents.push(...kundendokumentFiles);
  }

  const uniqueDocuments = uniqBy<ExternalFileResource>(
    documents,
    ({ attributes }) => attributes.url,
  );

  emitSelectedDocuments(uniqueDocuments);

  isLoadingCount.value--;
}, { immediate: true });
</script>
