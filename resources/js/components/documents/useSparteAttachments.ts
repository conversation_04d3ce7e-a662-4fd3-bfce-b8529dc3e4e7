import axios, { AxiosResponse } from 'axios';
import { Ref, ref } from 'vue';

import {
  mapKundenDocToExternalFile,
} from '@/components/documents/form/tabContent/kundenakte/mapKundenDocToExternalFile';
import { Attachment, ExternalFileResource, KundenDocumentResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

interface ReturnType {
  missingSparteAttachments: Ref<string[]>,
  sparteAttachmentFiles: Ref<ExternalFileResource[]>,

  loadSparteAttachments: (
    sparteId: string | undefined,
    vorgangstypId: string | undefined,
    kundeId: string | undefined,
  ) => Promise<void>,
}

const DOKUMENTTYPEN = [
  'Reisepass',
  'Personalausweis',
];

const BERUFSUNFAEHIGKEIT_SPARTEN_IDS = ['135', '290', '244', '153', '154'];
const ALTERSVORSORGE_SPARTEN_IDS = [
  '375', '236', '213', '221', '220', '398', '219', '130', '216', '214', '224',
  '223', '400', '222', '217', '134', '147', '146', '397', '140', '132', '517',
  '144', '142', '396', '139', '131', '145', '143', '399', '141', '215', '352',
];

export function useSparteAttachments(): ReturnType {
  const missingSparteAttachments = ref<string[]>([]);
  const sparteAttachmentFiles = ref<ExternalFileResource[]>([]);

  async function findDocWithAttributes(
    dokumentTypName: string,
    kundeId: string,
  ): Promise<ExternalFileResource | undefined> {
    let response: AxiosResponse<Document<KundenDocumentResource[]>>;
    try {
      response = await axios.get<Document<KundenDocumentResource[]>>(
        `/api/kunden/${kundeId}/documents`,
        {
          params: {
            'dokumentTyp': dokumentTypName,
          },
        },
      );
    } catch {
      return undefined;
    }

    if (response.data.data === undefined) {
      return undefined;
    }

    let doc = response.data.data[0];

    if (doc === undefined) {
      return undefined;
    }

    try {
      // load file size
      doc = (await axios.get<Document<KundenDocumentResource>>(
        `/api/kunden/${kundeId}/documents/${doc.id}`,
      )).data.data ?? doc;
    } catch {
      // do nothing
    }

    return mapKundenDocToExternalFile(doc, true);
  }

  async function loadSparteAttachments(
    sparteId: string | undefined,
    vorgangstypId: string | undefined,
    kundeId: string | undefined,
  ) {
    missingSparteAttachments.value = [];
    sparteAttachmentFiles.value = [];
    const isVorgangstypAntrag = vorgangstypId === '2';

    if (sparteId === undefined) {
      return;
    }

    const isSparteAltersvorsorge = ALTERSVORSORGE_SPARTEN_IDS.includes(
      sparteId,
    );
    const isSparteBerufsunfaehigkeit = BERUFSUNFAEHIGKEIT_SPARTEN_IDS.includes(
      sparteId,
    );

    if  (!isVorgangstypAntrag || !(isSparteAltersvorsorge || isSparteBerufsunfaehigkeit)) {
      return;
    }

    if (kundeId === undefined) {
      return;
    }

    let attachmentFile: ExternalFileResource | undefined = undefined;

    // get newest matching document with highest priority
    for (const dokumentTypName of DOKUMENTTYPEN) {
      attachmentFile = await findDocWithAttributes(
        dokumentTypName,
        kundeId,
      );

      if (attachmentFile !== undefined) {
        break; // get first doc that is matching the given attributes
      }
    }

    if (attachmentFile === undefined) {
      missingSparteAttachments.value = [Attachment.ReisepassPersonalausweis];

      return;
    }

    sparteAttachmentFiles.value.push(attachmentFile);
  }

  return {
    missingSparteAttachments,
    sparteAttachmentFiles,

    loadSparteAttachments,
  };
}
