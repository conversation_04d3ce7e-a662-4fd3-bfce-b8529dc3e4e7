<template>
  <DsButton
    size="sm"
    icon="eye"
    variant="secondary"
    external
    title="Dokument ansehen"
    @click.prevent="showModal = true"
  />
  <DsModal
    :show="showModal"
    hide-buttons
    anchor="top"
    size="md"
    @close="showModal = false"
  >
    <div
      class="flex flex-col"
      style="max-height: calc(100vh - 7rem)"
    >
      <div
        v-if="checkFileType(file) === 'pdf'"
        class="-mx-4 -mt-4 overflow-auto p-6 sm:-mx-6 sm:-mt-6"
      >
        <PdfReader
          class="-m-6"
          :pdf-url="file.links?.self ?? ''"
          :page-scale="1.15"
        />
      </div>
      <img
        v-else
        :src="file.links?.self ?? ''"
        class="mx-auto mb-5"
        :alt="file.attributes.name"
      >

      <footer class="-mx-6 flex items-center justify-end space-x-4 border-t px-6 pt-6">
        <DsButton
          size="lg"
          variant="secondary"
          icon="download"
          :href="file?.links?.self ?? ''"
          external
        >
          {{ checkFileType(file) === 'pdf' ? 'PDF' : 'Bild' }} herunterladen
        </DsButton>
        <DsButton
          v-if="!$slots.cta"
          size="lg"
          @click="showModal = false"
        >
          Schließen
        </DsButton>

        <slot name="cta" />
      </footer>
    </div>
  </DsModal>
</template>

<script setup lang="ts">
import {
  DsButton, DsModal,
} from '@demvsystems/design-components';
import { ref } from 'vue';

import PdfReader from '@/components/pdfReader/PdfReader.vue';
import { BaseFileResource, isFileResource } from '@/store/resources/types';

defineProps<{
  file: BaseFileResource;
}>();

const showModal = ref(false);

const checkFileType = (file: BaseFileResource) => {
  if (isFileResource(file) && file.attributes.mimetype === 'application/pdf') {
    return 'pdf';
  }

  const name = file.attributes.name;
  const extension = name.substring(name.lastIndexOf('.') + 1, name.length);

  return extension.toLowerCase();
};
</script>
