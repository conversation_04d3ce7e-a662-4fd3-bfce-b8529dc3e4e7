<template>
  <DsFileList
    v-slot="{file: fileListItem}"
    :files="fileListItems"
  >
    <DsBadge
      v-if="hasError(fileListItem)"
      title="Beim <PERSON>laden der Datei ist ein Fehler aufgetreten"
      type="error"
    >
      <PERSON><PERSON>
    </DsBadge>
    <template v-else-if="getFileResource(fileListItem).links?.self !== undefined">
      <FileListViewButton
        v-if="isViewable(getFileResource(fileListItem))"
        :file="getFileResource(fileListItem)"
      />

      <FileListDownloadButton
        :href="getFileResource(fileListItem).links?.self ?? ''"
      />

      <DsButton
        v-if="getFileResource(fileListItem).links?.kundenakteSettings"
        :href="getFileResource(fileListItem).links?.kundenakteSettings ?? ''"
        size="sm"
        icon="up-right-from-square"
        variant="secondary"
        title="Original in Kundenakte bearbeiten"
        external
      />
    </template>
  </DsFileList>
</template>

<script setup lang="ts">
import { DsButton, DsFileList, DsBadge, FileListItem } from '@demvsystems/design-components';
import { computed } from 'vue';

import FileListViewButton from '@/components/fileList/FileListViewButton.vue';
import { BaseFileResource, isFileResource } from '@/store/resources/types';

import FileListDownloadButton from './FileListDownloadButton.vue';
import isFileViewable from './isFileViewable';

type FileListItemWithId = FileListItem & {
  id: string,
};

const props = defineProps<{
  files: BaseFileResource[];
}>();

const fileListItems = computed<FileListItemWithId[]>(() => props.files.map(
  (fileResource: BaseFileResource) => ({
    id: fileResource.id,
    name: fileResource.attributes.name,
    size: fileResource.attributes.size,
  }),
));

const getFileResource = (fileListItem: FileListItemWithId): BaseFileResource => {
  const fileResource = props.files.find((
    resource: BaseFileResource,
  ) => resource.id === fileListItem.id);

  if (fileResource === undefined) {
    throw new Error('File not found');
  }

  return fileResource;
};

const isViewable = (fileResource: BaseFileResource) => {
  if (
    isFileResource(fileResource)
    // for octet-stream, determine viewability by extension
    && fileResource.attributes.mimetype !== 'application/octet-stream'
  ) {
    return isFileViewable(fileResource.attributes.mimetype);
  }

  const name = fileResource.attributes.name;
  const extension = name.substring(name.lastIndexOf('.') + 1, name.length);

  return isFileViewable(extension);
};

function hasError(fileListItem: FileListItemWithId): boolean {
  const file = getFileResource(fileListItem);

  if (!isFileResource(file)) {
    return false;
  }

  return file.attributes.status === 'error';
}
</script>
