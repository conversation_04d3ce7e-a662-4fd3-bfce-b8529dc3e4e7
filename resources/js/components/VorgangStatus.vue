<template>
  <DsBadge
    :type="config.type"
    class="whitespace-nowrap"
    :title="config.label"
    data-test="vorgang-status__badge"
  >
    <span>{{ config.label }}</span>
  </DsBadge>
</template>

<script setup lang="ts">
import { BadgeType, DsBadge } from '@demvsystems/design-components';
import { computed, defineProps } from 'vue';

import { statuses, VorgangStatus } from '@/types';

const statusToBadge = statuses.reduce((acc, entry) => {
  acc[entry.value] = entry;

  return acc;
}, {} as Record<VorgangStatus, {
  label: string;
  type: BadgeType
}>);

const props = defineProps<{
  status: VorgangStatus,
}>();

const config = computed(() => statusToBadge[props.status]);
</script>
