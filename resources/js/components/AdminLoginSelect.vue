<template>
  <DsSelect
    id="adminSelect"
    v-model="selected"
    :data="users"
    :search-keys="['attributes.name']"
    :min-search-length="2"
    :item-height="28"
    value-key="id"
    :on-search="onSearch"
    placement="bottom"
    placeholder="Nutzer auswählen"
    title="Als anderer Nutzer einloggen"
    virtualized
    debounce-search
  >
    <template #entry="{entry}">
      <div
        class="truncate"
        :title="entry.attributes.name"
      >
        {{ entry.attributes.name }} ({{ entry.attributes.externalId }})
      </div>
    </template>
  </DsSelect>
</template>

<script setup lang="ts">
import { DsSelect } from '@demvsystems/design-components';
import { nextTick, ref, watch } from 'vue';

import { JsonApiUrl } from '@/api';
import useCurrentUser from '@/components/users/useCurrentUser';
import { useAuthUsers } from '@/composables/useAuthUsers';
import { useImpersonate } from '@/composables/useImpersonate';
import { AuthUserResource } from '@/store/resources/types';

const options = ref<JsonApiUrl<AuthUserResource>>({});
const { users, updateUsers } = useAuthUsers(options);
const { impersonate, leaveImpersonation } = useImpersonate();
const { user } = useCurrentUser();

const selected = ref<string>();

watch(selected, async (userId) => {
  if (userId === undefined || userId === null) {
    if (!user.value?.attributes.isImpersonated) {
      return;
    }

    leaveImpersonation();

    return;
  }

  if (!(await impersonate(userId))) {
    selected.value = undefined;
  }
});

const onSearch = async (query: string) => {
  options.value = {
    find: {
      key: query,
      fields: {
        users: ['name', 'external_id'],
        firma: ['name'],
      },
    },
    sort: [
      {
        name: 'externalId',
      },
    ],
  };

  await nextTick();

  await updateUsers();
};
</script>
