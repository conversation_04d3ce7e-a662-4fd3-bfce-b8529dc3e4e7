<template>
  <li
    :class="{'text-gray-700': inline}"
    class="relative"
  >
    <div class="relative -ml-1.5 flex space-x-4">
      <div class="-mr-1.5">
        <!-- gray vertical line -->
        <span
          class="absolute left-6 -ml-px h-full w-0.5 bg-gray-200"
          :class="{'top-8': first}"
          aria-hidden="true"
        />
        <span
          v-if="last"
          class="absolute left-6 top-8 -ml-px h-full w-1 bg-white"
          aria-hidden="true"
        />
        <!-- icon -->
        <div
          :class="{
            'pt-px': !inline,
          }"
        >
          <div
            :title="iconConfig.helpText"
            class="relative flex w-12 flex-none items-center justify-center rounded-full bg-white text-gray-500"
            :class="{
              'mt-6 h-12': !inline,
              'mt-3 h-8': inline,
            }"
          >
            <div
              class="flex size-9 items-center justify-center rounded-full p-1.5"
              :class="
                {
                  'bg-orange-100 text-orange-500': iconConfig.color === 'orange',
                  'bg-red-100 text-red-500': iconConfig.color === 'red',
                  'bg-green-100 text-green-500': iconConfig.color === 'green',
                  'bg-blue-100 text-blue-500': iconConfig.color === 'blue',
                  'bg-gray-100': !inline && iconConfig.color === undefined,
                }
              "
            >
              <DsIcon
                :name="iconConfig.name"
                fixed-width
                :size="inline ? undefined : 'lg'"
                data-test="timeline__list-item__icon"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- main component -->
      <div
        class="my-3 flex overflow-y-auto"
        :class="{
          'grow py-1': inline,
          'grow rounded-md border': !inline,
          'border-l-orange-500': iconConfig.color === 'orange' && !inline,
          'border-l-red-500': iconConfig.color === 'red' && !inline,
          'border-l-green-500': iconConfig.color === 'green' && !inline,
          'border-l-blue-500': iconConfig.color === 'blue' && !inline,
        }"
      >
        <div
          v-if="!inline && iconConfig.color !== undefined"
          class="-ml-px w-1 shrink-0 grow-0"
          :class="{
            'bg-orange-500': iconConfig.color === 'orange',
            'bg-red-500': iconConfig.color === 'red',
            'bg-green-500': iconConfig.color === 'green',
            'bg-blue-500': iconConfig.color === 'blue',
          }"
        />
        <div :class="inline ? '' : 'w-full p-4 flex-grow space-y-3'">
          <slot name="default" />
        </div>
      </div>
    </div>
  </li>
</template>

<script setup lang="ts">
import { DsIcon } from '@demvsystems/design-components';

import { IconConfig } from './types';

defineProps<{
  inline?: boolean
  last?: boolean
  first?: boolean
  iconConfig: IconConfig
}>();
</script>
