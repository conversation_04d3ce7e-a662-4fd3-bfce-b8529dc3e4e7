<template>
  <DsDropdown
    :items="timelineElementActions"
    button-class="text-sm h-6 w-6 rounded bg-blue-100 hover:bg-blue-200 text-blue-700 hover:text-blue-800"
    :width="130"
    item-class="px-2 py-1 text-gray-800"
    @select="handleSelect"
  >
    <template #button>
      <DsIcon name="ellipsis-vertical" />
    </template>
    <template #item="{item}">
      {{ item.text }}
    </template>
  </DsDropdown>
  <DsModal
    ref="confirmationDeleteModal"
    :variant="ModalVariant.Error"
    title="Ausgewählten Eintrag löschen?"
    icon="trash"
    confirm-label="Ja, Eintrag löschen"
    cancel-label="Nein, Abbrechen"
  >
    <div class="flex min-w-0 flex-col space-y-2">
      <p>
        Sind Sie sicher, dass Sie dieses Timeline-Element löschen möchten?
        Diese Aktion kann nicht mehr rückgängig gemacht werden.
      </p>
      <p>
        Bitte geben Sie zusätzlich den Grund des Löschens an.
      </p>
      <DsForm
        :validation-errors="formErrors"
      >
        <DsFormGroup
          label="Grund"
          validation-name="attributes.content"
          required
        >
          <DsTextarea
            v-model="loeschGrund"
            name="loeschKommentar"
            placeholder="z.B. falsch hinterlegt"
          />
        </DsFormGroup>
      </DsForm>
    </div>
  </DsModal>
</template>

<script setup lang="ts">
import {
  DsDropdown,
  DsForm,
  DsFormGroup,
  DsIcon,
  DsModal,
  DsTextarea,
  ModalInstance,
  ModalVariant,
} from '@demvsystems/design-components';
import { ref } from 'vue';

import useDeleteTimelineElement from '@/composables/useDeleteTimelineElement';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import { eventBus } from '@/store/resources/store';
import type { TimelineEintragResource } from '@/store/resources/types';

const props = defineProps<{
  eintrag: TimelineEintragResource,
}>();

const formErrors = ref({});

eventBus.on(
  'emitValidation',
  (err) => {
    formErrors.value = err;
  },
);

const confirmationDeleteModal = ref<ModalInstance | null>(null);

const store = injectStore();
const vorgang = useVorgangFromRoute();

const loeschGrund = ref('');

async function deleteElement() {
  if (vorgang.value?.id === undefined) {
    return;
  }

  await useDeleteTimelineElement(
    vorgang.value.id,
    props.eintrag,
    store,
    loeschGrund.value,
  );
}

const timelineElementActions = [{
  text: 'Löschen',
  handler: () => {
    formErrors.value = {};
    void confirmationDeleteModal.value?.open({
      confirmed: deleteElement,
    });
  },
}];

const handleSelect = (index: number) => {
  timelineElementActions[index]?.handler();
};
</script>
