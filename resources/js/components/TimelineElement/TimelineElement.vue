<template>
  <TimelineListItem
    :inline="inline"
    :icon-config="iconConfig"
    :first="index === 0"
  >
    <!-- inline display -->
    <template v-if="inline">
      <UserTag
        :user="owner"
        class="font-semibold"
        inline
      />
      <slot name="default" />
      <TimeAgoText
        :value="element.attributes.createdAt"
        class="text-gray-500"
      />
    </template>

    <!-- multiline display -->
    <template v-else>
      <div
        class="flex items-center"
      >
        <div
          v-if="owner !== undefined"
          class="mr-3 flex size-9 shrink-0 items-center justify-center overflow-hidden rounded-full bg-blue-100"
        >
          <template v-if="ownerName === undefined">
            <img
              v-if="isAuftragsvorgang && ersteller.links?.avatar !== undefined"
              :src="ersteller.links.avatar"
              alt="avatar"
            >
            <img
              v-else-if="!isAuftragsvorgang && owner.links?.avatar !== undefined"
              :src="owner.links.avatar"
              alt="avatar"
            >
          </template>
          <DsIcon
            v-else-if="ownerName !== undefined"
            :name="korrespondenzIcon"
            class="text-blue-500"
          />
        </div>

        <div class="grow leading-tight">
          <div>
            <template v-if="isAuftragsvorgang">
              <span
                class="font-semibold"
                v-text="ersteller.attributes.name"
              />
              im Auftrag von
            </template>
            <span
              class="font-semibold"
              v-text="ownerName ?? owner?.attributes.name ?? 'System'"
            />
          </div>
          <span class="text-gray-500">
            <TimeAgoText
              :value="element.attributes.createdAt"
            />
          </span>
        </div>

        <div class="flex gap-2 self-start">
          <slot name="actions" />
          <DeleteElementControls
            v-if="element.attributes.isDeletable"
            :eintrag="element"
          />
        </div>
      </div>
      <slot name="default" />
    </template>
  </TimelineListItem>
</template>

<script setup lang="ts">
import { DsIcon } from '@demvsystems/design-components';
import { computed } from 'vue';

import DeleteElementControls from '@/components/TimelineElement/DeleteElementControls.vue';
import { EmailType } from '@/pages/vorgangAnlegen/types';
import { injectStore } from '@/store/resources/composition';
import type { TimelineEintragResource, UserResource } from '@/store/resources/types';
import { KorrespondenzResource } from '@/store/resources/types';

import TimeAgoText from '../tags/TimeAgoText.vue';
import UserTag from '../tags/UserTag.vue';

import TimelineListItem from './TimelineListItem.vue';
import { IconConfig } from './types';

const props = defineProps<{
  inline?: boolean,
  element: TimelineEintragResource,
  ownerName?: string,
  iconConfig: IconConfig,
  index?: number,
}>();

const store = injectStore();

// TODO support avatars for multiple ersteller types
const owner = computed(() => {
  return store.users.findRelated(props.element.relationships?.owner) as UserResource;
});

const ersteller = computed(() => {
  return store.users.findRelated(props.element.relationships?.ersteller) as UserResource;
});

const korrespondenzElement = computed(() => {
  if (
    props.element.relationships?.element?.data?.type !== 'korrespondenzen'
    || props.element.relationships?.element === undefined
  ) {
    return null;
  }

  return store.korrespondenzen.find(
    props.element.relationships.element.data.id,
  ) as KorrespondenzResource;
});

const korrespondenzIcon = computed<string>((): string => {
  if (korrespondenzElement.value === null) {
    return '';
  }

  switch (korrespondenzElement?.value?.attributes?.emailType) {
    case EmailType.Incoming:
      return 'inbox';
    case EmailType.Uploaded:
      return 'upload';
    default:
      return '';
  }
});

const isAuftragsvorgang = computed(() => {
  if (!ersteller.value) {
    return false;
  }

  return ersteller.value.id !== owner.value.id;
});
</script>
