<template>
  <div class="fixed flex space-x-2 p-4">
    <DsButton
      :disabled="zoomOutIsDisabled"
      variant="secondary"
      class="shadow-sm"
      size="lg"
      @click="emit('out');"
    >
      <DsIcon
        name="minus"
        variant="regular"
      />
    </DsButton>
    <DsButton
      variant="secondary"
      class="shadow-sm"
      size="lg"
      @click="emit('in');"
    >
      <DsIcon
        name="plus"
        variant="regular"
      />
    </DsButton>
  </div>
</template>

<script setup lang="ts">
import { DsButton, DsIcon } from '@demvsystems/design-components';

defineProps({
  zoomOutIsDisabled: Boolean,
});

const emit = defineEmits<{
  (event: 'in'): void,
  (event: 'out'): void,
}>();
</script>
