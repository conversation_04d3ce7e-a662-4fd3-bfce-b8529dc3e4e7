<template>
  <div class="flex items-center justify-center">
    <div
      v-if="isLoading"
      class="m-8 text-center"
    >
      <DsIcon
        name="spinner-third"
        variant="duotone"
        size="2x"
        class="text-blue-500"
        spin
      />
      <div class="mt-4">
        PDF wird abgerufen...
      </div>
    </div>

    <DsAlert
      v-else-if="!isPdfLoaded"
      class="m-8"
      type="error"
      label="Das Dokument konnte nicht geladen werden."
    >
      {{ errorMessage }}
    </DsAlert>

    <div
      v-else
      ref="canvasWrapper"
      class="size-full bg-gray-100"
    >
      <Zoom
        :zoom-out-is-disabled="zoomOutIsDisabled"
        @in="changeZoom(0.1)"
        @out="changeZoom(-0.1)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsAlert, DsIcon } from '@demvsystems/design-components';
import {
  getDocument,
  GlobalWorkerOptions,
  PDFDocumentProxy,
  version,
} from 'pdfjs-dist/legacy/build/pdf.mjs';
import { onUnmounted, ref, useTemplateRef, watch } from 'vue';

import Zoom from './Zoom.vue';
import { isGetDocumentError } from './types';

GlobalWorkerOptions.workerSrc = (
  `https://unpkg.com/pdfjs-dist@${version}/legacy/build/pdf.worker.min.mjs`
);

const {
  pdfUrl = undefined,
  pdfDocument = undefined,
  pageScale = 1,
  suppressErrorsAndRetry,
} = defineProps<{
  pdfUrl?: string,
  pdfDocument?: string,
  pageScale: number,
  suppressErrorsAndRetry?: boolean,
}>();

const errorMessage = ref('');

const setErrorMessage = (e: Error) => {
  switch (e.name) {
    case 'MissingPDFException':
      errorMessage.value = 'Bitte versuchen Sie es später erneut.';

      break;
    default:
      errorMessage.value = 'Ein unerwarteter Fehler ist aufgetreten.';
  }
};

const scale = ref(pageScale);
const zoomOutIsDisabled = ref(false);

const canvasWrapper = useTemplateRef<HTMLElement>('canvasWrapper');

let pdf: PDFDocumentProxy | null = null;

const isLoading = ref(false);
const isPdfLoaded = ref(false);

let tryAgain = suppressErrorsAndRetry; // does not have to be reactive

let pdfPollingIntervalId: number | undefined = undefined;

onUnmounted(() => {
  clearInterval(pdfPollingIntervalId);
});

const getPdf = async () => {
  isLoading.value = true;

  try {
    if (pdfUrl !== undefined) {
      pdf = await getDocument(pdfUrl).promise;
    }

    if (pdfDocument !== undefined) {
      const pdfString = atob(pdfDocument);
      pdf = await getDocument({ data: pdfString }).promise;
    }

    isPdfLoaded.value = true;
    tryAgain = false;
  } catch (e: unknown) {
    if (isGetDocumentError(e)) {
      if (e.status !== undefined && e.status >= 500) {
        tryAgain = false; // stop polling when we get a 500
      }

      setErrorMessage(e);
    }
  }

  if (!tryAgain) {
    isLoading.value = false;

    clearInterval(pdfPollingIntervalId);
  }
};

void (async () => {
  await getPdf();

  if (!tryAgain) {
    return;
  }

  pdfPollingIntervalId = window.setInterval(
    () => void getPdf(),
    1000,
  );
})();

const makeCanvas = (pageNumber: number) => {
  const canvas = document.createElement('canvas');
  canvas.className = 'canvas my-3 mx-auto border';

  const existingCanvas = canvasWrapper.value
    ?.getElementsByTagName('canvas')[pageNumber - 1];

  if (existingCanvas !== undefined) {
    existingCanvas.replaceWith(canvas);
  } else {
    canvasWrapper.value?.appendChild(canvas);
  }

  return canvas;
};

const renderPage = async (pageNumber: number) => {
  if (pdf === null) {
    return;
  }

  const canvas = makeCanvas(pageNumber);

  const page = await pdf.getPage(pageNumber);

  const viewport = page.getViewport({ scale: scale.value ?? null });
  canvas.height = viewport.height;
  canvas.width = viewport.width;

  const canvasContext = canvas.getContext('2d');
  if (canvasContext === null) {
    return;
  }

  page.render({ canvasContext, viewport });
};

const renderDocument = async () => {
  if (pdf === null) {
    return;
  }

  for (let pageNumber = 1; pageNumber <= pdf.numPages; pageNumber++) {
    await renderPage(pageNumber);
  }
};

const changeZoom = async (amount: number) => {
  if (scale.value < 0.2 && amount < 0) {
    zoomOutIsDisabled.value = true;

    return;
  }

  zoomOutIsDisabled.value = false;
  scale.value += amount;

  await renderDocument();
};

watch(canvasWrapper, async () => {
  await renderDocument();
});
</script>
