<template>
  <div
    class="flex size-7 cursor-pointer items-center justify-center rounded
    bg-blue-100 text-blue-500 transition duration-100  ease-out hover:bg-blue-200"
    :class="{['group-hover:bg-blue-200']: grouped}"
  >
    <DsIcon :name="icon" />
  </div>
</template>

<script setup lang="ts">
import { DsIcon } from '@demvsystems/design-components';
import { defineProps } from 'vue';

defineProps<{
  icon: string;
  grouped?: boolean;
}>();
</script>
