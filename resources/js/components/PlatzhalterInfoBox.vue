<template>
  <DsPopover
    :show="isPopoverOpen"
    :variant="PopoverVariant.White"
    placement="top"
    @click-outside="close"
  >
    <div ref="button">
      <DsButton
        icon="info-circle"
        size="sm"
        variant="secondary"
        @click="togglePopover"
      >
        Platzhalter
      </DsButton>
    </div>

    <template #content>
      <div class="space-y-2 p-2 text-sm">
        <p class="font-bold">
          Wie werden Platzhalter verwendet?
        </p>
        <img
          :src="imgUrl"
          alt="Beispiel Platzhalterauswahl"
        >
        <p>
          Um einen Platzhalter einzufügen, tippen Sie zunächst die
          Raute-Taste ("#") im Betreff- oder im Nachrichten-Feld ein,
          wodurch sich die Platzhalter-Auswahl öffnet.
          Sie können die Liste durch weitere Eingaben filtern und einen
          Eintrags mittels Mausklick oder Enter-Taste in den Text einfügen.
        </p>
      </div>
    </template>
  </DsPopover>
</template>

<script setup lang="ts">
import { DsButton, DsPopover, PopoverVariant } from '@demvsystems/design-components';
import { ref, useTemplateRef } from 'vue';

import imgUrl from '../../img/tags_workflow.gif';

const isPopoverOpen = ref(false);
const button = useTemplateRef<HTMLElement>('button');

const togglePopover = () => {
  if (isPopoverOpen.value) {
    isPopoverOpen.value = false;

    return;
  }

  isPopoverOpen.value = true;
};

const close = (event?: Event) => {
  if (event !== undefined && button.value?.contains(event.target as Node)) {
    return;
  }

  isPopoverOpen.value = false;
};
</script>
