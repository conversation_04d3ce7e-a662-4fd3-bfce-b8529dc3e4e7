<template>
  <div
    data-test="untervorgaenge-list__wrapper"
  >
    <h3 class="mb-3 text-sm font-semibold uppercase text-gray-700">
      Untervorgänge
    </h3>
    <div
      v-if="isLoading"
      class="space-y-3"
    >
      <DsSkeleton class="h-8 w-80" />
      <DsSkeleton
        class="h-48 w-full opacity-75"
        gradient
      />
      <DsSkeleton
        class="h-12 w-full opacity-50"
        gradient
      />
    </div>
    <div
      v-else
      class="space-y-3"
    >
      <DsInput
        v-model="searchTerm"
        icon="search"
        placeholder="Suche"
        data-test="untervorgaenge-list__search-input"
        class="w-80"
        @change="clearAndLoadUntervorgaenge"
        @submit.prevent
      />
      <Accordion
        v-model="offeneUntervorgaengeAccordion"
        :title="`Offene Untervorgänge${meta.offene ? ` (${meta.offene.total})` : ''}`"
        no-animation
      >
        <Untervorgaenge
          :untervorgaenge="offeneUntervorgaenge"
          :vorgangsgruppe="props.vorgangsgruppe"
          :load-next-handler="() => loadNext('offene')"
        />
      </Accordion>
      <Accordion
        v-model="erledigteUntervorgaengeAccordion"
        :title="`Erledigte Untervorgänge${meta.erledigte ? ` (${meta.erledigte.total})` : ''}`"
        no-animation
      >
        <Untervorgaenge
          :untervorgaenge="erledigteUntervorgaenge"
          :vorgangsgruppe="props.vorgangsgruppe"
          :load-next-handler="() => loadNext('erledigte')"
        />
      </Accordion>
    </div>
  </div>
  <UntervorgaengeStatusModal
    :show="showStatusModal"
    @close="emit('closeStatusModal')"
    @confirm="setUntervorgaengeStatusToErledigt"
  />
</template>

<script setup lang="ts">
import { DsInput, DsSkeleton } from '@demvsystems/design-components';
import { computed, ref } from 'vue';

import { get, put } from '@/api';
import Accordion from '@/components/Accordion.vue';
import Untervorgaenge from '@/components/unterVorgaenge/Untervorgaenge.vue';
import { VorgangStatusFilterMapping } from '@/pages/vorgaenge/components/filter/types';
import UntervorgaengeStatusModal
  from '@/pages/vorgang/components/Modals/UntervorgaengeStatusModal.vue';
import { createStore } from '@/store/resources';
import { provideStore } from '@/store/resources/composition';
import { eventBus } from '@/store/resources/store';
import { VorgangResource } from '@/store/resources/types';
import { VorgangStatus } from '@/types';
import { MetaObject } from '@/types/jsonapi';

const store = createStore();
provideStore(store);

const props = defineProps<{
  vorgangsgruppe: VorgangResource,
  showStatusModal: boolean,
}>();

const emit = defineEmits<{
  (event: 'closeStatusModal'): void
  (event: 'reloadVorgangsgruppe'): void
}>();

const isLoading = ref(true);

const offeneUntervorgaenge = computed(() => store.vorgaenge.getAll().filter(
  (vorgang) => vorgang.attributes.status !== VorgangStatus.Erledigt,
));

const erledigteUntervorgaenge = computed(() => store.vorgaenge.getAll().filter(
  (vorgang) => vorgang.attributes.status === VorgangStatus.Erledigt,
));

const offeneAccordion = ref(true);
const erledigteAccordion = ref(false);

const offeneUntervorgaengeAccordion = computed({
  get: () => offeneAccordion.value && offeneUntervorgaenge.value.length > 0,
  set: (value: boolean) => {
    offeneAccordion.value = value;
  },
});

const erledigteUntervorgaengeAccordion = computed({
  get: () => erledigteAccordion.value && erledigteUntervorgaenge.value.length > 0,
  set: (value: boolean) => {
    erledigteAccordion.value = value;
  },
});

const searchTerm = ref('');

type StatusKey = 'offene' | 'erledigte';

type Next = Record<StatusKey, string | null>;
type Meta = Record<StatusKey, MetaObject | null>;

const next = ref<Next>({
  offene: null,
  erledigte: null,
});

const meta = ref<Meta>({
  offene: null,
  erledigte: null,
});

async function loadUntervorgaenge(
  status: StatusKey,
) {
  const filterStatus = status === 'offene'
    ? VorgangStatusFilterMapping.Offen
    : VorgangStatusFilterMapping.Erledigt;

  try {
    const response = await get<VorgangResource>('/vorgaenge', {
      include: [
        'kunde',
        'sparte',
        'gesellschaft',
        'notifications',
      ],
      find: {
        key: searchTerm.value,
        fields: {
          kunde: ['name', 'external_id'],
          sparte: ['name'],
          vorgaenge: ['titel', 'vorgangsnummer'],
          gesellschaft: ['name'],
        },
      },
      fields: {
        kunden: ['name'],
        gesellschaften: ['name'],
        korrespondenzen: ['status', 'versandart'],
        mahnungen: ['status'],
        erinnerungen: ['status'],
      },
      sort: [
        {
          name: 'hasNotification',
          order: 'desc',
        },
      ],
      filter: {
        ...(filterStatus !== null ? { status: filterStatus } : {}),
        ueberVorgang: props.vorgangsgruppe.id,
      },
    }, {
      params: {
        per_page: 15,
      },
    });

    store.load(response.data);

    next.value[status] = response.data.links?.next ?? null;
    meta.value[status] = response.data.meta ?? null;
  } catch (e) {
    eventBus.emit('error', 'Untervorgänge konnten nicht geladen werden.');
  } finally {
    isLoading.value = false;
  }
}

async function loadNext(status: StatusKey) {
  if (next.value[status] === null) {
    return;
  }

  const response = await get<VorgangResource>(next.value[status] as string);

  store.load(response.data);

  next.value[status] = response.data.links?.next ?? null;
}

async function clearAndLoadUntervorgaenge() {
  store.clear();

  await loadUntervorgaenge('offene');
  await loadUntervorgaenge('erledigte');
}

async function setUntervorgaengeStatusToErledigt() {
  emit('closeStatusModal');

  await put<VorgangResource>(
    props.vorgangsgruppe.links?.self + '/untervorgaenge',
    {
      data: {
        type: 'vorgaenge',
        attributes: {
          status: VorgangStatus.Erledigt,
        },
      },
    });

  await clearAndLoadUntervorgaenge();

  emit('reloadVorgangsgruppe');
}

void loadUntervorgaenge('offene');
void loadUntervorgaenge('erledigte');
</script>
