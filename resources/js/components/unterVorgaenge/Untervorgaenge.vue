<template>
  <DsVirtualList
    :items="untervorgaenge"
    :item-height="48"
    :load-next-handler="loadNextHandler"
    :scroll-throttle="200"
    :buffer="15"
    outer-container-class="flex-1 min-h-0 overflow-y-auto max-h-96 overflow-hidden rounded-md rounded-t-none border-t -m-3 mt-0"
    inner-container-class="divide-y"
  >
    <template #default="{item: untervorgang}">
      <Untervorgang
        :untervorgang="untervorgang"
        :vorgangsgruppe="props.vorgangsgruppe"
        :display-sparte="displaySparte"
        data-test="untervorgaenge-list__item"
      />
    </template>
  </DsVirtualList>
</template>

<script setup lang="ts">
import { DsVirtualList } from '@demvsystems/design-components';
import { computed } from 'vue';

import Untervorgang from '@/components/unterVorgaenge/Untervorgang.vue';
import { VorgangResource } from '@/store/resources/types';

const props = defineProps<{
  untervorgaenge: VorgangResource[],
  vorgangsgruppe: VorgangResource,
  loadNextHandler: () => Promise<void>,
}>();

const displaySparte = computed(() => {
  if (!props.untervorgaenge.length) {
    return false;
  }

  const firstSparteId = props.untervorgaenge[0]?.relationships?.sparte?.data?.id;

  const filteredList = props.untervorgaenge.filter(
    (untervorgang) => untervorgang.relationships?.sparte?.data?.id !== firstSparteId,
  );

  return (filteredList.length > 0);
});
</script>
