<template>
  <router-link
    v-if="untervorgang.attributes.titel
      && untervorgang.attributes.status
      && untervorgang.links?.self"
    :to="{
      name: 'vorgaenge.show',
      params: {
        firmaId,
        vorgangsnummer: untervorgang.attributes.vorgangsnummer,
      },
    }"
    data-test="unter_vorgaenge__row"
    class="group relative flex cursor-pointer items-center space-x-3 px-2"
  >
    <div
      class="absolute inset-y-0 left-0 w-0 cursor-pointer transition-[width] group-hover:w-1.5 group-hover:hover:w-2"
      :class="{
        'w-1.5': notificationSeverity !== null,
        'bg-blue-500': notificationSeverity === NotificationSeverity.Info,
        'bg-red-500': notificationSeverity === NotificationSeverity.Error,
        'bg-gray-300': notificationSeverity === null,
      }"
      @click.prevent="markVorgang"
    />
    <div
      class="flex grow flex-row items-center space-x-3 space-y-0 truncate"
      data-test="unter_vorgaenge__row_data"
    >
      <div
        class="flex h-12 grow flex-col justify-center truncate leading-5"
        :class="{
          'text-blue-700': notificationSeverity === NotificationSeverity.Info,
          'text-red-700': notificationSeverity === NotificationSeverity.Error,
          'text-gray-800': notificationSeverity === null,
        }"
      >
        <div>
          <span
            v-if="titelOrGesellschaft"
            class="font-semibold"
          >
            {{ titelOrGesellschaft }}
          </span>
          <span
            v-if="sparte !== undefined && displaySparte"
          >
            ({{ sparte?.attributes.displayName }})
          </span>
        </div>
        {{ kunde?.attributes.name }}
      </div>

      <div class="flex items-center space-x-3">
        <TimeAgoTag
          v-if="untervorgang.attributes.faelligAt
            && untervorgang.attributes.status !== 'erledigt'"
          :value="untervorgang.attributes.faelligAt"
        />
        <VorgangStatus :status="untervorgang.attributes.status" />
      </div>
    </div>

    <IconBtn
      icon="arrow-right"
      class="ml-1 flex-none"
      grouped
    />
  </router-link>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVorgangNotification } from '@/pages/vorgaenge/composables/useVorgangNotifications';
import { injectStore } from '@/store/resources/composition';
import {
  BasicVorgangResource,
  NotificationSeverity,
  sortNotificationSeverityByPriority,
  VorgangResource,
} from '@/store/resources/types';

import IconBtn from '../IconBtn.vue';
import VorgangStatus from '../VorgangStatus.vue';
import TimeAgoTag from '../tags/TimeAgoTag.vue';
import useCurrentUser from '../users/useCurrentUser';

const props = defineProps<{
  untervorgang: BasicVorgangResource,
  vorgangsgruppe: VorgangResource,
  displaySparte: boolean,
}>();

const store = injectStore();

const gesellschaft = computed(() => store.gesellschaften.findRelated(
  props.untervorgang.relationships?.gesellschaft,
));

const kunde = computed(() => store.kunden.findRelated(
  props.untervorgang.relationships?.kunde,
));

const sparte = computed(() => store.sparten?.findRelated(
  props.untervorgang.relationships?.sparte,
));

const { user } = useCurrentUser();
const firmaId = computed(() => user.value?.attributes.firmaId);

const manuallySetNotification = ref<boolean | null>(null);

const notificationSeverity = computed(() => {
  if (manuallySetNotification.value) {
    return NotificationSeverity.Info;
  }

  const notifications = store.notifications
    .findAllRelated(props.untervorgang.relationships?.notifications)
    .sort((a, b) => (
      sortNotificationSeverityByPriority(a.attributes.severity, b.attributes.severity)
    ));

  return notifications[0]?.attributes.severity ?? null;
});

const titelOrGesellschaft = computed(() => {
  if (
    props.untervorgang.attributes.titel !== props.vorgangsgruppe.attributes.titel
    && !props.untervorgang.attributes.titel.includes('Bestandsübertragung')
  ) {
    return props.untervorgang.attributes.titel;
  }

  return gesellschaft.value?.attributes.name ?? '';
});

const { createNotification, deleteNotifications } = useVorgangNotification();

const markVorgang = async () => {
  if (notificationSeverity.value !== null) {
    manuallySetNotification.value = false;
    await deleteNotifications(props.untervorgang.id);

    return;
  }

  manuallySetNotification.value = true;
  await createNotification(props.untervorgang.id);
};
</script>
