<template>
  <aside class="flex flex-col divide-y overflow-hidden">
    <form
      class="flex flex-row space-x-2 px-3 py-2"
      @submit.prevent
    >
      <slot name="form" />
    </form>

    <div
      class="z-0 flex h-9 items-center justify-between space-x-3 bg-gray-50 px-3 py-1  shadow-sm"
    >
      <slot name="actions" />

      <DsDropdown
        v-if="actionsButtonText !== undefined
          && sideListActions !== undefined
          && sideListActions.length > 0"
        :items="sideListActions"
        button-class="h-8 px-2 text-sm font-medium text-gray-700 rounded  hover:bg-gray-200 hover:text-gray-900"
        item-class="px-2 py-1 text-gray-800"
        @open="iconName = SidleListIcon.ListIsOpen"
        @close="iconName = SidleListIcon.ListIsClosed"
        @select="handleSelect"
      >
        <template #button>
          {{ actionsButtonText }}
          <DsIcon
            class="ml-0.5"
            :name="iconName"
          />
        </template>
        <template #item="{item}">
          {{ item.text }}
        </template>
      </DsDropdown>

      <DsSkeleton
        v-else-if="isLoading"
        class="h-4 w-28"
      />
    </div>

    <slot
      v-if="!isLoading"
      name="nav"
    />
    <!-- skelies -->
    <div
      v-else
      class="flex w-full flex-col items-stretch"
    >
      <article
        v-for="entry in [
          'opacity-100',
          'opacity-80',
          'opacity-60',
          'opacity-40',
          'opacity-20',
        ]"
        :key="entry"
        :class="entry"
        class="flex space-x-3 border-b p-3 py-3.5"
        style="height: 97px"
      >
        <DsSkeleton class="size-4 rounded" />

        <div class="grow space-y-3.5">
          <p class="flex justify-between">
            <DsSkeleton class="h-4 w-2/3" />
            <DsSkeleton class="h-4 w-1/4" />
          </p>

          <p class="flex space-x-4">
            <DsSkeleton class="h-3 w-1/4" />
            <DsSkeleton class="h-3 w-2/4" />
          </p>

          <p class="grid grid-cols-2 gap-3">
            <DsSkeleton class="h-3 w-3/4" />
            <DsSkeleton class="h-3 w-full" />
          </p>
        </div>
      </article>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { DsDropdown, DsIcon, DsSkeleton } from '@demvsystems/design-components';
import { ref } from 'vue';

import { SideListAction, SidleListIconName } from './types';

const SidleListIcon = SidleListIconName;

const props = defineProps<{
  isLoading: boolean,
  sideListActions?: SideListAction[],
  /** left undefined will hide the action button */
  actionsButtonText?: string,
}>();

const handleSelect = (index: number) => {
  props.sideListActions?.[index]?.handler();
};

const iconName = ref<SidleListIconName>(SidleListIcon.ListIsClosed);
</script>
