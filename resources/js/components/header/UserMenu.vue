<template>
  <DsDropdown
    :items="options"
    placement="bottom-end"
    group-key="group"
    class="hidden rounded lg:block"
    button-class="font-medium text-sm h-8 px-2 flex items-center space-x-2 max-w-60 min-w-32 justify-center"
    item-class="px-2 py-1 flex flex-row space-x-2 items-center text-gray-800"
    :class="{
      'bg-red-500 text-white hover:bg-red-700 hover:text-white': user?.attributes?.isImpersonated ?? false,
      'text-gray-500 hover:bg-gray-200 hover:text-gray-800': !user?.attributes?.isImpersonated,
    }"
    group-with-dividers
    @select="handleSelect"
  >
    <template #button>
      <template v-if="user?.attributes">
        <DsIcon
          v-if="user?.attributes?.isImpersonated"
          fixed-width
          name="triangle-exclamation"
          title="Sie sind derzeit als ein anderer Nutzer eingeloggt!"
        />
        <DsIcon
          v-else
          name="user"
        />

        <div
          class="truncate"
          :title="user?.attributes?.name"
        >
          {{ user?.attributes?.name }}
        </div>
        <div v-if="user?.attributes?.isImpersonated && user?.attributes?.externalId">
          ({{ user?.attributes?.externalId }})
        </div>
      </template>

      <DsSkeleton
        v-else
        class="h-4 w-32"
      />

      <DsIcon name="chevron-down" />
    </template>
    <template #item="{item}">
      <DsIcon
        :name="item.icon"
        fixed-width
      />
      <span>{{ item.label }}</span>
    </template>
  </DsDropdown>
</template>

<script setup lang="ts">
import { DsDropdown, DsIcon, DsSkeleton } from '@demvsystems/design-components';

import { HeaderMenuOption } from '@/components/header/types';

import useCurrentUser from '../users/useCurrentUser';

const { user } = useCurrentUser();

const props = defineProps<{
  options: HeaderMenuOption[],
}>();

function handleSelect(index: number) {
  props.options[index]?.handler();
}
</script>
