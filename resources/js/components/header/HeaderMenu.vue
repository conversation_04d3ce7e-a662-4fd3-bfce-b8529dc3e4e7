<template>
  <nav
    class="z-10 hidden items-end space-x-8 lg:flex"
    data-test="nav"
  >
    <NavLink
      v-for="(option, index) in navOptions"
      :key="index"
      :to="option.route as LocationAsRelativeRaw"
    >
      {{ option.label }}
      <DsBadge
        v-if="option.badge"
        :type="option.badge.type"
        class="ml-1"
      >
        {{ option.badge.label }}
      </DsBadge>
    </NavLink>
  </nav>
  <div class="flex grow flex-row items-center justify-end space-x-4">
    <HeaderAction
      v-if="isAtRoute('kampagnen.index')"
      :handler="() => void router.push({name: 'kampagnen.create.basis'})"
      label="Kampagne erstellen"
    />
    <HeaderAction
      v-else-if="isAtRoute('vorlagen.show')"
      :handler="() => void router.push({name: 'vorlagen.show'})"
      label="Neue Vorlage"
    />
    <VorgangAnlegenModal
      v-else
    />

    <UserMenu
      :options="userOptions"
    />

    <MobileMenu
      :options="[...navOptions, ...userOptions]"
    />
  </div>

  <DsModal
    :show="openModal && isImpersonationAvailable"
    title="Als Makler einloggen"
    cancel-only
    @close="openModal = false"
  >
    <AdminLoginSelect />
  </DsModal>
</template>

<script setup lang="ts">
import { DsBadge, DsModal } from '@demvsystems/design-components';
import { computed, ref } from 'vue';
import { LocationAsRelativeRaw } from 'vue-router';

import AdminLoginSelect from '@/components/AdminLoginSelect.vue';
import HeaderAction from '@/components/header/HeaderAction.vue';
import MobileMenu from '@/components/header/MobileMenu.vue';
import NavLink from '@/components/header/NavLink.vue';
import UserMenu from '@/components/header/UserMenu.vue';
import { HeaderMenuOption } from '@/components/header/types';
import useCurrentUser from '@/components/users/useCurrentUser';
import { useImpersonate } from '@/composables/useImpersonate';
import VorgangAnlegenModal from '@/pages/vorgangAnlegen/components/VorgangAnlegenModal.vue';
import router from '@/router';

const { user } = useCurrentUser();
const { leaveImpersonation } = useImpersonate();

function isAtRoute(route: string): boolean {
  return router.currentRoute.value.matched.some(({ name }) => name === route);
}

const openModal = ref(false);

const isImpersonationAvailable = computed(() => (
  user?.value?.attributes?.isImpersonated || user?.value?.attributes?.canImpersonate
));

async function navigateTo(route?: LocationAsRelativeRaw) {
  if (route === undefined) {
    return;
  }

  await router.push(route);
}

const navOptions: HeaderMenuOption[] = [
  {
    label: 'Vorgänge',
    icon: 'inbox',
    group: 'navigation',
    handler: () => void navigateTo({ name: 'vorgaenge.index' }),
    route: { name: 'vorgaenge.index' },
  },
  {
    label: 'Kampagnen',
    icon: 'envelopes-bulk',
    group: 'navigation',
    handler: () => void navigateTo({ name: 'kampagnen.list' }),
    route: { name: 'kampagnen.list' },
  },
  {
    label: 'Vorlagen',
    icon: 'file-lines',
    group: 'navigation',
    handler: () => void navigateTo({ name: 'vorlagen.show' }),
    route: { name: 'vorlagen.show' },
  },
];

const userOptions = computed<HeaderMenuOption[]>(() => [
  ...(isImpersonationAvailable.value ? [{
    label: 'Als Makler einloggen',
    icon: 'user',
    group: 'impersonation',
    handler: () => openModal.value = true,
  }] : []),
  ...(user?.value?.attributes?.isImpersonated ? [{
    label: 'Als Makler ausloggen',
    icon: 'user-slash',
    group: 'impersonation',
    handler: () => leaveImpersonation(),
  }] : []),
  {
    label: 'Einstellungen',
    icon: 'wrench',
    group: 'settings',
    handler: () => void navigateTo({ name: 'settings.index' }),
  },
  {
    label: 'Logout',
    icon: 'power-off',
    group: 'settings',
    handler: () => window.location.href = '/logout',
  },
]);
</script>
