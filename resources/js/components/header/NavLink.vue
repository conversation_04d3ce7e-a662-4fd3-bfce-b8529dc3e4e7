<template>
  <router-link
    :to="to"
    class="flex h-8 items-center justify-center border-b-4 border-transparent py-4 text-sm font-medium text-gray-800 hover:text-blue-500"
    active-class="!text-blue-500 !border-blue-500"
  >
    <slot />
  </router-link>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { LocationAsRelativeRaw } from 'vue-router';

defineProps<{
  to: LocationAsRelativeRaw;
}>();
</script>
