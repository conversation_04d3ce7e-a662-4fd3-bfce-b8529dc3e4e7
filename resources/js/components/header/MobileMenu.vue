<template>
  <DsDropdown
    :items="options"
    placement="bottom-end"
    group-key="group"
    class="block rounded lg:hidden"
    button-class="h-8 px-3 rounded hover:bg-gray-200"
    item-class="px-4 py-3 flex flex-row space-x-2 items-center text-gray-800"
    group-with-dividers
    :width="200"
    disable-max-height
    @select="handleSelect"
  >
    <template #button>
      <DsIcon name="bars" />
    </template>
    <template #item="{item}">
      <DsIcon
        :name="item.icon"
        fixed-width
      />
      <span>{{ item.label }}</span>
    </template>
  </DsDropdown>
</template>

<script setup lang="ts">
import { DsDropdown, DsIcon } from '@demvsystems/design-components';

import { HeaderMenuOption } from '@/components/header/types';

const props = defineProps<{
  options: HeaderMenuOption[],
}>();

function handleSelect(index: number) {
  props.options[index]?.handler();
}
</script>
