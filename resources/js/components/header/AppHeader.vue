<template>
  <header
    class="flex h-16 flex-none items-center border-b bg-white bg-cover bg-right bg-no-repeat"
    data-test="header"
  >
    <div class="flex w-full items-center space-x-8 px-5 2xl:container 2xl:mx-auto  2xl:p-0">
      <div class="flex items-center space-x-5">
        <a
          :href="pwUrl"
          title="Zurück zu Professional works"
        >
          <img
            :src="logoUrl"
            alt="Firmenlogo"
            class="h-auto max-h-14 w-full max-w-sm object-contain"
          >
        </a>

        <router-link
          to="/"
          class="mb-1.5 hidden font-semibold hover:text-blue-500 md:block xl:text-lg"
        >
          Vorgangsmanager
        </router-link>
      </div>

      <HeaderMenu />
    </div>
  </header>
</template>

<script setup lang="ts">
import HeaderMenu from '@/components/header/HeaderMenu.vue';
import getPwUrl from '@/utils/getPwUrl';

const logoUrl = '/api/users/me/logo';
const pwUrl = getPwUrl();
</script>
