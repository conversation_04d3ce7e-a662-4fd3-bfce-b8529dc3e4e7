<template>
  <DsAccordion
    v-model="model"
    trigger-full-width
    class="rounded-md border border-gray-200 p-2"
  >
    <template #trigger="{open}">
      <div class="align-center flex justify-between">
        <span class="flex items-center font-semibold">
          {{ title }}
        </span>
        <DsButton
          class="ml-auto"
          size="sm"
          variant="clear"
          :icon="`${open ? 'chevron-up' : 'chevron-down'}`"
        />
      </div>
    </template>
    <template #default>
      <div class="space-y-3 p-1">
        <slot />
      </div>
    </template>
  </DsAccordion>
</template>

<script setup lang="ts">
import { DsAccordion, DsButton } from '@demvsystems/design-components';

const model = defineModel<boolean>();

defineProps<{
  title: string,
}>();
</script>
