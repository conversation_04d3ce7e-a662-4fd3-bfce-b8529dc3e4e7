<template>
  <DsDropdown
    class="focus:outline-none"
    button-class="w-full focus:outline-none"
    :items="loading ? [] : filteredMenuItems"
    keep-open-on-select
    :disabled="disabled"
    @select="handleSelect"
    @open="emitOpen"
  >
    <template #button>
      <slot name="button" />
    </template>

    <template #extra>
      <div class="border-b border-gray-200 p-px">
        <DsInput
          v-model="query"
          icon="search"
          placeholder="Suchen..."
          autofocus
          immediate
          inline
          @blur="nextTick(() => $event.target.focus())"
        />
      </div>
      <div
        v-if="loading"
        class="my-6 w-auto self-center"
      >
        <DsIcon
          name="spinner-third"
          variant="duotone"
          size="2x"
          spin
        />
      </div>
    </template>

    <template
      #item="{item}"
    >
      <div
        v-if="!loading"
        class="flex space-x-2 p-2 text-center"
      >
        <div class="w-4">
          <DsIcon
            v-if="item?.selected"
            name="check"
            class="text-green-700"
          />
        </div>
        <slot
          name="item"
          :item="item.data"
        />
      </div>
    </template>
  </DsDropdown>

  <slot
    name="selected-items"
    :items="selectedItems"
  />
</template>

<script setup lang="ts">
import { DsDropdown, DsIcon, DsInput } from '@demvsystems/design-components';
import Fuse from 'fuse.js';
import {
  computed,
  nextTick,
  ref,
} from 'vue';

import { type ResourceObject } from '@/types/jsonapi';

interface SelectMenuItem {
  data: unknown;
  selected: boolean;
}

const props = defineProps<{
  items: ResourceObject[],
  fuseOptions: Fuse.IFuseOptions<SelectMenuItem>
  selectedItems: ResourceObject[],
  loading: boolean,
  disabled?: boolean,
}>();

const emit = defineEmits(['open', 'select', 'deselect']);

function isItemSelected(item: ResourceObject): boolean {
  return props.selectedItems.some((i) => i.id === item.id);
}

const menuItems = computed(() => props.items.map((item) => ({
  data: item,
  selected: isItemSelected(item),
})));
const query = ref('');
const fuse = computed(() => new Fuse(menuItems.value, props.fuseOptions));

const filteredMenuItems = computed<SelectMenuItem[]>(
  () => query.value === ''
    ? menuItems.value // fuse finds no results if query is empty
    : fuse.value.search(query.value).map((result) => result.item),
);

function handleSelect(filteredIndex: number) {
  const menuItemInFiltered = filteredMenuItems.value[filteredIndex];

  if (!menuItemInFiltered) {
    return;
  }

  menuItemInFiltered.selected = !menuItemInFiltered.selected;

  emit(menuItemInFiltered.selected ? 'select' : 'deselect', menuItemInFiltered.data);
}

function emitOpen() {
  query.value = '';
  emit('open');
}
</script>
