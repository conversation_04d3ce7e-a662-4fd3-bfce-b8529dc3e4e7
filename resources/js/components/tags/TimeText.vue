<template>
  <time :title="format(date, onlyDate ? 'PPPP' : 'PPPPpp', {locale: de})">
    {{ label }}
  </time>
</template>

<script setup lang="ts">
import { format, parseISO } from 'date-fns';
import { de } from 'date-fns/locale';
import { computed } from 'vue';

const props = defineProps<{
  value: string,
  onlyDate?: boolean,
}>();

const date = computed(() => parseISO(props.value));

const label = computed(() =>
  format(date.value, props.onlyDate ? 'PP' : 'PPpp', { locale: de }),
);
</script>
