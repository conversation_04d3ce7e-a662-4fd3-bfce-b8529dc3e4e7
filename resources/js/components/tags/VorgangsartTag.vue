<template>
  <Tag
    :icon="icon"
    title="Vorgangsart"
  >
    {{ title }}
  </Tag>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import Tag from '@/components/tags/Tag.vue';
import { Vorgangsart } from '@/store/resources/types';

const props = defineProps<{
  art: Vorgangsart,
}>();

const title = computed(() => {
  switch (props.art) {
    case Vorgangsart.KorrespondenzBrief:
    case Vorgangsart.KorrespondenzEmail:
      return 'Korrespondenz';
    case Vorgangsart.Aufgabe:
      return 'Aufgabe';
    case Vorgangsart.Dunkelverarbeitet:
      return 'Aufgabe (dunkelverarbeitet)';
    default:
      return 'Vorgangsgruppe';
  }
});

const icon = computed(() => {
  switch (props.art) {
    case Vorgangsart.KorrespondenzBrief:
    case Vorgangsart.KorrespondenzEmail:
      return 'comments-alt';
    case Vorgangsart.Aufgabe:
    case Vorgangsart.Dunkelverarbeitet:
      return 'tasks';
    default:
      return 'folder-tree';
  }
});
</script>
