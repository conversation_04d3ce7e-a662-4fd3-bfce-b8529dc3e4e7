<template>
  <span
    v-if="user?.attributes !== undefined"
    class="text-left"
    :class="{'inline-flex': !inline}"
    data-test="user-tag"
  >
    <span class="mr-1.5 h-6 shrink-0">
      <img
        v-if="user.links?.avatar !== undefined"
        :src="user.links.avatar"
        class="inline size-5 rounded-full bg-gray-100 align-text-bottom"
        alt="Benutzeravatar"
      >
    </span>

    <span v-text="user.attributes.name" />
  </span>
</template>

<script setup lang="ts">
import type { UserResource } from '@/store/resources/types';

defineProps<{
  user?: UserResource,
  inline?: boolean,
}>();
</script>
