<template>
  <time :title="format(date, props.onlyDate ? 'PPPP' : 'PPPPpp', {locale: de})">
    {{ label }}
  </time>
</template>

<script setup lang="ts">
import { format, formatRelative, parseISO } from 'date-fns';
import { de } from 'date-fns/locale';
import { computed } from 'vue';

import { useUserSettingsStore } from '@/pages/settings/stores/userSettingsStore';
import { DateFormat } from '@/pages/settings/types';

const props = defineProps<{
  value: string,
  onlyDate?: boolean,
  capitalize?: boolean,
}>();

const now = new Date();
const date = computed(() => parseISO(props.value));

// https://date-fns.org/docs/I18n-Contribution-Guide#formatrelative
// https://github.com/date-fns/date-fns/blob/master/src/locale/en-US/_lib/formatRelative/index.js
// https://github.com/date-fns/date-fns/issues/1218
// https://stackoverflow.com/questions/47244216/how-to-customize-date-fnss-formatrelative
const customFormatRelativeLocale = {
  lastWeek: "'letzten' eeee 'um' HH:mm",
  yesterday: "'gestern um' HH:mm",
  today: "'heute um' HH:mm",
  tomorrow: "'morgen um' HH:mm",
  nextWeek: "eeee 'um' HH:mm",
  other: "P 'um' HH:mm",
};

const userSettingsStore = useUserSettingsStore();

function getFormattedDateWithTime() {
  if (userSettingsStore.dateFormat === DateFormat.Einheitlich) {
    return format(date.value, customFormatRelativeLocale.other, { locale: de });
  }

  return formatRelative(date.value, now, {
    locale: {
      ...de,
      formatRelative: (token: keyof typeof customFormatRelativeLocale) => (
        customFormatRelativeLocale[token]
      ),
    },
  });
}

const label = computed(() => {
  let withTime = getFormattedDateWithTime();

  // workaround for https://github.com/date-fns/date-fns/issues/1218
  if (props.onlyDate) {
    withTime = withTime.replace(/ um \d{2}:\d{2}/, '');
  }

  if (props.capitalize) {
    withTime = withTime.charAt(0).toUpperCase() + withTime.slice(1);
  }

  return withTime;
});
</script>
