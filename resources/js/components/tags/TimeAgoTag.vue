<template>
  <Tag
    icon="clock"
    :status="status"
  >
    <time :title="format(date, 'PPPP', {locale: de})">
      {{ label }}
    </time>
  </Tag>
</template>

<script setup lang="ts">
import {
  differenceInDays, differenceInMonths, format, formatRelative, isPast, parseISO,
} from 'date-fns';
import { de } from 'date-fns/locale';
import { computed } from 'vue';

import { useUserSettingsStore } from '@/pages/settings/stores/userSettingsStore';
import { DateFormat } from '@/pages/settings/types';

import Tag from './Tag.vue';

const customRelative = {
  lastWeek: "'letzten' eeee",
  yesterday: "'gestern'",
  today: "'heute'",
  tomorrow: "'morgen'",
  nextWeek: 'eeee',
  other: 'd. LLLL',
};
const locale = {
  ...de,
  formatRelative: (token: keyof typeof customRelative) => customRelative[token],
};

const props = defineProps<{
  value: string,
  noUrgencyStyling?: boolean,
}>();

const userSettingsStore = useUserSettingsStore();

const now = new Date();

const date = computed(() => parseISO(props.value));

const status = computed(() => {
  if (props.noUrgencyStyling) {
    return 'default';
  }

  if (isPast(date.value)) {
    return 'urgent';
  }

  if (Math.abs(differenceInDays(now, date.value)) < 5) {
    return 'medium';
  }

  return 'highlight';
});

const label = computed(() => {
  if (userSettingsStore.dateFormat === DateFormat.Dynamisch) {
    if (Math.abs(differenceInDays(now, date.value)) <= 6) {
      return formatRelative(date.value, now, { locale });
    }

    if (Math.abs(differenceInMonths(now, date.value)) <= 6) {
      return format(date.value, customRelative.other, { locale });
    }
  }

  return format(date.value, 'P', { locale });
});
</script>
