<template>
  <span class="items-center">
    <DsIcon
      :name="icon"
      :class="{
        'text-gray-300': status === 'default',
        'text-gray-700': status === 'highlight',
        'text-red-500': status === 'urgent',
        'text-orange-500': status === 'medium',
      }"
      fixed-width
    />

    <span
      class="ml-1 truncate"
      :class="{
        'text-gray-500': status === 'default',
        'text-gray-800': status === 'highlight',
        'text-red-700': status === 'urgent',
        'text-orange-700': status === 'medium',
        'font-semibold': status === 'urgent' || status === 'medium' || status === 'highlight',
      }"
    >
      <slot />
    </span>
  </span>
</template>

<script setup lang="ts">
import { DsIcon } from '@demvsystems/design-components';

withDefaults(defineProps<{
  icon: string,
  status?: 'urgent' | 'medium' | 'default' | 'highlight',
}>(), {
  status: 'default',
});
</script>
