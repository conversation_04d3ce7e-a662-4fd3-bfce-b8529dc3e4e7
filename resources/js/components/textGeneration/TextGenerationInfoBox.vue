<template>
  <DsPopover
    :variant="PopoverVariant.White"
    :show="isPopoverOpen"
    placement="auto"
    @click-outside="closePopover"
  >
    <div ref="button">
      <button
        class="w-full py-1 pl-4 text-left text-sm text-gray-800 hover:bg-gray-100 focus:outline-none"
        @click="togglePopover"
      >
        <DsIcon
          name="info-circle"
        />
        Hilfe
      </button>
    </div>

    <template #content>
      <div class="space-y-2 p-2 text-sm">
        <p class="font-bold">
          Wie wird der KI-Assistent verwendet?
        </p>
        <img
          :src="imgUrl"
          alt="Beispiel Schadensmeldung"
        >
        <p>
          Für sinnvolle KI-generierte Texte ist ein klarer Kontext entscheidend.
          Dieser ergibt sich aus vorhandenen Daten (z. B. Vorgangstyp, Sparte, Korrespondenzen)
          und Ihren Eingaben im Editor, die als Anweisungen dienen.
        </p>
        <p>
          Je weniger Kontext vorhanden ist oder wenn der gewünschte Text davon abwei<PERSON>
          (z. B. bei „Antrag einreichen“ eine Kündigung formulieren),
          müssen die Anweisungen präziser sein.
          Ist der Kontext klar und soll der Text nur ergänzt werden, genügen oft Stichworte.
        </p>
      </div>
    </template>
  </DsPopover>
</template>

<script setup lang="ts">
import { DsIcon, DsPopover, PopoverVariant } from '@demvsystems/design-components';
import { ref } from 'vue';

import imgUrl from '../../../img/ai_workflow.gif';

const isPopoverOpen = ref(false);

function togglePopover() {
  isPopoverOpen.value = !isPopoverOpen.value;
}

function closePopover() {
  isPopoverOpen.value = false;
}
</script>
