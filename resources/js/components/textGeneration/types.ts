export enum TextGenerationAction {
  Erzeugen = 'erzeugen',
  Kuerzen = 'kuerzen',
  Ausschmuecken = 'ausschmuecken',
  Formeller = 'formeller',
  <PERSON><PERSON> = 'lockerer',
  Werblicher = 'werblicher',
  Bulletpoints = 'bulletpoints',
}

export enum TextGenerationLocation {
  VorgangAnlegen = 'vorgang_anlegen',
  KampagneErstellen = 'kampagne_erstellen',
  NachrichtVerfassen = 'nachricht_verfassen',
}

export type DropdownOptions = {
  label: string,
  group: string,
  action: TextGenerationAction,
};
