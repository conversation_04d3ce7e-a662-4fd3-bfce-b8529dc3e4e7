<template>
  <DsPopover
    :show="!showHasSeenKiAssistant"
    placement="top"
  >
    <DsDropdown
      :items="options"
      group-key="group"
      placement="top"
      :width="210"
      class="-mt-px"
      button-class="px-2 h-6 text-sm border-transparent bg-blue-100 hover:bg-blue-200 active:bg-blue-100 text-blue-700 hover:text-blue-800 focus:border-blue-500 focus:shadow-outline-blue font-medium rounded focus:outline-none transition ease-out duration-100 grow min-w-0 border"
      :item-class="isEditorEmpty ? 'text-gray-500 hover:bg-transparent cursor-default' : 'text-gray-800'"
      @select="handleSelect"
    >
      <template #button>
        <DsIcon name="sparkles" />
        KI Assistent
      </template>

      <template #extra>
        <TextGenerationInfoBox />
      </template>

      <template #item="{item}">
        {{ item.label }}
      </template>
    </DsDropdown>
    <template #content>
      <div class="flex items-center py-1 text-xs">
        <p>
          Unser KI-Assistent jetzt mit neuen Funktionen
        </p>
        <DsIcon
          name="xmark"
          class="ml-1 size-4 cursor-pointer"
          @click="closeHasSeenKiAssistant"
        />
      </div>
    </template>
  </DsPopover>
</template>

<script setup lang="ts">
import { DsDropdown, DsIcon, DsPopover } from '@demvsystems/design-components';
import { useLocalStorage } from '@vueuse/core';
import { computed } from 'vue';

import { EmpfaengerSuggestionContextType } from '@/components/form/useEmpfaengerSuggestions';
import TextGenerationInfoBox from '@/components/textGeneration/TextGenerationInfoBox.vue';
import {
  DropdownOptions,
  TextGenerationAction,
  TextGenerationLocation,
} from '@/components/textGeneration/types';
import { useTextGeneration } from '@/composables/useTextGeneration';
import { Anrede } from '@/pages/vorgangAnlegen/types';
import { Versandart } from '@/pages/vorlagen/types';

const props = withDefaults(defineProps<{
  location: TextGenerationLocation;
  content: string;
  anrede: Anrede;
  versandart?: Versandart;
  vorgangstypId?: string | null;
  sparteId?: string | null;
  vorgangId?: string;
  empfaengertyp?: EmpfaengerSuggestionContextType;
  empfaengerMail?: string;
  kampagneIdeeId?: string;
}>(), {
  versandart: Versandart.Mail,
  vorgangstypId: null,
  sparteId: null,
  vorgangId: undefined,
  empfaengertyp: undefined,
  empfaengerMail: undefined,
  kampagneIdeeId: undefined,
});

const emit = defineEmits<{
  (event: 'update:content', content: string): void,
}>();

const { generateText } = useTextGeneration();

const showHasSeenKiAssistant = useLocalStorage<boolean>('hasSeenKiAssistant', false);

function closeHasSeenKiAssistant() {
  showHasSeenKiAssistant.value = true;
}

const isEditorEmpty = computed(() => props.content === '');

const options = <DropdownOptions[]>[
  {
    label: 'Neuen Text generieren',
    group: 'Textgenerierung',
    action: TextGenerationAction.Erzeugen,
  },
  {
    label: 'Kürzen',
    group: 'Textformulierung',
    action: TextGenerationAction.Kuerzen,
  },
  {
    label: 'Ausschmücken',
    group: 'Textformulierung',
    action: TextGenerationAction.Ausschmuecken,
  },
  {
    label: 'Formeller schreiben',
    group: 'Textformulierung',
    action: TextGenerationAction.Formeller,
  },
  {
    label: 'Lockerer schreiben',
    group: 'Textformulierung',
    action: TextGenerationAction.Lockerer,
  },
  {
    label: 'Werblicher gestalten',
    group: 'Textformulierung',
    action: TextGenerationAction.Werblicher,
  },
  {
    label: 'In Bulletpoints umwandeln',
    group: 'Textformulierung',
    action: TextGenerationAction.Bulletpoints,
  },
];

async function handleSelect(index: number) {
  if (isEditorEmpty.value) {
    return;
  }

  const action = options[index]?.action;

  if (action === undefined) {
    throw Error('No action defined for selected option');
  }

  const newContent = await generateText(
    action,
    props.location,
    props.content,
    props.anrede,
    props.versandart,
    props.vorgangstypId,
    props.sparteId,
    props.vorgangId,
    props.empfaengertyp,
    props.empfaengerMail,
    props.kampagneIdeeId,
  );

  emit('update:content', newContent);
}
</script>
