<template>
  <transition-group
    name="list"
    tag="ul"
    class="space-y-3"
    data-test="toast-list"
  >
    <li
      v-for="message in reversedQueue"
      :key="message.id"
      class="duration-350 opacity-95 shadow-md transition ease-in-out"
    >
      <DsAlert
        :type="message.type"
        :label="message.label"
        data-test="toast-list__toast"
      >
        {{ message.content }}
      </DsAlert>
    </li>
  </transition-group>
</template>

<script setup lang="ts">
import { DsAlert } from '@demvsystems/design-components';
import { EventType } from 'mitt';
import { computed, ref } from 'vue';

import { eventBus } from '@/store/resources/store';

import registry from './registry';
import { Toast } from './types';

const queue = ref<Toast[]>([]);
let nextId = 0;

const removeToast = (idToBeRemoved: number) => {
  queue.value = queue.value.filter(({ id }) => id !== idToBeRemoved);
};

eventBus.on('*', (type: EventType, event: unknown) => {
  const toast = registry[type.toString()];

  if (toast === undefined) {
    return;
  }

  if (typeof event === 'string' && event != '') {
    toast.content = event;
  }

  const id = nextId++;

  queue.value.push({
    ...toast,
    id,
  });

  setTimeout(() => removeToast(id), 4000);
});

const reversedQueue = computed(
  () => queue.value.slice(0, 3).reverse(), // show max 3 elements
);
</script>

<style scoped>
.list-enter-from {
  opacity: 0;
  transform: translateY(-2.5rem);
}

.list-leave-to {
  opacity: 0;
}
</style>
