<template>
  <NodeViewWrapper
    as="span"
    :class="[
      'inline-flex h-5 cursor-pointer select-none items-center rounded bg-blue-100 px-1 font-semibold text-blue-800 hover:bg-blue-200',
      {'h-full rounded-none bg-blue-500 text-white': selected},
    ]"
    :data-name="node.attrs.name"
    @dblclick="props.editor?.commands.setNodeSelection(props.getPos())"
  >
    <ElPopover
      :width="300"
      trigger="click"
      placement="bottom"
    >
      <template #reference>
        <div>
          <span
            :class="{
              'text-blue-500': !selected,
              'text-blue-200': selected,
            }"
          >#</span>
          <span>{{ node.attrs.name }}</span>
        </div>
      </template>
      <template #default>
        <div class="text-sm">
          <span
            class="block leading-tight text-black"
            v-text="tag?.attributes.description"
          />
          <span
            v-if="tag?.attributes.example?.length"
            class="block text-gray-700"
            v-html="tag?.attributes.example"
          />
        </div>
      </template>
    </ElPopover>
  </NodeViewWrapper>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/core';
import { NodeViewWrapper } from '@tiptap/vue-3';
import { ElPopover } from 'element-plus';
import { computed } from 'vue';

import { useTagStore } from './tagStore';

const props = defineProps<{
  editor?: Editor;
  node: {
    attrs: {
      name: string;
    };
  };
  selected?: boolean;
  getPos: () => number;
}>();

const tagsStore = useTagStore();

const tag = computed(() => {
  return tagsStore.tags.find((t) => t.attributes.tag === props.node.attrs.name);
});
</script>
