<template>
  <div>
    <DsDropdownBase
      ref="dropdown"
      :items="itemTags"
      :width="350"
      preselect-first-value
      @select="selectItem"
    >
      <template #item="{item}">
        <div class="px-2 text-xs">
          <span
            class="line-clamp-2 block font-mono font-bold text-blue-700"
            v-text="item.attributes.tag"
          />
          <span
            class="line-clamp-3 block leading-tight"
            v-text="item.attributes.description"
          />
          <span
            v-if="item.attributes.example.length > 0"
            class="line-clamp-2 block text-gray-700"
            v-html="item.attributes.example"
          />
        </div>
      </template>

      <template #no-items>
        <div class="p-2 text-sm text-gray-500">
          Keine <PERSON>halter gefunden
        </div>
      </template>
    </DsDropdownBase>
  </div>
</template>

<script setup lang="ts">
import { DsDropdownBase } from '@demvsystems/design-components';
import type { SuggestionProps } from '@tiptap/suggestion';
import { ref, computed } from 'vue';

import { useTagStore } from './tagStore';

const props = defineProps<{
  command: SuggestionProps['command'];
  query: SuggestionProps['query'];
}>();

const tagsStore = useTagStore();

const itemTags = computed(() =>
  tagsStore.tags.filter((tag) => {
    const tagName = tag.attributes.tag.toLowerCase();
    const query = props.query.toLowerCase();

    if (query === '') {
      return true;
    }

    if (tagName.includes(query)) {
      return true;
    }

    const tagSegments = tagName.split(':');
    const querySegments = query.split(':');

    if (querySegments.length > tagSegments.length) {
      return false;
    }

    return querySegments.every((querySegment, index) => {
      const tagSegment = tagSegments[index];

      return tagSegment && tagSegment.startsWith(querySegment);
    });
  }),
);

const selectItem = (index: number) => {
  const tag = itemTags.value[index];
  if (tag) {
    props.command({ name: tag.attributes.tag });
  }
};

const dropdown = ref<{
  selectNext: () => void;
  selectPrevious: () => void;
  emitSelectedIndex: () => void;
  select: (index: number) => void;
} | null>(null);

const onKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'ArrowDown') {
    dropdown.value?.selectNext();

    return true;
  }

  if (e.key === 'ArrowUp') {
    dropdown.value?.selectPrevious();

    return true;
  }

  if (e.key === 'Enter') {
    dropdown.value?.emitSelectedIndex();

    return true;
  }

  dropdown.value?.select(0);

  return false;
};

defineExpose({ onKeyDown });
</script>
