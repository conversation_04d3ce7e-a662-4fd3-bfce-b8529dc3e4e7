import { Node } from '@tiptap/core';
import { Image } from '@tiptap/extension-image';
import { Paragraph } from '@tiptap/extension-paragraph';

import TagList from '@/components/extensions/tags/index';

export function getEditorExtensions(isInline = false): Node[] {
  const extensions = [
    TagList,
  ];

  if (!isInline) {
    extensions.push(
      Paragraph.configure({
        HTMLAttributes: {
          style: 'min-height: 1.5em; font-family: Arial, sans-serif; font-size: 12pt; color: #000;',
        },
      }),
      Image.configure({
        inline: true,
        allowBase64: true,
      }),
    );
  }

  return extensions;
}
