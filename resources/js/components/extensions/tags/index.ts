import { mergeAttributes, Node } from '@tiptap/core';
import Suggestion, { SuggestionKeyDownProps } from '@tiptap/suggestion';
import { VueRenderer, VueNodeViewRenderer } from '@tiptap/vue-3';
import tippy, { Instance } from 'tippy.js';

import TagList from './TagList.vue';
import TagNodeView from './TagNodeView.vue';

const TAG_CHAR = '#';
const TAG_NAME = 'tag';

export default Node.create({
  name: TAG_NAME,
  group: 'inline',
  inline: true,
  selectable: false,
  atom: true,

  addAttributes() {
    return {
      name: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-name'),
        renderHTML: (attributes) => {
          if (!attributes.name) {
            return {};
          }

          return {
            'data-name': attributes.name,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: `span[data-type="${TAG_NAME}"]`,
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['span', mergeAttributes({ 'data-type': TAG_NAME }, HTMLAttributes)];
  },

  addNodeView() {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    return VueNodeViewRenderer(TagNodeView);
  },

  renderText({ node }) {
    return `${node.attrs.name}`;
  },

  addKeyboardShortcuts() {
    return {
      Backspace: () => this.editor.commands.command(({ tr, state }) => {
        let isMention = false;
        const { selection } = state;
        const { empty, anchor } = selection;

        if (!empty) {
          return false;
        }

        state.doc.nodesBetween(anchor - 1, anchor, (node, pos) => {
          if (node.type.name === this.name) {
            isMention = true;

            const content = `${node.attrs.name}` || '';
            const lastDelimiter = content.lastIndexOf(':');
            const prefix = content.substring(0, lastDelimiter) + (lastDelimiter > 0 ? ':' : '');

            tr.insertText(`${TAG_CHAR}${prefix}`, pos, pos + node.nodeSize);

            return false;
          }

          return undefined;
        });

        return isMention;
      }),
    };
  },

  addProseMirrorPlugins() {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return [
      Suggestion({
        editor: this.editor,
        char: TAG_CHAR,

        command({ editor, range, props }) {
          editor
            .chain()
            .focus()
            .insertContentAt(range, [
              {
                type: 'tag',
                attrs: props,
              },
            ])
            .run();
        },

        allow: ({ editor, range }) => (
          editor.can().insertContentAt(range, { type: TAG_NAME })
        ),

        render: () => {
          let renderer: VueRenderer;
          let popup: Instance;

          return {
            onStart(props) {
              renderer = new VueRenderer(TagList, {
                editor: props.editor,
                props,
              });

              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              popup = tippy('body', {
                getReferenceClientRect: props.clientRect,
                content: renderer.element,
                showOnCreate: true,
                interactive: true,
                trigger: 'manual',
                placement: 'bottom-start',
              })[0]!;
            },

            onUpdate(props) {
              renderer.updateProps(props);
              popup.setProps({
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                getReferenceClientRect: props.clientRect,
              });
            },

            onKeyDown(props: SuggestionKeyDownProps) {
              return !!(renderer.ref as {
                onKeyDown: (e: KeyboardEvent) => boolean,
              })?.onKeyDown(props.event);
            },

            onExit() {
              popup.destroy();
              renderer.destroy();
            },
          };
        },
      }),
    ];
  },
});
