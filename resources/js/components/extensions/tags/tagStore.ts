import { captureException } from '@sentry/vue';
import { defineStore } from 'pinia';
import { onMounted, ref } from 'vue';

import { get } from '@/api';
import { ResourceObject } from '@/types/jsonapi';

export type TagDescription = ResourceObject<'tagDescriptionElement', {
  tag: string;
  description: string;
  example: string;
}>;

export const useTagStore = defineStore('tags', () => {
  const tags = ref<TagDescription[]>([]);

  const fetchTags = async (): Promise<void> => {
    try {
      const { data } = await get<TagDescription[]>('tags');

      tags.value = (data.data ?? []).sort((a, b) => (
        a.attributes.tag.localeCompare(b.attributes.tag, 'de-DE')
      ));
    } catch (e) {
      captureException('Error while fetching tags. store/tags/index.ts');
    }
  };

  onMounted(async () => {
    if (tags.value.length === 0) {
      await fetchTags();
    }
  });

  return {
    tags,
    fetchTags,
  };
});
