import {
  createRouter,
  createWebHistory,
  RouteLocationNormalized,
  RouteRecordRaw,
} from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'vorgaenge.index',
    meta: {
      title: 'Vorgänge',
    },
    component: () => import('./pages/vorgaenge/index.vue'),
    children: [
      {
        path: ':firmaId/:vorgangsnummer',
        name: 'vorgaenge.show',
        meta: {
          title: (route?: RouteLocationNormalized) => (
            `#${route?.params.vorgangsnummer}`
          ),
        },
        component: () => import('./pages/vorgang/index.vue'),
        props: (route) => ({
          firmaId: route.params.firmaId,
          vorgangsnummer: route.params.vorgangsnummer,
          isVorgangNew: !!route.query.isVorgangNew,
        }),
      },
    ],
  },
  {
    path: '/vorlagen',
    name: 'vorlagen.index',
    meta: {
      title: 'Vorlagen',
    },
    component: () => import('./pages/vorlagen/index.vue'),
    children: [
      {
        path: ':id?',
        props: true,
        name: 'vorlagen.show',
        component: () => import('./pages/vorlagen/pages/vorlageShow.vue'),
      },
    ],
  },
  {
    path: '/kampagnen',
    name: 'kampagnen.index',
    meta: {
      title:'Kampagnen',
    },
    component: () => import('@/pages/kampagnen/index.vue'),
    children: [
      {
        path: '',
        props: true,
        name: 'kampagnen.list',
        component: () => import('@/pages/kampagnen/pages/kampagneUebersicht.vue'),
      },
      {
        path: ':id',
        props: true,
        name: 'kampagnen.show',
        component: () => import('@/pages/kampagnen/pages/kampagneEinzelseite.vue'),
      },
      {
        path: ':id/bearbeiten',
        props: true,
        name: 'kampagnen.edit',
        component: () => import('@/pages/kampagnen/pages/kampagneCreate.vue'),
        children: [
          {
            path: '',
            props: true,
            name: 'kampagnen.edit.basis',
            component: () => import('@/pages/kampagnen/pages/createForm/BasisKonfiguration.vue'),
          },
          {
            path: 'empfaenger',
            props: true,
            name: 'kampagnen.edit.recipients',
            component: () => import('@/pages/kampagnen/pages/createForm/Empfaenger.vue'),
          },
          {
            path: 'inhalt',
            name: 'kampagnen.edit.content',
            component: () => import('@/pages/kampagnen/pages/createForm/Inhalt.vue'),
            props: (route) => ({
              activeTab: route.query.tab,
            }),
          },
          {
            path: 'zusammenfassung',
            props: true,
            name: 'kampagnen.edit.summary',
            component: () => import('@/pages/kampagnen/pages/createForm/Zusammenfassung.vue'),
          },
        ],
      },
      {
        path: 'erstellen',
        props: true,
        name: 'kampagnen.create',
        meta: {
          title:'Neue Kampagne',
        },
        component: () => import('@/pages/kampagnen/pages/kampagneCreate.vue'),
        children: [
          {
            path: '',
            props: true,
            name: 'kampagnen.create.basis',
            meta: {
              title: 'Neue Kampagne',
            },
            component: () => import('@/pages/kampagnen/pages/createForm/BasisKonfiguration.vue'),
          },
        ],
      },
    ],
  },
  {
    path: '/einstellungen',
    name: 'settings.index',
    meta: {
      title:'Einstellungen',
    },
    component: () => import('@/pages/settings/index.vue'),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

router.beforeEach((to, from, next) => {
  if (to.path === from.path) {
    next();

    return;
  }

  document.title = typeof to.meta.title === 'function'
    ? `${to.meta.title(to)} | Vorgangsmanager`
    : `${to.meta.title} | Vorgangsmanager`;

  next();
});

export default router;

declare module 'vue-router' {
  interface RouteMeta {
    title: ((route?: RouteLocationNormalized) => string) | string,
  }
}
