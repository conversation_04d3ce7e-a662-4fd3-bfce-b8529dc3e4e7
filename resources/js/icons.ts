import { library } from '@fortawesome/fontawesome-svg-core';
import { faSpinner as fadSpinner } from '@fortawesome/pro-duotone-svg-icons';
import {
  faAngleUp as farAngleUp,
  faAt as farAt,
  faCircleCheck as farCircleCheck,
  faCircleExclamation as farExclamation,
  faCircleInfo as farCircleInfo,
  faCommentsAlt as farCommentsAlt,
  faEnvelope as farEnvelope,
  faEnvelopesBulk as farEnvelopesBulk,
  faFolderTree as farFolderTree,
  faInbox as farInbox,
  faInputText as farInputText,
  faMinus as farMinus,
  faPen as farPen,
  faMemoPad as farMemoPad,
  faPlus as farPlus,
  faQuestion as farQuestion,
  faSquarePlus as farSquarePlus,
  faStar as farStar,
  faTasks as farTasks,
  faTrash as farTrash,
  faTriangleExclamation as farTriangleExclamation,
} from '@fortawesome/pro-regular-svg-icons';
import {
  faAlarmClock as fasAlarmClock,
  faAngleRight as fasAngleRight,
  faAngleUp as fasAngleUp,
  faArrowLeft as fasArrowLeft,
  faArrowRight as fasArrowRight,
  faAt as fasAt,
  faBars as fasBars,
  faBuilding as fasBuilding,
  faBuildings as fasBuildings,
  faCalendarDay as fasCalendarDay,
  faCheck as fasCheck,
  faChevronDown as fasChevronDown,
  faChevronUp as fasChevronUp,
  faCircleInfo as fasCircleInfo,
  faClock as fasClock,
  faCog as fasCog,
  faComment as fasComment,
  faCommentAltEdit as fasCommentAltEdit,
  faCommentsAlt as fasCommentsAlt,
  faCopy as fasCopy,
  faDownload as fasDownload,
  faEdit as fasEdit,
  faEllipsisVertical as fasEllipsisVertical,
  faEnvelope as fasEnvelope,
  faEnvelopesBulk as fasEnvelopesBulk,
  faEraser as fasEraser,
  faExclamationTriangle as fasExclamationTriangle,
  faExternalLinkAlt as fasExternalLinkAlt,
  faEye as fasEye,
  faFastForward as fasFastForward,
  faFileAlt as fasFileAlt,
  faFilePlus as fasFilePlus,
  faFiles as fasFiles,
  faFileTimes as fasFileTimes,
  faFilter as fasFilter,
  faFlag as fasFlag,
  faFolderOpen as fasFolderOpen,
  faFolderTree as fasFolderTree,
  faHashtag as fasHashtag,
  faHome as fasHome,
  faInbox as fasInbox,
  faLink as fasLink,
  faListCheck as fasListCheck,
  faLock as fasLock,
  faPaperPlane as fasPaperPlane,
  faPen as fasPen,
  faPhone as fasPhone,
  faPlay as fasPlay,
  faPlus as fasPlus,
  faPowerOff as fasPowerOff,
  faRobotAstromech as fasRobotAstromech,
  faSave as fasSave,
  faSearch as fasSearch,
  faSliders as fasSliders,
  faSparkles as fasSparkles,
  faSpinnerThird as fasSpinnerThird,
  faSync as fasSync,
  faTasks as fasTasks,
  faTrash as fasTrash,
  faTriangleExclamation as fasTriangleExclamation,
  faUnlink as fasUnlink,
  faUpload as fasUpload,
  faUser as fasUser,
  faUserMinus as fasUserMinus,
  faUsers as fasUsers,
  faUserSlash as fasUserSlash,
  faWrench as fasWrench,
  faXmark as fasXmark,
} from '@fortawesome/pro-solid-svg-icons'; // default variant for <DsIcon>

library.add(
  fasUserMinus,
  fasBars,
  fasSpinnerThird,
  farPen,
  farPlus,
  fasPlus,
  farSquarePlus,
  farMinus,
  fasFolderTree,
  fasChevronUp,
  fasChevronDown,
  fasUpload,
  farInbox,
  farFolderTree,
  farTasks,
  fasBuildings,
  farAngleUp,
  fasSliders,
  fasListCheck,
  fasTasks,
  farCommentsAlt,
  fasTriangleExclamation,
  farTriangleExclamation,
  fasCommentsAlt,
  fasXmark,
  fasCheck,
  fasFlag,
  fadSpinner,
  farAt,
  fasAt,
  fasEnvelopesBulk,
  farEnvelopesBulk,
  fasEnvelope,
  farEnvelope,
  fasCog,
  farCircleInfo,
  farCircleCheck,
  fasDownload,
  fasEye,
  fasBuilding,
  fasUser,
  fasUsers,
  fasUserSlash,
  fasClock,
  fasHashtag,
  fasArrowRight,
  fasFilter,
  fasEraser,
  fasFastForward,
  fasFolderOpen,
  fasFileTimes,
  fasSearch,
  fasSync,
  fasAngleUp,
  fasPhone,
  fasPen,
  fasPowerOff,
  farMemoPad,
  fasHome,
  fasCircleInfo,
  fasAngleRight,
  fasAlarmClock,
  fasExclamationTriangle,
  fasEdit,
  fasSave,
  fasLink,
  fasUnlink,
  fasInbox,
  fasFileAlt,
  fasTrash,
  farTrash,
  fasExternalLinkAlt,
  fasArrowLeft,
  fasComment,
  fasPaperPlane,
  fasLock,
  fasCopy,
  fasCommentAltEdit,
  fasCalendarDay,
  farInputText,
  farExclamation,
  fasFilePlus,
  fasEllipsisVertical,
  fasFiles,
  fasRobotAstromech,
  farQuestion,
  fasPlay,
  fasSparkles,
  fasWrench,
  farStar,
);
