import { DsButton, DsPopover } from '@demvsystems/design-components';
import { mount, VueWrapper } from '@vue/test-utils';
import { describe, expect, it } from 'vitest';
import { ComponentPublicInstance } from 'vue';

import PlatzhalterInfoBox from '@/components/PlatzhalterInfoBox.vue';

describe('DetailsModal', () => {
  it('infoboxOpen toggles with a click', async () => {
    const wrapper = mount(PlatzhalterInfoBox);
    const button = wrapper.findComponent(DsButton).find('button');
    const popover = (
      wrapper.findComponent(DsPopover) as VueWrapper<ComponentPublicInstance<{ show: boolean }>>
    );

    expect(popover.props().show).toBe(false);
    await button.trigger('click');
    expect(popover.props().show).toBe(true);
    await button.trigger('click');
    expect(popover.props().show).toBe(false);
  });
});
