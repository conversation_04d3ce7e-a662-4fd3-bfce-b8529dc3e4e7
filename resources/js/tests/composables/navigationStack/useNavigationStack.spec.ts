import { beforeEach, describe, expect, it } from 'vitest';
import { ComputedRef } from 'vue';

import { StackComponent } from '@/composables/navigationStack/types';
import { useNavigationStack } from '@/composables/navigationStack/useNavigationStack';

describe('useActionsNavigationStack', () => {
  const Foo = {
    template: `
      <div>
        <span id="foo">{{ foo }}</span>
      </div>
    `,

    data() {
      return {
        foo: 'foo',
      };
    },
  };

  const Bar = {
    template: `
      <div>
        <span id="bar">{{ bar }}</span>
      </div>
    `,

    data() {
      return {
        bar: 'bar',
      };
    },
  };

  const Baz = {
    template: `
    <div>
      <span id="baz">{{ baz }}</span>
    </div>
  `,

    data() {
      return {
        bar: 'baz',
      };
    },
  };

  const StackComponentFoo: StackComponent = {
    title: 'Foo',
    componentName: 'Foo',
    component: Foo,
  };

  const StackComponentBar: StackComponent = {
    title: 'Bar',
    componentName: 'Bar',
    component: Bar,
  };

  const StackComponentBaz: StackComponent = {
    title: 'Baz',
    componentName: 'Baz',
    component: Baz,
  };

  let activeComponent: ComputedRef<StackComponent>;
  let cachedComponents: ComputedRef<string[]>;
  let popComponent: () => void;
  let pushComponent: (action: StackComponent) => void;
  let resetNavigationStack: () => void;

  beforeEach(() => {
    const nav = useNavigationStack(StackComponentFoo);
    activeComponent = nav.activeComponent;
    cachedComponents = nav.cachedComponents;
    popComponent = nav.popComponent;
    pushComponent = nav.pushComponent;
    resetNavigationStack = nav.resetNavigationStack;
  });

  it('initiate navigation with a component', () => {
    expect(activeComponent.value).toStrictEqual(StackComponentFoo);
  });

  it('push a new component', () => {
    pushComponent(StackComponentBar);
    expect(activeComponent.value).toStrictEqual(StackComponentBar);
    pushComponent(StackComponentBaz);
    expect(activeComponent.value).toStrictEqual(StackComponentBaz);
  });

  it('pop a component', () => {
    pushComponent(StackComponentBar);
    pushComponent(StackComponentBaz);
    popComponent();
    expect(activeComponent.value).toStrictEqual(StackComponentBar);
    popComponent();
    expect(activeComponent.value).toStrictEqual(StackComponentFoo);
  });

  it('doesnt pop a component if there is only one component', () => {
    popComponent();
    expect(activeComponent.value).toStrictEqual(StackComponentFoo);
  });

  it('reset navigation stack', () => {
    pushComponent(StackComponentBar);
    pushComponent(StackComponentBaz);
    resetNavigationStack();
    expect(activeComponent.value).toStrictEqual(StackComponentFoo);
  });

  it('cashed components', () => {
    pushComponent(StackComponentBar);
    pushComponent(StackComponentBaz);
    expect(cachedComponents.value).toStrictEqual(['Foo', 'Bar', 'Baz']);
  });
});
