import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { format } from 'date-fns';

import { UserSettingsResource } from './pages/settings/types';
import {
  AuthUserResource,
  BriefVorlageResource,
  ErinnerungResource,
  FirmaRessource,
  GesellschaftResource,
  KorrespondenzResource,
  KundeResource,
  MahnungResource,
  MailVorlageResource,
  SparteResource,
  TimelineEintragResource,
  UserResource,
  VerknuepfungResource,
  VertragResource,
  VorgangResource,
  VorgangTypResource,
  VorlageResource,
  KampagneResource,
} from './store/resources/types';
import { Data, Document, Error, ResourceObject } from './types/jsonapi';

const api = axios.create({
  baseURL: '/api',
});

// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
api.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

type UnpackArray<T> = T extends (infer U)[] ? U : T;

type Attributes<T> = T extends ResourceObject<string, infer A> ? A : never;

type Relationships<T> = (
  T extends ResourceObject<string, never, infer R> ? R : never
);

type ExtractType<T> = T extends ResourceObject<infer Type> ? Type : never;
type ExtractFields<T> = T extends ResourceObject ? Record<ExtractType<T>,
  (keyof Attributes<T>)[]> : never;

type Fields = Partial< & ExtractFields<UnpackArray<VorgangResource>>
& ExtractFields<UnpackArray<VorgangTypResource>>
& ExtractFields<UnpackArray<KampagneResource>>
& ExtractFields<UnpackArray<VertragResource>>
& ExtractFields<UnpackArray<FirmaRessource>>
& ExtractFields<UnpackArray<KundeResource>>
& ExtractFields<UnpackArray<GesellschaftResource>>
& ExtractFields<UnpackArray<UserResource>>
& ExtractFields<UnpackArray<AuthUserResource>>
& ExtractFields<UnpackArray<KorrespondenzResource>>
& ExtractFields<UnpackArray<MahnungResource>>
& ExtractFields<UnpackArray<ErinnerungResource>>
& ExtractFields<UnpackArray<VerknuepfungResource>>
& ExtractFields<UnpackArray<TimelineEintragResource>>
& ExtractFields<UnpackArray<SparteResource>>
& ExtractFields<UnpackArray<VorlageResource>>
& ExtractFields<UnpackArray<MailVorlageResource>>
& ExtractFields<UnpackArray<BriefVorlageResource>>
& ExtractFields<UnpackArray<UserSettingsResource>>>;

export interface JsonApiUrl<T extends Data = Data> {
  include?: (keyof Relationships<UnpackArray<T>> | string)[];
  fields?: Fields;
  filter?: {
    [x: string]: string | undefined | null,
  },
  dateFilter?: {
    fromDate?: Date,
    untilDate?: Date,
  },
  find?: {
    key: string,
    fields?: {
      [x: string]: string[],
    }
  },
  sort?: {
    name: keyof Attributes<UnpackArray<T>>;
    order?: 'asc' | 'desc';
  }[];
}

/**
 * Appends any given parameters based on the config to the given URL.
 *
 * @param url
 * @param options
 */
const addOptionsToUrl = (url: string, options?: JsonApiUrl): string => {
  if (options === undefined) {
    return url;
  }

  const extra = [];

  if (options.include !== undefined && options.include.length > 0) {
    extra.push(`include=${options.include?.join(',')}`);
  }

  if (
    options.find !== undefined
    && options.find.fields !== undefined
    && options.find.key !== undefined
    && options.find.key !== ''
  ) {
    extra.push(`find[key]=${encodeURIComponent(options.find.key)}`);

    extra.push(...Object
      .entries(options.find.fields)
      .map(([resource, fields = []]) => (
        `find[fields][${resource}]=${fields.join(',')}`
      )));
  }

  if (options.fields !== undefined) {
    extra.push(...Object
      .entries(options.fields)
      .map(([resource, fields = []]) => (
        `fields[${resource}]=${fields.join(',')}`
      )));
  }

  if (options.filter !== undefined) {
    extra.push(...Object
      .entries(options.filter)
      .map(([resource, filter = []]) => (
        `filter[${resource}]=${filter}`
      )));
  }

  if (options.dateFilter !== undefined) {
    extra.push(...Object
      .entries(options.dateFilter)
      .map(([resource, filter = []]) => {
        if (filter === undefined) {
          return '';
        }

        return `dateFilter[${resource}]=` + format((filter as Date), 'yyyy-MM-dd');
      }));
  }

  if (options.sort !== undefined) {
    extra.push('sort=' + options.sort.map((sort) =>
      `${sort.order === 'desc' ? '-' : ''}${sort.name}`,
    ).join());
  }

  return url + (url.indexOf('?') > 0 ? '&' : '?') + extra.join('&');
};

export const get = async <T extends Data>(
  url: string,
  options?: JsonApiUrl<T>,
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<Document<T>>> => (
  api.get<Document<T>>(addOptionsToUrl(url, options), config)
);

export const post = async <T extends Data>(
  url: string,
  data: unknown,
  options?: JsonApiUrl<T>,
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<Document<T>>> => (
  api.post<Document<T>>(addOptionsToUrl(url, options), data, config)
);

export const put = async <T extends Data>(
  url: string,
  data: unknown,
  options?: JsonApiUrl<T>,
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<Document<T>>> => (
  api.put<Document<T>>(addOptionsToUrl(url, options), data, config)
);

export const del = async <T extends Data>(
  url: string,
  options?: JsonApiUrl<T>,
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<Document<T>>> => (
  api.delete<Document<T>>(addOptionsToUrl(url, options), config)
);

export const isAxiosError = <T extends Data>(e: unknown): e is AxiosError<Document<T>> => (
  axios.isAxiosError(e) && e.response?.data !== undefined
);

export const extractErrors = (
  errors: Error[],
  prefix?: string,
): Record<string, string[]> => (
  errors.reduce((acc, error) => {
    let field = error.source?.parameter ?? '';
    if (prefix !== undefined) {
      field = `${prefix}.${field}`;
    }

    acc[field] = [...(acc[field] ?? []), error.detail ?? ''];

    return acc;
  }, {} as Record<string, string[]>)
);
