import axios from 'axios';
import { Ref, ref } from 'vue';

import { EmpfaengerSuggestionContextType } from '@/components/form/useEmpfaengerSuggestions';
import { TextGenerationAction, TextGenerationLocation } from '@/components/textGeneration/types';
import { Anrede } from '@/pages/vorgangAnlegen/types';
import { Versandart } from '@/pages/vorlagen/types';
import { eventBus } from '@/store/resources/store';

const isLoading = ref(false);

export function useTextGeneration(): {
  isLoading: Ref<boolean>;
  generateText: (
    action: TextGenerationAction,
    location: TextGenerationLocation,
    content: string,
    anrede: Anrede,
    versandart: Versandart,
    vorgangstypId: string | null,
    sparteId: string | null,
    vorgangId?: string,
    empfaengertyp?: EmpfaengerSuggestionContextType,
    empfaengerMail?: string,
    kampagneIdeeId?: string,
  ) => Promise<string>
} {
  async function generateText(
    action: TextGenerationAction,
    location: TextGenerationLocation,
    content: string,
    anrede: Anrede,
    versandart: Versandart,
    vorgangstypId: string | null,
    sparteId: string | null,
    vorgangId?: string,
    empfaengertyp?: EmpfaengerSuggestionContextType,
    empfaengerMail?: string,
    kampagneIdeeId?: string,
  ): Promise<string> {
    isLoading.value = true;

    try {
      const response = await axios.post<{ content: string }>('/api/textgen/', {
        action: action,
        location: location,
        content: content,
        anrede: anrede,
        versandart: versandart,
        vorgangstypId: vorgangstypId,
        sparteId: sparteId,
        vorgangId: vorgangId,
        empfaengertyp: empfaengertyp,
        empfaengerMail: empfaengerMail,
        kampagneIdeeId: kampagneIdeeId,
      });

      return response.data.content;
    } catch (e) {
      eventBus.emit('error', 'KI Assistenz konnte nicht geladen werden. Bitte versuchen Sie es erneut.');

      return content;
    } finally {
      isLoading.value = false;
    }
  }

  return {
    isLoading,
    generateText,
  };
}
