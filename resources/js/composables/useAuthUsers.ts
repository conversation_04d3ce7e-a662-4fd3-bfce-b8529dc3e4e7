import { merge } from 'lodash-es';
import { ref, Ref, watch } from 'vue';

import { get, JsonApiUrl } from '@/api';
import { AuthUserResource } from '@/store/resources/types';
import { useExternalList } from '@/utils/useExternalList';

const DEFAULT_OPTIONS: JsonApiUrl<AuthUserResource> = {
  fields: {
    authUsers: ['name', 'externalId'],
  },
  sort: [{
    name: 'lastName',
  }],
};

const options = ref<JsonApiUrl<AuthUserResource>>(DEFAULT_OPTIONS);

const {
  list: users,
  isLoading,
  update,
} = useExternalList<AuthUserResource>('users', options, { updateManually: true });

export const useAuthUsers = (
  passedOptions?: JsonApiUrl<AuthUserResource> | Ref<JsonApiUrl<AuthUserResource>>,
): {
    users: Ref<AuthUserResource[]>,
    isLoading: Ref<boolean>,
    getUser: (id: string) => Promise<AuthUserResource | null>,
    updateUsers: () => Promise<void>,
  } => {
  if (passedOptions !== undefined) {
    const passedOptionsRef = ref(passedOptions);
    watch(passedOptionsRef, (newPassedOptions) => {
      if (newPassedOptions === undefined) {
        options.value = DEFAULT_OPTIONS;
      }

      options.value = merge(DEFAULT_OPTIONS, newPassedOptions);
    }, { immediate: true });
  }

  const getUser = async (id: string): Promise<AuthUserResource | null> => {
    try {
      const response = await get<AuthUserResource>(`users/${id}`);

      return response.data.data ?? null;
    } catch {
      return null;
    }
  };

  return {
    users,
    isLoading,

    getUser,
    updateUsers: update,
  };
};
