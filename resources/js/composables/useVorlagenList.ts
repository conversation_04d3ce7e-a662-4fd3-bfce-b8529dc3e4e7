import axios, { AxiosResponse } from 'axios';
import { Ref, ref } from 'vue';

import { eventBus } from '@/store/resources/store';
import {
  BriefVorlageResource,
  KampagneBriefVorlageResource,
  KampagneMailVorlageResource,
  MailVorlageResource,
  VorlageResource,
} from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

export type VorlagenListParameters = {
  ownerId?: Ref<string | undefined>,
  vorlagenType: Ref<'vorlagenBrief' | 'vorlagenMail' | 'vorlagenKampagneMail' | 'vorlagenKampagneBrief'>
};

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const useVorlagenList = (params: VorlagenListParameters) => {
  const vorlagen = ref<VorlageResource[]>([]);
  const vorlagenBrief = ref<BriefVorlageResource[]>([]);
  const vorlagenMail = ref<MailVorlageResource[]>([]);
  const vorlagenKampagneBrief = ref<KampagneBriefVorlageResource[]>([]);
  const vorlagenKampagneMail = ref<KampagneMailVorlageResource[]>([]);
  const isLoading = ref<boolean>(false);

  const resetVorlagen = () => {
    vorlagen.value = [];
    vorlagenBrief.value = [];
    vorlagenMail.value = [];
    vorlagenKampagneBrief.value = [];
    vorlagenKampagneMail.value = [];
  };

  const loadVorlagenAndIncludedVorlagen = async (vorgangTypeId?: string) => {
    resetVorlagen();
    isLoading.value = true;

    let url: string | undefined = '/api/vorlagen';
    let isFirstIteration = true;

    do {
      let response: AxiosResponse<Document<VorlageResource[]>>;
      try {
        if (isFirstIteration) {
          url += `?include=vorlage&filter[vorlage_type]=${params.vorlagenType.value}`;
          if (vorgangTypeId !== undefined) {url += `&filter[vorlage.vorgangTypId]=${vorgangTypeId}`;}

          url += '&sort=-updatedAt';
          if (params.ownerId?.value !== undefined) {url += `&ersteller=${params.ownerId.value}`;}
        }

        response = await axios.get(url);
        isFirstIteration = false;
      } catch (e) {
        isLoading.value = false;
        eventBus.emit('error', 'Vorlagen konnten nicht geladen werden.');

        return;
      }

      const { data, included, links } = response.data;
      if (data === undefined || included === undefined) {
        isLoading.value = false;

        return;
      }

      vorlagen.value.push(...data);

      switch (params.vorlagenType.value) {
        case 'vorlagenBrief':
          vorlagenBrief.value.push(...(included as BriefVorlageResource[]));

          break;
        case 'vorlagenMail':
          vorlagenMail.value.push(...(included as MailVorlageResource[]));

          break;
        case 'vorlagenKampagneBrief':
          vorlagenKampagneBrief.value.push(...(included as KampagneBriefVorlageResource[]));

          break;
        case 'vorlagenKampagneMail':
          vorlagenKampagneMail.value.push(...(included as KampagneMailVorlageResource[]));

          break;
      }

      url = links?.next ?? undefined;
    } while (url !== undefined);

    isLoading.value = false;
  };

  return {
    vorlagen,
    vorlagenBrief,
    vorlagenMail,
    vorlagenKampagneBrief,
    vorlagenKampagneMail,
    isLoading,
    loadVorlagenAndIncludedVorlagen,
  };
};
