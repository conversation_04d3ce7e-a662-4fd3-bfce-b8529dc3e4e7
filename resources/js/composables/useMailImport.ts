import axios from 'axios';

import { eventBus } from '@/store/resources/store';

export default function useMailImport(): {
  queueMailImport: () => Promise<boolean>,
} {
  function queueMailImport(): Promise<boolean> {
    return axios.get('api/users/me/queueMailImport')
      .then(() => {
        eventBus.emit('mailImportQueued');

        return true;
      })
      .catch(() => {
        eventBus.emit('mailImportFailed');

        return false;
      });
  }

  return {
    queueMailImport,
  };
}
