import { HTMLContent } from '@tiptap/core';
import { watchDebounced } from '@vueuse/core';
import { uniqBy } from 'lodash-es';
import { computed, ComputedRef, Ref, ref, watch } from 'vue';

import { useAnsprechpartners } from '@/composables/useAnsprechpartners';
import { An<PERSON>e, BriefAbsender, BriefEmpfaenger } from '@/pages/vorgangAnlegen/types';
import { Versandart } from '@/pages/vorlagen/types';
import {
  BriefVorlageResource,
  EmailElement,
  GesellschaftResource,
  KundeResource,
  MailVorlageResource,
  SparteResource,
  toEmailElement,
  VorlageMailEmpfaenger,
  VorlageMailEmpfaengerType,
  VorlageResource,
} from '@/store/resources/types';
import { getReceiverMailAddress } from '@/utils/getUserMailAddress';

import { useVorlagenList } from './useVorlagenList';
import { useZustaendigerVermittler } from './useZustaendigerVermittler';

/* --- Exported Types --- */
export type VorlagenParameters = {
  versandart: Ref<Versandart>,
  kunde: Ref<KundeResource | undefined | null> | ComputedRef<KundeResource | undefined | null>,
  vertriebsweg: Ref<GesellschaftResource | undefined>
  | ComputedRef<GesellschaftResource | undefined>,
  sparte: Ref<SparteResource | undefined | null> | ComputedRef<SparteResource | undefined> | null,
  ownerId: Ref<string | undefined>,
  vorgangstypId: Ref<string | undefined | null> | ComputedRef<string | undefined | null>,
  mailEmpfaenger: Ref<EmailElement[]>,
  mailHasCcAndBcc: Ref<boolean>,
  mailCc: Ref<EmailElement[]>,
  mailBcc: Ref<EmailElement[]>,
  briefEmpfaengerType?: Ref<BriefEmpfaenger | undefined>,
  briefAbsender?: Ref<BriefAbsender | undefined>,
  resetEmpfaengerFields?: () => unknown,
};

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const useVorlagen = (params: VorlagenParameters) => {
  /* --- data --- */

  const selectedVorlage = ref<VorlageResource>();

  const ownerMailAddress = ref<EmailElement>();

  const anrede = ref<Anrede>(Anrede.Siezen);

  const vorlageBetreff = ref<HTMLContent>();
  const vorlageContent = ref<HTMLContent>();
  const vorlageType = computed(
    () => params.versandart.value === Versandart.Brief
      ? 'vorlagenBrief'
      : 'vorlagenMail',
  );

  /* --- composables & stores --- */
  const {
    loadAnsprechpartnerList,
    ansprechpartnerMails,
  } = useAnsprechpartners();

  const {
    loadZustaendigerVermittlerReceiverAddress,
    clearZustaendigerVermittler,
    zustaendigerVermittlerReceiverAddress,
  } = useZustaendigerVermittler();

  const {
    vorlagen,
    vorlagenBrief,
    vorlagenMail,
    loadVorlagenAndIncludedVorlagen,
  } = useVorlagenList({ ownerId: params.ownerId, vorlagenType: vorlageType });

  const selectedVorlageBrief = computed<BriefVorlageResource | undefined>(
    () => vorlagenBrief.value.find(
      (v) => v.id === selectedVorlage.value?.relationships?.vorlage?.data?.id,
    ),
  );
  const selectedVorlageMail = computed<MailVorlageResource | undefined>(
    () => vorlagenMail.value.find(
      (v) => v.id === selectedVorlage.value?.relationships?.vorlage?.data?.id,
    ),
  );

  const clientUseDuzen = computed<boolean>(() => {
    const isEmpfaengerTypeKunde = (
      selectedVorlageMail.value?.attributes.empfaengerTypes.includes(
        VorlageMailEmpfaengerType.Kunde,
      ) ?? false
    );

    if (!isEmpfaengerTypeKunde) {
      return false;
    }

    return params.kunde.value?.attributes.informal ?? false;
  });
  anrede.value = clientUseDuzen.value ? Anrede.Duzen : Anrede.Siezen;

  // user input for anrede
  const hasVorlageInformal = computed(() => {
    if (params.versandart.value === Versandart.Brief
    ) {
      return selectedVorlageBrief.value?.attributes.informalContent !== undefined;
    }

    return selectedVorlageMail.value?.attributes.informalContent !== undefined
      || selectedVorlageMail.value?.attributes.informalSubject !== undefined;
  });

  /* --- functions --- */

  const resetVorlagen = () => {
    vorlagen.value = [];
    vorlagenBrief.value = [];
    vorlagenMail.value = [];
  };

  const updateVorlagen = async () => {
    if (params.vorgangstypId.value == null) {
      return;
    }

    await loadVorlagenAndIncludedVorlagen(
      params.vorgangstypId.value,
    );
  };

  const updateContent = () => {
    if (selectedVorlage.value === undefined) {
      vorlageContent.value = undefined;
      vorlageBetreff.value = undefined;

      return;
    }

    // update fields for email.
    if (vorlageType.value === 'vorlagenMail') {
      if (selectedVorlageMail.value?.attributes === undefined) {
        return;
      }

      const isInformal = anrede.value === Anrede.Duzen
        && selectedVorlageMail.value.attributes.informalContent !== undefined;

      vorlageContent.value = isInformal
        ? selectedVorlageMail.value.attributes.informalContent
        : selectedVorlageMail.value.attributes.formalContent;
      vorlageBetreff.value = isInformal
        ? selectedVorlageMail.value.attributes.informalSubject
        : selectedVorlageMail.value.attributes.formalSubject;

      return;
    }

    if (selectedVorlageBrief.value?.attributes === undefined) {
      return;
    }

    const isInformal = anrede.value === Anrede.Duzen
      && selectedVorlageBrief.value.attributes.informalContent !== undefined;

    vorlageContent.value = isInformal
      ? selectedVorlageBrief.value.attributes.informalContent
      : selectedVorlageBrief.value.attributes.formalContent;
  };

  function getEmpfaengerForType(type: VorlageMailEmpfaenger): EmailElement[] {
    let empfaengers;

    if (!(type in VorlageMailEmpfaengerType)) {
      empfaengers = { email: type };
    }

    switch (type as VorlageMailEmpfaengerType) {
      case VorlageMailEmpfaengerType.DEMV:
        empfaengers = { name: 'demv', email: '<EMAIL>' };

        break;
      case VorlageMailEmpfaengerType.Gesellschaft:
        empfaengers = ansprechpartnerMails.value;

        break;
      case VorlageMailEmpfaengerType.Kunde:
        empfaengers = toEmailElement(params.kunde.value?.attributes);

        break;
      case VorlageMailEmpfaengerType.Versender:
        if (ownerMailAddress.value?.email === undefined) {
          empfaengers = [] as EmailElement[];

          break;
        }

        empfaengers = {
          email: ownerMailAddress.value?.email,
          name:
            ownerMailAddress.value?.name !== undefined && ownerMailAddress.value?.name !== ''
              ? ownerMailAddress.value?.name
              : ownerMailAddress.value?.email,
        } as EmailElement;

        break;
      case VorlageMailEmpfaengerType.ZustaendigerVermittler:
        empfaengers = zustaendigerVermittlerReceiverAddress.value ?? [];

        break;
    }

    if (empfaengers === undefined) {
      return [];
    }

    return Array.isArray(empfaengers) ? empfaengers : [empfaengers];
  }

  function getMailEmpfaengersFromTypes(
    empfaengerTypes: VorlageMailEmpfaenger[],
  ): EmailElement[] {
    return uniqBy(empfaengerTypes.reduce((mailEmpfaengers, type) => [
      ...mailEmpfaengers,
      ...getEmpfaengerForType(type),
    ], <EmailElement[]>[]), (e) => e.email);
  }

  const updateEmpfaengers = (allowClear = false) => {
    if (selectedVorlage.value === undefined) {
      if (allowClear) {
        if (params.resetEmpfaengerFields !== undefined) {
          params.resetEmpfaengerFields();
        }

        if (params.briefEmpfaengerType !== undefined) {
          params.briefEmpfaengerType.value = BriefEmpfaenger.Eigene;
        }
      }

      return;
    }

    // update empfaenger for email.
    if (vorlageType.value === 'vorlagenMail') {
      if (selectedVorlageMail.value?.attributes === undefined) {
        return;
      }

      params.mailEmpfaenger.value = getMailEmpfaengersFromTypes(
        selectedVorlageMail.value.attributes.empfaengerTypes,
      );

      params.mailHasCcAndBcc.value = (
        selectedVorlageMail.value.attributes.cc.length > 0
        || selectedVorlageMail.value.attributes.bcc.length > 0
      );

      params.mailCc.value = getMailEmpfaengersFromTypes(
        selectedVorlageMail.value.attributes.cc,
      );
      params.mailBcc.value = getMailEmpfaengersFromTypes(
        selectedVorlageMail.value.attributes.bcc,
      );

      return;
    }

    if (selectedVorlageBrief.value?.attributes === undefined) {
      return;
    }

    if (params.briefEmpfaengerType === undefined) {
      return;
    }

    if (params.briefAbsender !== undefined) {
      params.briefAbsender.value = params.kunde.value?.id == null
        ? BriefAbsender.Makler
        : selectedVorlageBrief.value?.attributes.senderTyp ?? BriefAbsender.Makler;
    }

    const newBriefEmpfaengerType = (
      selectedVorlageBrief.value?.attributes.empfaengerType
      ?? BriefEmpfaenger.Eigene
    );

    const isBriefEmpfaengerTypeGesellschaftAndGesellschaftNull = (
      newBriefEmpfaengerType === BriefEmpfaenger.Gesellschaft
      && params.vertriebsweg.value?.id == null
    );

    const isBriefEmpfaengerTypeKundeAndKundeNull = (
      newBriefEmpfaengerType === BriefEmpfaenger.Kunde
      && params.kunde.value?.id == null
    );

    if (
      isBriefEmpfaengerTypeGesellschaftAndGesellschaftNull
      || isBriefEmpfaengerTypeKundeAndKundeNull
    ) {
      params.briefEmpfaengerType.value = BriefEmpfaenger.Eigene;

      return;
    }

    params.briefEmpfaengerType.value = newBriefEmpfaengerType;
  };

  /* --- reactivity --- */

  // if clientUseDuzen updates, update anrede regardless of the user input.
  watch([
    params.kunde,
    selectedVorlage,
  ], () => {
    anrede.value = clientUseDuzen.value && hasVorlageInformal.value
      ? Anrede.Duzen
      : Anrede.Siezen;
  });

  // on owner change, reload vorlagen
  watch(params.ownerId, async () => {
    await updateVorlagen();

    selectedVorlage.value = vorlagen.value[0] ?? undefined;
  });

  watch(params.ownerId, (ownerId) => {
    void getReceiverMailAddress(ownerId)
      .then((emailElement) => {
        ownerMailAddress.value = emailElement;
      });
  }, { immediate: true });

  // on ansprechpartner relevant data change: update ansprechpartners & empfaengers
  // watchDebounced used to prevent race condition as this watcher is called multiple times
  watchDebounced([
    params.vertriebsweg,
    params.vorgangstypId,
    params.sparte,
  ], async () => {
    if (params.vorgangstypId.value == null) {
      return;
    }

    if (params.versandart.value === Versandart.Mail) {
      await loadAnsprechpartnerList(
        params.vorgangstypId.value,
        params.vertriebsweg.value?.id ?? undefined,
        params.sparte?.value?.id ?? undefined,
      );
    }

    updateEmpfaengers();
  }, { immediate: true, debounce: 10 });

  // on update vorgangTyp load vorlagen and select first vorlage
  watch(params.vorgangstypId, async (newVorgangstypId) => {
    if (newVorgangstypId == undefined || newVorgangstypId === '') {
      resetVorlagen();
    } else {
      await loadVorlagenAndIncludedVorlagen(newVorgangstypId);
    }

    selectedVorlage.value = vorlagen.value.find(
      (vorlage) => vorlage.attributes.isBasisvorlage === true,
    ) ?? vorlagen.value[0];
  }, { immediate: true });

  // on kunde or absender change: update empfaengers
  watch(params.kunde, async (newKunde) => {
    clearZustaendigerVermittler();
    if (!newKunde) {
      return;
    }

    await loadZustaendigerVermittlerReceiverAddress(newKunde.id);
  });

  watch([
    params.kunde,
    ownerMailAddress,
    ansprechpartnerMails,
    zustaendigerVermittlerReceiverAddress,
  ], () => updateEmpfaengers());

  // anrede changes: update content
  watch(anrede, () => updateContent());

  // vorlage changes: clear & update empfaenger & content
  watch(selectedVorlage, () => {
    updateEmpfaengers(true);
    updateContent();
  });

  return {
    hasVorlageInformal,
    anrede,
    vorlagen,
    vorlageBetreff,
    vorlageContent,
    selectedVorlage,
    selectedVorlageBrief,
    selectedVorlageMail,
    updateVorlagen,
    selectedVorlageId: computed({
      get: () => selectedVorlage.value?.id ?? null,
      set: (newId) => {
        selectedVorlage.value = vorlagen.value.find((v) => v.id === newId);
      },
    }),
  };
};
