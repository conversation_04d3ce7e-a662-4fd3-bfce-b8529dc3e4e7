import { extractErrors, isAxiosError, post } from '@/api';
import { reloadStoreFromCurrentVorgang } from '@/pages/vorgang/components/Timeline/Actions/storeService';
import { eventBus } from '@/store/resources/store';
import { Store, TimelineEintragResource } from '@/store/resources/types';

export default async (
  vorgangId: string,
  timelineEintrag: TimelineEintragResource,
  store: Store,
  loeschGrund: string,
) : Promise<void> => {
  try {
    const elementType = timelineEintrag.relationships?.element?.data?.type;
    const elementId = timelineEintrag.relationships?.element?.data?.id;

    await post(`vorgaenge/${vorgangId}/${elementType}/${elementId}/delete`, {
      data: {
        type: 'loeschKommentar',
        attributes: {
          content: loeschGrund,
        },
      },
    });
    await reloadStoreFromCurrentVorgang(store, vorgangId);
  }  catch (e) {
    if (isAxiosError(e) && e.response !== undefined) {
      eventBus.emit('emitValidation', extractErrors(e.response.data?.errors ?? []));
    }

    throw e;
  }
};
