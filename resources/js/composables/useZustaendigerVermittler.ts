import axios from 'axios';
import { ref } from 'vue';

import { isAxiosError } from '@/api';
import { eventBus } from '@/store/resources/store';
import { EmailElement, UserResource } from '@/store/resources/types';

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useZustaendigerVermittler() {
  const url = ref<string | null>(null);
  const zustaendigerVermittlerReceiverAddress = ref<EmailElement | null>(null);
  const zustaendigerVermittler = ref<UserResource | null>(null);

  async function loadZustaendigerVermittlerReceiverAddress(
    kundeId: string,
  ): Promise<void> {
    url.value = `/api/kunden/${kundeId}/zustaendigerVermittler/receiverMailAddress`;
    try {
      const response = await axios.get(url.value);
      zustaendigerVermittlerReceiverAddress.value = response.data;
    } catch (e) {
      if (!isAxiosError(e) || e.response === undefined) {
        eventBus.emit('error');
        throw e;
      }
    }
  }

  async function loadZustaendigerVermittler(
    kundeId: string,
  ): Promise<void> {
    url.value = `/api/kunden/${kundeId}/zustaendigerVermittler`;
    try {
      const response = await axios.get(url.value);
      zustaendigerVermittler.value = response.data;
    } catch (e) {
      if (!isAxiosError(e) || e.response === undefined) {
        eventBus.emit('error');
        throw e;
      }
    }
  }

  function clearZustaendigerVermittler(): void {
    zustaendigerVermittler.value = null;
    zustaendigerVermittlerReceiverAddress.value = null;
  }

  return {
    clearZustaendigerVermittler,
    loadZustaendigerVermittlerReceiverAddress,
    loadZustaendigerVermittler,
    zustaendigerVermittlerReceiverAddress,
    zustaendigerVermittler,
  };
}
