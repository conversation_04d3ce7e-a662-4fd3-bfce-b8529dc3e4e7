import axios from 'axios';

import { eventBus } from '@/store/resources/store';

type ImpersonateOptions = {
  redirectBack?: boolean,
};

export function useImpersonate(): {
  impersonate: (userId: string, options?: ImpersonateOptions) => Promise<boolean>,
  leaveImpersonation: () => void,
} {
  function impersonate(userId: string, options?: ImpersonateOptions): Promise<boolean> {
    return axios.get(`/api/users/${userId}/allows-impersonation`)
      .then(() => {
        let url = `/users/${userId}/impersonate`;

        if (options?.redirectBack) {
          url += '?redirectBack';
        }

        window.location.href = url;

        return true;
      })
      .catch(() => {
        eventBus.emit('adminLoginFailed');

        return false;
      });
  }

  function leaveImpersonation() {
    window.location.href = '/users/leave-impersonate';
  }

  return {
    impersonate,
    leaveImpersonation,
  };
}
