// extracted for a better unit test

import { computed, ComputedRef, ref } from 'vue';

import { StackComponent } from './types';

export function useNavigationStack(initialComponent: StackComponent):
{
  activeComponent: ComputedRef<StackComponent>;
  cachedComponents: ComputedRef<string[]>;
  popComponent: () => void;
  pushComponent: (action: StackComponent) => void;
  resetNavigationStack: () => void;
  stackSize: ComputedRef<number>
} {
  const actionsNavigationStack = ref<StackComponent[]>(
    new Array<StackComponent>(
      initialComponent,
    ));

  const stackSize = computed(() => actionsNavigationStack.value.length ?? 0);

  const activeComponent = computed(() => (
    actionsNavigationStack.value.at(-1) ?? initialComponent
  ));

  const popComponent = () => {
    // dont allow empty stack.
    if (actionsNavigationStack.value.length <= 1) {
      return;
    }

    actionsNavigationStack.value.pop();
  };

  const pushComponent = (action: StackComponent) => {
    actionsNavigationStack.value.push(action);
  };

  const resetNavigationStack = () => {
    actionsNavigationStack.value = [initialComponent];
  };

  // for the keep-alive functionality.
  const cachedComponents = computed(() => actionsNavigationStack.value.map(
    (comp) => comp.componentName,
  ));

  return {
    activeComponent,
    cachedComponents,
    popComponent,
    pushComponent,
    resetNavigationStack,
    stackSize,
  };
}
