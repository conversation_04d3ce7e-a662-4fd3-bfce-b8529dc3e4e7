import { ComputedRef, computed, ref } from 'vue';

import { JsonApiUrl } from '@/api';
import {
  AnsprechpartnerResource,
  EmailElement,
} from '@/store/resources/types';
import { useExternalList } from '@/utils/useExternalList';

export function useAnsprechpartners(): {
  loadAnsprechpartnerList: (
    vorgangstypId: string, gesellschaftId?: string, sparteId?: string
  ) => Promise<void>;
  ansprechpartnerMails: ComputedRef<EmailElement[]>;
} {
  const url = ref<string | null>(null);
  const options = ref<JsonApiUrl<AnsprechpartnerResource[]>>({});

  const {
    list: ansprechpartnerList,
    update: updateList,
  } = useExternalList<AnsprechpartnerResource>(
    url,
    options,
    { updateManually: true },
  );

  async function loadAnsprechpartnerList(
    vorgangstypId: string,
    gesellschaftId?: string,
    sparteId?: string,
  ): Promise<void> {
    if (gesellschaftId === undefined) {
      ansprechpartnerList.value = [];

      return;
    }

    url.value = `gesellschaft/${gesellschaftId}/ansprechpartner`;
    options.value = {
      filter: {
        vorgangstyp: vorgangstypId,
        ...(sparteId !== undefined ? { sparte: sparteId } : {}),
      },
    };

    await updateList();
  }

  const ansprechpartnerMails = computed<EmailElement[]>(() => {
    return ansprechpartnerList.value.map((person) => ({
      email: person.attributes.email,
      name: person.attributes.lastname,
    } as EmailElement));
  });

  return {
    loadAnsprechpartnerList,
    ansprechpartnerMails,
  };
}
