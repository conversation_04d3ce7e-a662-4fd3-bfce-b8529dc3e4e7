import { MaybeRef, unref } from 'vue';

import {
  saveFile,
  toUploadedFileResource,
  uploadFileToS3,
} from '@/utils/fileUtils';

const BLOB_URL_REGEX = /blob:https?:\/\/[^\s"]+/g;

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useImageUploadHandler(
  ownerId: MaybeRef<string | undefined>,
  ownerType: MaybeRef<string | undefined>,
) {
  /**
   * Uploads images from files and returns their URLs.
   * If no owner is provided during execution, a blob URL is returned instead.
   */
  function imageUploadHandler(files: File[]): Promise<string>[] {
    return files.map(uploadImage);
  }

  async function uploadImage(file: File): Promise<string> {
    const id = unref(ownerId);
    const type = unref(ownerType);

    // If owner info isn't present, create a temporary blob URL
    if (id === undefined || type === undefined) {
      return URL.createObjectURL(file);
    }

    // Otherwise, upload to S3 and save the file reference
    const tempPath = await uploadFileToS3(file);
    const fileResource = toUploadedFileResource(file, tempPath, true);
    const savedFile = await saveFile(fileResource, id, type, true);
    const url = savedFile.links?.self;

    if (url === undefined) {
      throw new Error('File upload failed: Missing URL in response');
    }

    return url;
  }

  /**
   * Uploads files from blob URLs in a text.
   * The uploaded files are then replaced with their uploaded URLs in the text.
   */
  async function uploadImagesFromBlobUrls(text: string) {
    const blobUrls = text.match(BLOB_URL_REGEX) ?? [];

    if (blobUrls.length === 0) {
      return text; // No blob URLs found
    }

    const uploadResults = await Promise.all(blobUrls.map(uploadImageFromBlobUrl));
    const urlMap = new Map(uploadResults);

    // Replace blob URLs with uploaded URLs in the text
    return text.replace(BLOB_URL_REGEX, (match) => {
      const replacement = urlMap.get(match);
      if (replacement === undefined) {
        throw new Error(`Failed to find uploaded URL for ${match}`);
      }

      return replacement;
    });
  }

  async function uploadImageFromBlobUrl(
    blobUrl: string,
  ): Promise<[string, string]> {
    const file = await getFileFromBlobUrl(blobUrl);
    const uploadedUrl = await uploadImage(file);
    URL.revokeObjectURL(blobUrl); // Clean up blob URL

    return [blobUrl, uploadedUrl];
  }

  async function getFileFromBlobUrl(blobUrl: string): Promise<File> {
    const response = await fetch(blobUrl);
    const blob = await response.blob();

    return new File([blob], 'blob-upload', { type: blob.type });
  }

  function hasBlobUrls(text: string): boolean {
    return BLOB_URL_REGEX.test(text);
  }

  return {
    imageUploadHandler,
    uploadImagesFromBlobUrls,
    hasBlobUrls,
  };
}
