# Manueller MSG-Konvertierung Test

## Vorbereitung

### 1. ConvertApi-Konfiguration prüfen
```bash
docker compose exec php-fpm bash -c "php artisan convert-api:test"
```

### 2. Logs vorbereiten
```bash
# Terminal 1: Logs überwachen
docker compose exec php-fpm bash -c "tail -f storage/logs/laravel.log | grep -i 'msg\|convert\|error'"
```

## Test-Szenarien

### Szenario 1: Erfolgreiche MSG-Konvertierung

**Schritte:**
1. Öffne die Anwendung im Browser
2. Navigiere zu einem bestehenden Vorgang
3. Lade eine gültige MSG-Datei hoch
4. Beobachte die Logs im Terminal

**Erwartete Log-Ausgabe:**
```
INFO: Processing MSG file {"file_id":123,"filename":"test.msg","size":12345}
INFO: Starting MSG to MIME conversion via ConvertApi
INFO: ConvertApi metadata conversion successful {"meta_url":"https://..."}
INFO: MSG conversion successful {"has_subject":true,"has_sender":true,"attachment_count":0}
```

**Erwartetes Ergebnis:**
- ✅ Datei wird erfolgreich hochgeladen
- ✅ MSG wird zu E-Mail konvertiert
- ✅ E-Mail erscheint in der Timeline
- ✅ Betreff, Absender und Inhalt sind sichtbar

### Szenario 2: Fehlende ConvertApi-Konfiguration

**Vorbereitung:**
```bash
# ConvertApi-Secret temporär entfernen
docker compose exec php-fpm bash -c "sed -i 's/CONVERT_API_SECRET=.*/CONVERT_API_SECRET=/' .env"
docker compose exec php-fpm bash -c "php artisan config:clear"
```

**Schritte:**
1. Versuche eine MSG-Datei hochzuladen
2. Beobachte die Logs

**Erwartete Log-Ausgabe:**
```
ERROR: ConvertApi secret is not configured {"config_value":""}
```

**Erwartetes Ergebnis:**
- ❌ 422-Fehler: "CONVERT_API_SECRET is not configured"

**Cleanup:**
```bash
# ConvertApi-Secret wiederherstellen
docker compose exec php-fpm bash -c "sed -i 's/CONVERT_API_SECRET=.*/CONVERT_API_SECRET=your_actual_secret/' .env"
docker compose exec php-fpm bash -c "php artisan config:clear"
```

### Szenario 3: Beschädigte MSG-Datei

**Schritte:**
1. Erstelle eine Fake-MSG-Datei:
   ```bash
   echo "This is not a real MSG file" > fake.msg
   ```
2. Benenne sie zu `fake.msg` um
3. Versuche sie hochzuladen
4. Beobachte die Logs

**Erwartete Log-Ausgabe:**
```
INFO: Processing MSG file {"file_id":124,"filename":"fake.msg","size":28}
INFO: Starting MSG to MIME conversion via ConvertApi
ERROR: MSG to MIME conversion failed {"error":"ConvertApi conversion failed: ..."}
ERROR: MSG conversion failed in MailConverterService {"file_id":124,"error":"..."}
```

**Erwartetes Ergebnis:**
- ❌ 422-Fehler: "MSG-Datei konnte nicht konvertiert werden: ..."

### Szenario 4: Debug-Endpoint testen

**HTTP-Request:**
```bash
# Mit gültigem Auth-Token
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Accept: application/json" \
     http://localhost/api/debug/convert-api-status
```

**Erwartete Antwort (Erfolg):**
```json
{
  "status": "success",
  "message": "ConvertApi is configured",
  "secret_length": 32,
  "secret_preview": "8knS...A5ki"
}
```

**Erwartete Antwort (Fehler):**
```json
{
  "status": "error",
  "message": "CONVERT_API_SECRET not configured",
  "config_value": ""
}
```

## Automatisierte Tests ausführen

### Backend-Tests
```bash
docker compose exec php-fpm bash -c "php artisan test tests/Feature/MsgFileConversionTest.php"
```

### Frontend-Tests (Cypress)
```bash
# Cypress öffnen
npm run cypress:open

# Oder headless ausführen
npm run cypress:run --spec "cypress/e2e/msg_file_upload.cy.ts"
```

## Debugging-Checkliste

Wenn Tests fehlschlagen, prüfe:

- [ ] ConvertApi-Secret ist korrekt konfiguriert
- [ ] ConvertApi-Account hat genügend Credits
- [ ] Netzwerkverbindung zu ConvertApi funktioniert
- [ ] MSG-Datei ist nicht beschädigt
- [ ] Dateigröße ist nicht zu groß
- [ ] Logs zeigen detaillierte Fehlermeldungen

## Häufige Probleme

### Problem: "ConvertApi conversion failed: Unauthorized"
**Lösung:** API-Secret überprüfen und neu setzen

### Problem: "ConvertApi conversion failed: Insufficient credits"
**Lösung:** ConvertApi-Account aufladen

### Problem: "Invalid file stream"
**Lösung:** Datei-Upload-Prozess und Dateiberechtigungen prüfen

### Problem: "ConvertApi returned null message"
**Lösung:** MSG-Datei auf Beschädigung prüfen, andere MSG-Datei testen

## Test-Dateien

Für Tests kannst du folgende MSG-Dateien verwenden:

1. **Echte MSG-Datei:** Exportiere eine E-Mail aus Outlook als MSG
2. **Test-MSG-Datei:** Lade eine kleine MSG-Datei aus dem Internet herunter
3. **Fake-MSG-Datei:** Erstelle eine Textdatei mit .msg-Endung (sollte fehlschlagen)

## Monitoring in Produktion

```bash
# Kontinuierliches Monitoring der MSG-Konvertierung
tail -f storage/logs/laravel.log | grep -E "(MSG|ConvertApi|conversion)" --color=always
```
