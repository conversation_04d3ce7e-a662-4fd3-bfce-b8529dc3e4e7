describe('MSG File Upload and Conversion', () => {
  beforeEach(() => {
    // Mock PW responses
    cy.mockPwResponse('get', '/stammdaten/api/ansprechpartner/getAnsprechpartner', '{"status":"success","data":[]}');
    cy.mockPwResponse('get', '/api/tags/replace', '{"status":"success","data":{"datum":{"type":"text","value":"10.12.1815"}}}');
    
    // Create a test vorgang first
    cy.createTestVorgang();
  });

  it('should successfully upload and convert MSG file', () => {
    // Navigate to vorgang detail page
    cy.getBySel('vorgang__timeline').should('exist');
    
    // Mock successful file upload
    cy.intercept('POST', '/api/files', {
      statusCode: 201,
      body: {
        data: {
          id: 123,
          filename: 'test-email.msg',
          mimetype: 'application/vnd.ms-outlook',
          size: 12345
        }
      }
    }).as('fileUpload');

    // Mock successful MSG conversion
    cy.intercept('POST', '/api/vorgaenge/*/mail-converter', {
      statusCode: 200,
      body: {
        data: {
          id: 456,
          betreff: 'Test Email Subject',
          content: '<p>Test email content</p>',
          absender: { email: '<EMAIL>', name: 'Test Sender' },
          empfaenger: [{ email: '<EMAIL>', name: 'Test Recipient' }],
          versendet_at: '2024-01-15T10:30:00Z'
        }
      }
    }).as('msgConversion');

    // Upload MSG file
    cy.getBySel('file-upload-button').click();
    
    // Create a mock MSG file
    const msgContent = 'Mock MSG file content';
    const msgFile = new File([msgContent], 'test-email.msg', {
      type: 'application/vnd.ms-outlook'
    });

    // Simulate file selection
    cy.getBySel('file-input').selectFile({
      contents: Cypress.Buffer.from(msgContent),
      fileName: 'test-email.msg',
      mimeType: 'application/vnd.ms-outlook'
    });

    // Wait for file upload
    cy.wait('@fileUpload').then((interception) => {
      expect(interception.response?.statusCode).to.equal(201);
    });

    // Trigger MSG conversion
    cy.getBySel('convert-msg-button').click();

    // Wait for MSG conversion
    cy.wait('@msgConversion').then((interception) => {
      expect(interception.response?.statusCode).to.equal(200);
    });

    // Verify converted email appears in timeline
    cy.getBySel('vorgang__timeline')
      .should('contain.text', 'Test Email Subject')
      .should('contain.text', 'Test email content')
      .should('contain.text', '<EMAIL>');
  });

  it('should handle MSG conversion failure gracefully', () => {
    // Navigate to vorgang detail page
    cy.getBySel('vorgang__timeline').should('exist');
    
    // Mock successful file upload
    cy.intercept('POST', '/api/files', {
      statusCode: 201,
      body: {
        data: {
          id: 123,
          filename: 'corrupted-email.msg',
          mimetype: 'application/vnd.ms-outlook',
          size: 12345
        }
      }
    }).as('fileUpload');

    // Mock MSG conversion failure
    cy.intercept('POST', '/api/vorgaenge/*/mail-converter', {
      statusCode: 422,
      body: {
        message: 'MSG-Datei konnte nicht konvertiert werden: ConvertApi conversion failed'
      }
    }).as('msgConversionFailure');

    // Upload MSG file
    cy.getBySel('file-upload-button').click();
    
    const msgContent = 'Corrupted MSG file content';
    cy.getBySel('file-input').selectFile({
      contents: Cypress.Buffer.from(msgContent),
      fileName: 'corrupted-email.msg',
      mimeType: 'application/vnd.ms-outlook'
    });

    // Wait for file upload
    cy.wait('@fileUpload');

    // Trigger MSG conversion
    cy.getBySel('convert-msg-button').click();

    // Wait for MSG conversion failure
    cy.wait('@msgConversionFailure').then((interception) => {
      expect(interception.response?.statusCode).to.equal(422);
    });

    // Verify error message is displayed
    cy.getBySel('error-message')
      .should('be.visible')
      .should('contain.text', 'MSG-Datei konnte nicht konvertiert werden');
  });

  it('should handle ConvertApi configuration error', () => {
    // Navigate to vorgang detail page
    cy.getBySel('vorgang__timeline').should('exist');
    
    // Mock successful file upload
    cy.intercept('POST', '/api/files', {
      statusCode: 201,
      body: {
        data: {
          id: 123,
          filename: 'test-email.msg',
          mimetype: 'application/vnd.ms-outlook',
          size: 12345
        }
      }
    }).as('fileUpload');

    // Mock ConvertApi configuration error
    cy.intercept('POST', '/api/vorgaenge/*/mail-converter', {
      statusCode: 422,
      body: {
        message: 'CONVERT_API_SECRET is not configured. Please set the environment variable.'
      }
    }).as('configError');

    // Upload MSG file
    cy.getBySel('file-upload-button').click();
    
    const msgContent = 'Test MSG file content';
    cy.getBySel('file-input').selectFile({
      contents: Cypress.Buffer.from(msgContent),
      fileName: 'test-email.msg',
      mimeType: 'application/vnd.ms-outlook'
    });

    // Wait for file upload
    cy.wait('@fileUpload');

    // Trigger MSG conversion
    cy.getBySel('convert-msg-button').click();

    // Wait for configuration error
    cy.wait('@configError').then((interception) => {
      expect(interception.response?.statusCode).to.equal(422);
    });

    // Verify configuration error message is displayed
    cy.getBySel('error-message')
      .should('be.visible')
      .should('contain.text', 'CONVERT_API_SECRET is not configured');
  });

  it('should validate file type before upload', () => {
    // Navigate to vorgang detail page
    cy.getBySel('vorgang__timeline').should('exist');
    
    // Try to upload non-MSG file
    cy.getBySel('file-upload-button').click();
    
    const txtContent = 'This is not an MSG file';
    cy.getBySel('file-input').selectFile({
      contents: Cypress.Buffer.from(txtContent),
      fileName: 'not-an-email.txt',
      mimeType: 'text/plain'
    });

    // Verify validation error
    cy.getBySel('file-validation-error')
      .should('be.visible')
      .should('contain.text', 'Nur MSG-Dateien sind erlaubt');
  });

  it('should show loading state during conversion', () => {
    // Navigate to vorgang detail page
    cy.getBySel('vorgang__timeline').should('exist');
    
    // Mock successful file upload
    cy.intercept('POST', '/api/files', {
      statusCode: 201,
      body: {
        data: {
          id: 123,
          filename: 'test-email.msg',
          mimetype: 'application/vnd.ms-outlook',
          size: 12345
        }
      }
    }).as('fileUpload');

    // Mock slow MSG conversion
    cy.intercept('POST', '/api/vorgaenge/*/mail-converter', {
      statusCode: 200,
      body: {
        data: {
          id: 456,
          betreff: 'Test Email Subject',
          content: '<p>Test email content</p>'
        }
      },
      delay: 2000 // 2 second delay
    }).as('slowMsgConversion');

    // Upload MSG file
    cy.getBySel('file-upload-button').click();
    
    const msgContent = 'Test MSG file content';
    cy.getBySel('file-input').selectFile({
      contents: Cypress.Buffer.from(msgContent),
      fileName: 'test-email.msg',
      mimeType: 'application/vnd.ms-outlook'
    });

    cy.wait('@fileUpload');

    // Trigger MSG conversion
    cy.getBySel('convert-msg-button').click();

    // Verify loading state is shown
    cy.getBySel('conversion-loading')
      .should('be.visible')
      .should('contain.text', 'MSG-Datei wird konvertiert...');

    // Verify convert button is disabled during loading
    cy.getBySel('convert-msg-button')
      .should('be.disabled');

    // Wait for conversion to complete
    cy.wait('@slowMsgConversion');

    // Verify loading state is hidden
    cy.getBySel('conversion-loading')
      .should('not.exist');

    // Verify convert button is enabled again
    cy.getBySel('convert-msg-button')
      .should('not.be.disabled');
  });
});
