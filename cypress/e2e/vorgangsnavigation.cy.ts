import { addDays, format } from 'date-fns';
import { cloneDeep } from 'lodash-es';

import { KorrespondenzResource, VorgangResource } from '@/store/resources/types';
import { VorgangStatus } from '@/types';
import { Document } from '@/types/jsonapi';

const today = new Date();

const mockAufgabe = {
  titel: 'Aufgabe Test',
  faelligAt: format(addDays(today, 1), 'yyyy-MM-dd'),
};

const mockKorrespondenz = [
  {
    status: VorgangStatus.Offen,
    vorgangsTyp: '89',
    faelligAt: format(addDays(today, 2), 'yyyy-MM-dd'),
    kundeId: '1',
    gesellschaftId: '1',
    bearbeiter: 2,
  },
  {
    status: VorgangStatus.Offen,
    vorgangsTyp: '91',
    faelligAt: format(addDays(today, 4), 'yyyy-MM-dd'),
    gesellschaftId: '1',
    kundeId: '3',
  },
  {
    status: VorgangStatus.Erledigt,
    vorgangsTyp: '104',
    faelligAt: format(addDays(today, 5), 'yyyy-MM-dd'),
    gesellschaftId: '1',
    kundeId: '1',
  },
  {
    status: VorgangStatus.Offen,
    vorgangsTyp: '104',
    faelligAt: format(addDays(today, 6), 'yyyy-MM-dd'),
    gesellschaftId: '3',
    kundeId: '2',
    vertragId: null,
  },
  {
    status: VorgangStatus.Offen,
    vorgangsTyp: '104',
    faelligAt: format(addDays(today, 7), 'yyyy-MM-dd'),
    gesellschaftId: '3',
    kundeId: '2',
    vertragId: '6',
  },
];

// send mock data to backend
function postMockVorgaenge() {
  cy.fixture<VorgangResource>('vorgaenge/aufgabe')
    .then((aufgabe) => {
      aufgabe.attributes.titel = mockAufgabe.titel;
      aufgabe.attributes.faelligAt = mockAufgabe.faelligAt;
      cy.request('POST', '/api/vorgaenge', { data: aufgabe });
    });

  cy.fixture<VorgangResource>('vorgaenge/gruppe')
    .then((gruppe) => {
      return cy.request('POST', '/api/vorgaenge', { data: gruppe })
        .then((response) => response.body.data.id);
    })
    .then((groupId) => {
      cy.fixture<VorgangResource & {
        attributes: { groupParent: number }
      }>('vorgaenge/korrespondenz')
        .then((korrespondenz) => {
          cy.mockPwResponse('post', '/api/mailer/forUser/1/send', '{"status":"success","data":{"from":{"address":"<EMAIL>","name":"tester"},"to":[{"address":"<EMAIL>","name":"tester"}],"cc":[{"address":"<EMAIL>","name":"tester"}],"bcc":[{"address":"<EMAIL>","name":"tester"}]}}', 2);

          korrespondenz.attributes.faelligAt = format(addDays(today, 7), 'yyyy-MM-dd');
          korrespondenz.attributes.groupParent = Number.parseInt(groupId);

          korrespondenz.attributes.status = VorgangStatus.Offen;
          cy.request<Document<KorrespondenzResource>>('POST', '/api/vorgaenge', { data: cloneDeep(korrespondenz) });

          korrespondenz.attributes.status = VorgangStatus.Erledigt;
          cy.request<Document<KorrespondenzResource>>('POST', '/api/vorgaenge', { data: cloneDeep(korrespondenz) });
        });
    });

  mockKorrespondenz.forEach((mock) => {
    cy.fixture<VorgangResource>('vorgaenge/korrespondenz')
      .then((korrespondenz) => {
        cy.mockPwResponse('post', '/api/mailer/forUser/1/send', '{"status":"success","data":{"from":{"address":"<EMAIL>","name":"tester"},"to":[{"address":"<EMAIL>","name":"tester"}],"cc":[{"address":"<EMAIL>","name":"tester"}],"bcc":[{"address":"<EMAIL>","name":"tester"}]}}');

        korrespondenz.attributes.status = mock.status;
        if (korrespondenz.relationships?.vorgangTyp?.data?.id) {
          korrespondenz.relationships.vorgangTyp.data.id = mock.vorgangsTyp;
        }

        if (korrespondenz.relationships?.kunde?.data?.id) {
          korrespondenz.relationships.kunde.data.id = mock.kundeId;
        }

        if (korrespondenz.relationships?.gesellschaft?.data?.id) {
          korrespondenz.relationships.gesellschaft.data.id =
            mock.gesellschaftId;
        }

        if (korrespondenz.relationships?.vertraege?.data && mock.vertragId !== undefined) {
          if (mock.vertragId === null) {
            korrespondenz.relationships.vertraege.data = [];
          } else {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            korrespondenz.relationships.vertraege.data[0].id = mock.vertragId;
          }
        }

        korrespondenz.attributes.faelligAt = mock.faelligAt;

        cy.request<Document<KorrespondenzResource>>('POST', '/api/vorgaenge', { data: korrespondenz })
          .then((response) => {
            expect(response.status).to.eq(201);
            expect(response).to.have.property('body');
            if (mock.bearbeiter !== undefined) {
              cy.request('POST', `/api/vorgaenge/${response.body.data?.id}/participants/bearbeiter`, {
                data: {
                  type: 'vorgang_participants',
                  relationships: {
                    user: {
                      data: {
                        type: 'users',
                        id: mock.bearbeiter,
                      },
                    },
                  },
                },
              });
              cy.request('DELETE', `/api/vorgaenge/${response.body.data?.id}/participants/bearbeiter/1`);
            }
          });
      });
  });
}

// helper functions for repeated actions
function clearSearchInput() {
  cy.getBySel('vorgangsliste__search-input')
    .clear()
    .type('{enter}');
}

function openFilterModal() {
  cy.getBySel('vorgangsliste__filter__btn')
    .find('button')
    .click();
}

function clearFilters() {
  cy.getBySel('vorgangsliste__filter__clear')
    .find('button')
    .click();
}

function applyFilters() {
  cy.get('button').contains('Filter übernehmen').first().click();
}

// run tests
describe('vorgangsnavigation', () => {
  it('shows notification if vorgangsliste is empty, search and filter for vorgaenge in vorgangsliste', () => {
    // Test if notification for empty vorgangsliste is being displayed
    cy.getBySel('vorgangsliste')
      .find('[data-test="vorgangsliste__empty-message"]')
      .should('not.be.empty');

    cy.getBySel('vorgangsliste')
      .find('button')
      .should('exist');

    // create mock data for further testing
    postMockVorgaenge();

    // Test if searching for vorgang yields the expected result
    cy.getBySel('vorgangsliste__search-input')
      .type(mockAufgabe.titel)
      .type('{enter}');

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 1);

        cy.getBySel('vorgangsliste__item__titel')
          .should('have.text', mockAufgabe.titel)
          .click();
      });

    // test if vorgang is open
    cy.getBySel('vorgang__titel')
      .should('have.text', mockAufgabe.titel);

    cy.getBySel('vorgang__vorgangsnummer')
      .should('exist');

    clearSearchInput();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 6);
      });

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.containsBySel('vorgangsliste__item', 'Testgruppe')
          .should('exist')
          .contains('span', '1 / 2')
          .click();
      });
    cy.getBySel('vorgang__titel')
      .should('have.text', 'Testgruppe');
    cy.getBySel('untervorgaenge-list__item')
      .should('have.length', 2)
      .should('contain.text', 'Offen')
      .should('contain.text', 'Erledigt');
    cy.containsBySel('vorgang__sidebar__cta', 'Fällig zum')
      .find('button')
      .should('be.disabled');

    // Test if filter participants yields the expected results

    openFilterModal();
    cy.getBySel('vorgangsliste__filter__toggle-participants-select').find('button').click();

    cy.selectFromDsSelect('vorgangsliste__filter__bearbeiter', 0);
    applyFilters();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 5);
      });

    openFilterModal();
    cy.clearDsSelect('vorgangsliste__filter__bearbeiter');
    cy.selectFromDsSelect('vorgangsliste__filter__beobachter', 0);
    applyFilters();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 6);
      });

    openFilterModal();
    cy.getBySel('vorgangsliste__filter__toggle-bearbeiter-select').find('button').click();

    cy.selectFromDsSelect('vorgangsliste__filter__participants', 1);
    applyFilters();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 1);

        cy.getBySel('vorgangsliste__item__titel')
          .should('have.text', 'Ablauf (M-K)');
      });

    // Test if filter kunde yields the expected results
    openFilterModal();
    clearFilters();
    cy.selectFromDsSelectBySearch('vorgangsliste__filter__kunde', 'Gabriela Bavarai');
    applyFilters();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 1);

        cy.getBySel('vorgangsliste__item__titel')
          .should('have.text', 'Check-up Finanzen (M-K)');
      });

    // Test if filter vertrag yields the expected results
    openFilterModal();
    clearFilters();
    cy.selectFromDsSelectBySearch('vorgangsliste__filter__kunde', 'Maria Musterfrau');
    cy.selectFromDsMultiselectByLabel('vorgangsliste__filter__vertrag', 'Schaden-13485');
    applyFilters();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 1);

        cy.getBySel('vorgangsliste__item__titel')
          .should('have.text', 'Terminbestätigung');
      });

    // Test if filter gesellschaft yields the expected results
    openFilterModal();
    clearFilters();

    cy.selectFromDsSelect('vorgangsliste__filter__gesellschaft', 1);
    applyFilters();

    // Test if filter status yields the expected results
    openFilterModal();
    clearFilters();
    cy.selectFromDsSelect('vorgangsliste__filter__status', 1);
    applyFilters();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 1);

        cy.getBySel('vorgangsliste__item__titel')
          .should('have.text', 'Terminbestätigung');
      });

    // Test if filter vorgangsart yields the expected results
    openFilterModal();
    clearFilters();
    cy.selectFromDsMultiselectByLabel('vorgangsliste__filter__vorgangsart', 'Aufgabe');
    applyFilters();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 1);

        cy.getBySel('vorgangsliste__item__titel')
          .should('have.text', 'Aufgabe Test');
      });

    // Test if filter vorgangsTypen yields the expected results
    openFilterModal();
    clearFilters();
    cy.selectFromDsMultiselectByLabel('vorgangsliste__filter__vorgangstypen', 'Ablauf (M-K)');
    applyFilters();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 1);

        cy.getBySel('vorgangsliste__item__titel')
          .should('have.text', 'Ablauf (M-K)');
      });

    // Test if filter fromDate yields the expected results
    openFilterModal();
    clearFilters();
    cy.setDateInDsDatepicker('vorgangsliste__filter__from-date', addDays(today, 2));
    applyFilters();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 5);
      });

    // Test if filter untilDate and bis yields the expected results
    openFilterModal();
    cy.setDateInDsDatepicker('vorgangsliste__filter__until-date', addDays(today, 3));
    applyFilters();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 1);
      });

    // Test if manually clearing all filters yields expected result
    openFilterModal();
    clearFilters();
    cy.clearDsSelect('vorgangsliste__filter__participants');
    cy.clearDsSelect('vorgangsliste__filter__status');
    applyFilters();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 7);
      });

    // Test if aborting the setting of filters yields expected results
    openFilterModal();
    clearFilters();
    cy.get('button').contains('Abbrechen').first().click();

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 7);
      });
  });
});
