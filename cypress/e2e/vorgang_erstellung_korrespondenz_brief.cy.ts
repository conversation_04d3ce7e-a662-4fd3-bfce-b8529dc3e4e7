import { addMonths } from 'date-fns';

import { VorgangResource, VorlageResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

describe('Vorgangerstellung: Briefkorrespondenz', () => {
  it('creates a vorgang with versandart brief', () => {
    cy.mockPwResponse('get', '/stammdaten/api/ansprechpartner/getAnsprechpartner', '{"status":"success","data":[{"id":"20","sex_descriptor":"Frau","firstname":"<PERSON>","lastname":"Farwick (Bankprodukte)","email":"<EMAIL>","fax":"040 ********","phone_business":"040 ********","phone_mobile":"","homepage":"http:\\/\\/","address":"Holzdamm 53 - 20099 Hamburg","department":""}]}');
    cy.mockPwResponse('get', '/api/tags/replace', '{"status":"success","data":{"datum":{"type":"text","value":"10.12.1815"}}}');
    cy.mockPwResponse('get', 'api/stammdaten/gesellschaft/956/adresse', '{"status":"success","data":{"address":{"id":"1565","street":"An den Treptowers","nr":"3","zip":"12345","city":"Berlin","lat":null,"long":null,"last_edit_user_id":null,"last_edit_datetime":"2017-11-16 09:36:25","addition":"","country_id":"65"},"country":{"id":"65","num":"276","domain":".de","name":"Deutschland","countries":"Germany","countries_long":"Federal Republic of Germany","iso":"DE","ioc":"GER","ort":"Berlin","kont":"eur","inno_id":"1","nationality_name_ger":"deutsch"}}}');

    cy.fixture<VorgangResource>('vorlagen/brief')
      .then((vorlage) => {
        cy.request<Document<VorlageResource>>('POST', '/api/vorlagen', { data: vorlage })
          .then((response) => {
            expect(response.status).to.eq(201);
            expect(response).to.have.property('body');
          });
      });

    cy.intercept('/api/*').as('api');

    cy.visit('/');
    cy.get('button').contains('Vorgang anlegen').first().click();

    /* basis-informationen */

    // row 1
    cy.getBySel('step1__vorgangsart__brief').click();

    /* check mandatory fields */
    cy.intercept({
      method: 'POST',
      url: '/api/vorgaenge',
    }).as('vorgangApiCall');

    cy.getBySel('vorgang-anlegen__modal').find('button').contains('Zur Vorschau').first().click();

    cy.wait('@vorgangApiCall').then((interception) => {
      assert.equal(interception.response?.statusCode, 422);
    });

    cy.getBySel('basis-info__vorgangstyp__select').contains('Vorgangstyp muss ausgefüllt werden.');
    cy.getBySel('step1__address__name').contains('Name muss ausgefüllt werden.');
    cy.getBySel('step1__address__adresszeile1').contains('Adresszeile 1 muss ausgefüllt werden.');
    cy.getBySel('step1__address__plz').contains('PLZ muss ausgefüllt werden.');
    cy.getBySel('step1__address__stadt').contains('Stadt muss ausgefüllt werden.');
    cy.getBySel('vorgang-anlegen__nachricht__inhalt').contains('Inhalt muss ausgefüllt werden.');

    // back to row 1
    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Allgemeine Anfrage');

    cy.getBySel('basis-info__vorgangstitel')
      .find('input')
      .should('have.value', 'Allgemeine Anfrage');

    // VorgangTitel

    cy.getBySel('basis-info__vorgangstitel')
      .find('input')
      .clear()
      .type('Ich mag Züge');

    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .clear()
      .type('It took me 5 minutes to write that text, and all you say is "k"');

    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Adress-/Datenaktualisierung');

    cy.contains('Sie haben Änderungen an Titel, Betreff und/oder Inhalt durchgeführt und sind im Begriff eine Vorlage zu laden. Ihre Änderungen gehen damit unwiderruflich verloren.')
      .should('exist');

    cy.get('button').contains('Nein, Änderungen behalten').click();

    cy.getBySel('basis-info__vorgangstitel')
      .find('input')
      .should('have.value', 'Ich mag Züge');

    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .should('contain.text', 'It took me 5 minutes to write that text, and all you say is "k"');

    cy.setDateInDsDatepicker('basis-info__faellig-at', addMonths(new Date(), 2));

    // row 2
    cy.selectFromDsSelectBySearch('basis-info__kunde__select', 'Gabriela Bavarai');
    cy.selectFromDsSelect('basis-info__gesellschaft__select', 0);
    cy.selectFromDsSelect('basis-info__sparte__select', 0);

    /* empfaenger */
    cy.getBySel('step1__address__empfaenger__gesellschaft').click();

    cy.getBySel('step1__address__name').find('input').should('have.value', '1&1');
    cy.getBySel('step1__address__adresszeile1').find('input').should('have.value', 'An den Treptowers 3');
    cy.getBySel('step1__address__plz').find('input').should('have.value', '12345');
    cy.getBySel('step1__address__stadt').find('input').should('have.value', 'Berlin');

    // content
    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .type('I know what Im doing! Here, in this place, I can change things! I can stop Kanan from dying! #');
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'datum')
      .should('exist')
      .click();
    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .contains('[data-name=datum]', 'datum')
      .should('exist'); // date tag

    // zur vorschau
    cy.intercept({
      method: 'POST',
      url: '/api/vorgaenge',
    }).as('vorgangApiCall');
    cy.get('button').contains('Zur Vorschau').first().click();

    cy.wait('@vorgangApiCall').then((interception) => {
      assert.equal(interception.response?.statusCode, 201);
    });

    // check if pdf reader exists
    cy.getBySel('details-view__pdf-reader').should('exist');

    // vorgang anlegen
    cy.getBySel('vorgang-anlegen__modal')
      .find('button')
      .contains('Vorgang anlegen')
      .first()
      .click();

    // test created vorgang
    cy.getBySel('vorgang__titel')
      .should('exist')
      .should('contain.text', 'Ich mag Züge');

    cy.getBySel('timeline__list-item__icon')
      .first()
      .should('have.attr', 'data-icon', 'envelope');
    cy.getBySel('vorgang__timeline')
      .should('exist')
      .should('contain.text', 'Tester 1')
      .should('contain.text', 'I know what Im doing! Here, in this place, I can change things! I can stop Kanan from dying! 10.12.1815');

    cy.getBySel('vorgang__sidebar__kunde')
      .should('exist')
      .should('contain.text', 'Gabriela Bavarai');
    cy.getBySel('vorgang__sidebar__gesellschaft')
      .should('exist')
      .should('contain.text', '1&1');
  });
});
