<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\VertragAenderungenElement;
use Illuminate\Database\Eloquent\Factories\Factory;

class VertragAenderungenElementFactory extends Factory
{
    protected $model = VertragAenderungenElement::class;

    public function definition(): array
    {
        return [
            'author_id' => User::factory(),
            'element' => $this->faker->randomNumber(),
        ];
    }
}
