#!make

include .env
export $(shell sed 's/=.*//' .env)

setup: php-setup

clean:
	docker compose down
	docker compose rm -f -v
	docker builder prune

docker-up:
	docker compose up -d

docker-down:
	docker compose down

docker-restart:
	docker compose restart

lint: php-lint yarn-lint yarn-tsc

lint-fix: php-lint-fix yarn-lint-fix yarn-tsc

test: php-test yarn-test php-infection cypress-run

install: php-install yarn-install

migrate: php-migrate

rollback-one: php-rollback-one

## Frontend:
node:
	docker compose restart node

yarn:
	docker compose exec node yarn $(filter-out $@,$(MAKECMDGOALS))

yarn-install:
	docker compose exec node yarn install

yarn-lint:
	docker compose exec node yarn run lint

yarn-lint-fix:
	docker compose exec node yarn run lint:fix

yarn-tsc:
	docker compose exec node yarn run tsc

yarn-watch:
	docker compose logs -f --tail=5 node

yarn-test:
	docker compose exec node yarn run test

yarn-production:
	docker compose exec node yarn run production

## Backend:
php-setup:
	test -e .env || cp .env.example .env
	docker compose build php-fpm # First build PHP-FPM as it creates a image used by other images
	docker compose up -d --build --force-recreate
	make php-install
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off php artisan key:generate'
	make php-migrate-fresh

php-install:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off composer install'

php-lint:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off ./vendor/bin/phpcstd'
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off ./vendor/bin/deptrac analyse --config-file=deptrac_support.yaml'

php-lint-fix:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off ./vendor/bin/phpcstd --fix'

php-test:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off ./vendor/bin/phpunit'

php-test-unit:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off ./vendor/bin/phpunit --testsuite=unit'

php-test-feature:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off ./vendor/bin/phpunit --testsuite=feature'

php-infection:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=coverage ./vendor/bin/infection'

php-test-watch:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off ./vendor/bin/phpunit-watcher watch'

php-migrate: php-install cypress-delete-snapshot
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off php artisan migrate'

php-make-migration: php-install
    docker compose exec php-fpm bash -c 'XDEBUG_MODE=off php artisan make:migration '

php-migrate-fresh: php-install
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off php artisan migrate:fresh --seed'

php-route-list:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off php artisan route:list --except-path=telescope,vapor'

php-dependency-graph:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off php vendor/bin/deptrac analyze --formatter=graphviz --graphviz-dump-image=dependencies.png'

php-cache-clear:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off php artisan cache:clear'

php-artisan:
	docker compose exec php-fpm bash -c "XDEBUG_MODE=off php artisan $(filter-out $@,$(MAKECMDGOALS))"

dispatch-job:
	docker compose exec php-fpm bash -c "XDEBUG_MODE=off php artisan job:dispatch $(filter-out $@,$(MAKECMDGOALS))"

php-rollback-one:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off php artisan migrate:rollback --step=1'

phpcstd:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off ./vendor/bin/phpcstd --ci'

vapor:
	docker compose exec php-fpm php vendor/bin/vapor $(filter-out $@,$(MAKECMDGOALS))

composer:
	docker compose exec php-fpm bash -c "XDEBUG_MODE=off composer $(filter-out $@,$(MAKECMDGOALS))"

.PHONY: openapi
openapi:
	docker compose exec php-fpm bash -c 'XDEBUG_MODE=off ./vendor/bin/openapi app/Domain/ApiGateway -o openapi.json'

## Cypress:

cypress-run: docker-up cypress-install yarn-production cypress-run-fast

cypress-run-fast:
	yarn cypress run

cypress-run-spec: docker-up cypress-install yarn-production
	$(MAKE) cypress-run-spec-fast $(filter-out $@,$(MAKECMDGOALS))

cypress-run-spec-fast:
	yarn cypress run --spec "cypress/e2e/$(filter-out $@,$(MAKECMDGOALS)).cy.ts"

cypress-open: docker-up cypress-install yarn-production cypress-open-fast

cypress-open-fast:
	yarn cypress open --e2e

cypress-delete-snapshot:
	docker compose exec php-fpm php artisan snapshot:delete cypress-dump

cypress-install:
	yarn cypress install
	docker compose exec -T mysql mysql -u"root" -p"${DB_PASSWORD}" -e "drop database if exists cypress"
	docker compose exec -T mysql mysql -u"root" -p"${DB_PASSWORD}" -e "create database cypress"
	docker compose exec -T mysql mysql -u"root" -p"${DB_PASSWORD}" -e "GRANT ALL PRIVILEGES ON cypress.* TO '${DB_USERNAME}'@'%';"

## Database:

db-connect:
	docker compose exec mysql mysql -s -t -u"$(DB_USERNAME)" -p"$(DB_PASSWORD)" "$(DB_DATABASE)"

db-connect-production: SHELL:=/bin/bash
db-connect-production:
	ssh -f -N -L 3306:${DB_HOST_PRODUCTION}:3306 -i ${VAPOR_JUMPBOX_PRIVATE_KEY} ${VAPOR_JUMPBOX_USER}@${VAPOR_JUMPBOX_HOST}
	mysql --defaults-file=<(printf "[client]\nuser = %s\npassword = %s" "${DB_USER_PRODUCTION}" "${DB_PASSWORD_PRODUCTION}") -s -t -h 127.0.0.1 vapor

db-connect-staging: SHELL:=/bin/bash
db-connect-staging:
	ssh -f -N -L 3306:${DB_HOST_STAGING}:3306 -i ${VAPOR_JUMPBOX_PRIVATE_KEY} ${VAPOR_JUMPBOX_USER}@${VAPOR_JUMPBOX_HOST}
	mysql --defaults-file=<(printf "[client]\nuser = %s\npassword = %s" "${DB_USER_STAGING}" "${DB_PASSWORD_STAGING}") -s -t -h 127.0.0.1 vapor

db-connect-dev: SHELL:=/bin/bash
db-connect-dev:
	ssh -f -N -L 3306:${DB_HOST_DEV}:3306 -i ${VAPOR_JUMPBOX_PRIVATE_KEY} ${VAPOR_JUMPBOX_USER}@${VAPOR_JUMPBOX_HOST}
	mysql --defaults-file=<(printf "[client]\nuser = %s\npassword = %s" "${DB_USER_DEV}" "${DB_PASSWORD_DEV}") -s -t -h 127.0.0.1 vapor

