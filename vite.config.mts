/* eslint-disable import/no-extraneous-dependencies */

import * as path from 'path';

import vue from '@vitejs/plugin-vue';
import analyze from 'rollup-plugin-analyzer';
import ElementPlus from 'unplugin-element-plus/vite';
import { defineConfig, loadEnv } from 'vite';

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  const env = loadEnv(
    'mock',
    process.cwd(),
    '',
  );

  return {
    base: command === 'serve' ? '' : '/build/',
    publicDir: 'fake_dir_so_nothing_gets_copied',
    resolve: {
      dedupe: ['vue'],

      alias: {
        '@': path.resolve(__dirname, 'resources/js'),
      },
    },

    server: {
      port: parseInt(env.VITE_SERVER_PORT ?? '5173'),
      cors: {
        origin: /^https?:\/\/(?:(?:[^:]+\.)?localhost|127\.0\.0\.1|\[::1\]|vorgaenge\.demv\.internal)(?::\d+)?$/,
      },
    },

    optimizeDeps: {
      // optimizing interferes with yalc
      exclude: ['@demvsystems/design-components'],
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "@demv_systems/feu-element-plus-styles/src/scss/elementPlus.scss" as *;',
        },
      },
    },

    build: {
      manifest: true,
      outDir: 'public/build',
      rollupOptions: {
        input: 'resources/js/app.ts',
        plugins: [
          analyze({
            summaryOnly: true,
            limit: 10,
          }),
        ],
      },
    },

    plugins: [
      vue(),
      ElementPlus({
        useSource: true,
        defaultLocale: 'de',
      }),
    ],

    test: {
      globals: true,
      threads: false,
      environment: 'jsdom',
      include: ['resources/js/**/tests/**/*.spec.ts'],
      setupFiles: 'resources/js/tests/vitest.setup.ts',
      deps: {
        inline: ['element-plus'],
      },
    },
  };
});
