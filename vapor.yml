id: 20869
name: vorgaenge

ignore:
  - '.dockerignore'
  - '.editorconfig'
  - '.git*'
  - '.phpcstd.*'
  - '.phpunit*'
  - 'ecs.php'
  - 'node_modules'
  - 'phpcs.xml'
  - 'phpmd.xml'
  - 'phpstan.neon.dist'
  - 'public/**/*.map'
  - 'tests'
  - 'yarn-debug.log'

environments:
  production:
    memory: 2048
    cli-memory: 512
    runtime: 'php-8.2:al2'
    scheduler: true
    gateway-version: 2
    cache: vorgaenge-production
    database: vorgaenge-production-db-private
    network: vapor-private
    storage: demv-vorgaenge-production
    timeout: 60
    cli-timeout: 600
    queue-timeout: 900
    queues:
      - vorgaenge-production: 20
      - vorgaenge-production-background: 80
    domain:
      - vorgaenge.professional.works
      - vorgaenge.deutscher-maklerverbund.de
    build:
      - 'COMPOSER_MIRROR_PATH_REPOS=1 composer install --no-dev --classmap-authoritative'
      - 'php artisan event:cache'
      - 'php artisan config:cache'
      - 'yarn install --frozen-lockfile && yarn run tsc && yarn vite build --base=$(grep -oP "^ASSET_URL=\K.*$" .env)/build/ && rm -rf node_modules'
    deploy:
      - 'php artisan migrate --force'

  staging:
    memory: 2048
    cli-memory: 512
    runtime: 'php-8.2:al2'
    scheduler: true
    gateway-version: 2
    cache: vorgaenge-staging
    database: vorgaenge-staging-db-private
    network: vapor-private
    storage: demv-vorgaenge-staging
    timeout: 60
    cli-timeout: 600
    queues:
      - vorgaenge-staging: 4
      - vorgaenge-staging-background: 8
    queue-timeout: 900
    domain:
      - staging-vorgaenge.professional.works
      - staging-vorgaenge.deutscher-maklerverbund.de
    build:
      - 'COMPOSER_MIRROR_PATH_REPOS=1 composer install --no-dev --classmap-authoritative'
      - 'php artisan event:cache'
      - 'php artisan config:cache'
      - 'yarn install --frozen-lockfile && yarn run tsc && yarn vite build --base=$(grep -oP "^ASSET_URL=\K.*$" .env)/build/ && rm -rf node_modules'
    deploy:
      - 'php artisan migrate --force'

  development:
    memory: 2048
    cli-memory: 512
    runtime: docker
    scheduler: true
    gateway-version: 2
    cache: vorgaenge-development
    database: vorgaenge-development-db
    storage: demv-vorgaenge-development
    timeout: 60
    cli-timeout: 600
    queues:
      - vorgaenge-development: 4
      - vorgaenge-development-background: 8
    queue-timeout: 900
    domain:
      - dev-vorgaenge.professional.works
      - dev-vorgaenge.deutscher-maklerverbund.de
    build:
      - 'COMPOSER_MIRROR_PATH_REPOS=1 composer install --no-dev --classmap-authoritative'
      - 'php artisan event:cache'
      - 'php artisan config:cache'
      - 'yarn install --frozen-lockfile && yarn run tsc && yarn vite build --base=$(grep -oP "^ASSET_URL=\K.*$" .env)/build/ && rm -rf node_modules'
    deploy:
      - 'php artisan migrate --force'
