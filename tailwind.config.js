const defaultTheme = require('tailwindcss/defaultTheme');

module.exports = {
  theme: {
    screens: {
      ...defaultTheme.screens,
      'xl': '1281px',
    },
    extend: {
      fontWeight: {
        bolder: 'bolder',
      },
      fontSize: {
        'x-large': 'x-large',
        'large': 'large',
        'medium': 'medium',
      },
      lineClamp: {
        7: '7',
        8: '8',
        9: '9',
        12: '12',
      },
      maxHeight: {
        'max-h-100': '25rem',
      },
    },
  },

  plugins: [
    require('@demvsystems/design-components').tailwindPlugin,
  ],

  content: [
    './node_modules/@demvsystems/design-components/dist/design-components.mjs',
    './resources/**/*.{vue,js,ts}',
  ],
};
