{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "types": ["vite/client", "vitest/globals", "element-plus/global"], "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["./resources/js/*"]}, "noUncheckedIndexedAccess": true, "strictNullChecks": true, "isolatedModules": true}, "exclude": ["node_modules", "cypress.config.ts"], "include": ["vite.config.mts", "resources/js/**/*.ts", "resources/js/**/*.d.ts", "resources/js/**/*.vue", "**/.eslintrc.js", "*.ts", "*.js", "resources/js/__mocks__/axios.ts"]}