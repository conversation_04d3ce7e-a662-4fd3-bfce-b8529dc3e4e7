parameters:
	ignoreErrors:
		- "#^Dynamic call to static method Illuminate\\\\Database\\\\Eloquent#"

		-
			message: "#should be compatible with parameter \\$response \\(Illuminate\\\\Http\\\\JsonResponse\\)#"
			path: app/Support/JsonApi/Resources

		-
			message: "#Variable method call#"
			path: app/Relationships/BiDirectionalBelongsToMany.php

		-
			message: "#Variable property access#"
			path: app/Relationships/BiDirectionalBelongsToMany.php

		-
			message: "#^Variable method call on App\\\\Models\\\\Model\\.$#"
			count: 2
			path: app/Support/JsonApi/JsonApiQueryBuilder.php
